import axios, { AxiosResponse, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import {
  UserCreate,
  UserUpdate,
  UserPublic,
  UserProfileCreate,
  UserProfileUpdate,
  UserProfilePublic,
  PolicyCreate,
  PolicyUpdate,
  PolicyPublic,
  DocumentPublic,
  DocumentUpdate,
  Token,
  LoginRequest,
  PolicyStatus,
  PolicyProvider,
  PolicyType,
  ProfileStatus
} from '@/types/backend';

// Get API base URL from environment variables or use default
const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL || 'https://inerca-backend.fly.dev';

console.log('Using API base URL:', API_BASE_URL);

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor for adding token
api.interceptors.request.use(
  async (config) => {
    try {
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`);
      console.log('[API Request] Config:', {
        url: config.url,
        method: config.method,
        headers: { ...config.headers },
        data: config.data,
        params: config.params
      });

      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log('[API Request] Added auth token to request');
      } else {
        console.log('[API Request] No auth token found');
      }
      return config;
    } catch (error) {
      console.error('[API Request Error]:', error);
      return config;
    }
  },
  (error) => {
    console.error('[API Request Interceptor Error]:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors and logging
api.interceptors.response.use(
  (response) => {
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`);
    console.log('[API Response] Status:', response.status);
    console.log('[API Response] Data:', response.data);
    console.log('[API Response] Headers:', response.headers);
    return response;
  },
  async (error: AxiosError) => {
    console.error(`[API Error] ${error.config?.method?.toUpperCase()} ${error.config?.url}`);
    console.error('[API Error] Status:', error.response?.status);
    console.error('[API Error] Data:', error.response?.data);
    console.error('[API Error] Headers:', error.response?.headers);
    console.error('[API Error] Full Error:', error);

    // Handle 401 Unauthorized - token expired or invalid
    if (error.response?.status === 401) {
      console.log('[API Error] 401 Unauthorized - Clearing auth data');
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refreshToken');
      await AsyncStorage.removeItem('user');

      showToast(
        'error',
        'Session Expired',
        'Please log in again',
        { visibilityTime: 4000 }
      );
    } else {
      // Show error toast for other errors
      const responseData = error.response?.data as any;
      const errorMessage = responseData?.detail ||
                           responseData?.message ||
                           error.message ||
                           'An error occurred';

      console.log('[API Error] Showing error toast:', errorMessage);
      showToast(
        'error',
        'Error',
        typeof errorMessage === 'string' ? errorMessage : 'An error occurred',
        { visibilityTime: 4000 }
      );
    }

    return Promise.reject(error);
  }
);

// API service functions
export const apiService = {
  // Auth endpoints
  auth: {
    login: async (email: string, password: string): Promise<Token> => {
      console.log('[Auth] Login attempt for email:', email);
      const formData = new URLSearchParams();
      formData.append('username', email);
      formData.append('password', password);
      console.log('[Auth] Login form data prepared');

      const response = await api.post<Token>('/api/v1/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('[Auth] Login response received:', response.data);
      return response.data;
    },

    register: async (userData: UserCreate): Promise<UserPublic> => {
      console.log('[Auth] Register attempt with data:', userData);
      const response = await api.post<UserPublic>('/api/v1/register', userData);
      console.log('[Auth] Register response received:', response.data);
      return response.data;
    },

    googleLogin: async (): Promise<any> => {
      console.log('[Auth] Google login attempt');
      try {
        const response = await api.get('/api/v1/google/login');
        console.log('[Auth] Google login response received:', response.data);
        return response.data;
      } catch (error: any) {
        console.error('[Auth] Google login API error:', error);
        // Re-throw with more context
        if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
          const networkError = new Error('Network Error');
          networkError.name = 'NetworkError';
          throw networkError;
        }
        throw error;
      }
    },

    googleCallback: async (code: string): Promise<any> => {
      console.log('[Auth] Google callback with code');
      try {
        const response = await api.get('/api/v1/google/callback', {
          params: { code }
        });
        console.log('[Auth] Google callback response received:', response.data);
        return response.data;
      } catch (error: any) {
        console.error('[Auth] Google callback API error:', error);
        // Re-throw with more context
        if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
          const networkError = new Error('Network Error');
          networkError.name = 'NetworkError';
          throw networkError;
        }
        throw error;
      }
    },

    refreshToken: async (): Promise<Token> => {
      console.log('[Auth] Refresh token attempt');
      const response = await api.get<Token>('/api/v1/refresh-token');
      console.log('[Auth] Refresh token response received:', response.data);
      return response.data;
    },

    getCurrentUser: async (): Promise<UserPublic> => {
      console.log('[Auth] Get current user attempt');
      const response = await api.get<UserPublic>('/api/v1/user/me');
      console.log('[Auth] Get current user response received:', response.data);
      return response.data;
    },

    forgotPassword: async (email: string): Promise<any> => {
      console.log('[Auth] Forgot password attempt for email:', email);
      const response = await api.post('/api/v1/forgot-password', null, {
        params: { email }
      });
      console.log('[Auth] Forgot password response received:', response.data);
      return response.data;
    },

    resetPassword: async (resetData: { token: string; new_password: string; confirm_password: string }): Promise<any> => {
      console.log('[Auth] Reset password attempt with token');
      const response = await api.patch('/api/v1/reset-password', resetData);
      console.log('[Auth] Reset password response received:', response.data);
      return response.data;
    },

    updateAccount: async (userData: UserUpdate): Promise<UserPublic> => {
      console.log('[Auth] Update account attempt with data:', userData);
      const response = await api.patch<UserPublic>('/api/v1/update-account', userData);
      console.log('[Auth] Update account response received:', response.data);
      return response.data;
    },
  },

  // User profile endpoints
  userProfile: {
    getProfile: async (): Promise<UserProfilePublic> => {
      console.log('[Profile] Get profile attempt');
      const response = await api.get<UserProfilePublic>('/api/v1/user-profile');
      console.log('[Profile] Get profile response received:', response.data);
      return response.data;
    },

    createProfile: async (profileData: UserProfileCreate): Promise<UserProfilePublic> => {
      console.log('[Profile] Create profile attempt with data:', profileData);
      const response = await api.post<UserProfilePublic>('/api/v1/user-profile', profileData);
      console.log('[Profile] Create profile response received:', response.data);
      return response.data;
    },

    updateProfile: async (profileData: UserProfileUpdate): Promise<UserProfilePublic> => {
      console.log('[Profile] Update profile attempt with data:', profileData);
      const response = await api.patch<UserProfilePublic>('/api/v1/user-profile', profileData);
      console.log('[Profile] Update profile response received:', response.data);
      return response.data;
    },
  },

  // Document endpoints
  documents: {
    uploadDocument: async (file: any, policyId?: string): Promise<DocumentPublic> => {
      const formData = new FormData();
      formData.append('file', file);

      let url = '/api/v1/documents';
      if (policyId) {
        url += `?policy_id=${policyId}`;
      }

      const response = await api.post<DocumentPublic>(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },

    getAllDocuments: async (): Promise<DocumentPublic[]> => {
      const response = await api.get<DocumentPublic[]>('/api/v1/documents');
      return response.data;
    },

    downloadDocument: async (id: string): Promise<Blob> => {
      const response = await api.get(`/api/v1/documents/${id}/download`, {
        responseType: 'blob',
      });
      return response.data;
    },

    viewDocument: async (id: string): Promise<Blob> => {
      const response = await api.get(`/api/v1/documents/${id}/view`, {
        responseType: 'blob',
      });
      return response.data;
    },

    getPolicyDocuments: async (policyId: string): Promise<DocumentPublic[]> => {
      const response = await api.get<DocumentPublic[]>(`/api/v1/documents/${policyId}/policy`);
      return response.data;
    },

    deleteDocument: async (id: string): Promise<any> => {
      const response = await api.delete(`/api/v1/documents/${id}/delete`);
      return response.data;
    },
  },

  // Policy endpoints
  policies: {
    createPolicy: async (policyData: PolicyCreate): Promise<PolicyPublic> => {
      console.log('[Policy] Create policy attempt with data:', policyData);
      const response = await api.post<PolicyPublic>('/api/v1/policies', policyData);
      console.log('[Policy] Create policy response received:', response.data);
      return response.data;
    },

    getUserPolicies: async (filters?: {
      policy_status?: PolicyStatus,
      policy_provider?: PolicyProvider,
      policy_type?: PolicyType
    }): Promise<PolicyPublic[]> => {
      console.log('[Policy] Get user policies attempt with filters:', filters);
      const response = await api.get<PolicyPublic[]>('/api/v1/policies', { params: filters });
      console.log('[Policy] Get user policies response received:', response.data);
      return response.data;
    },

    getPolicyById: async (id: string): Promise<PolicyPublic> => {
      console.log('[Policy] Get policy by ID attempt for ID:', id);
      const response = await api.get<PolicyPublic>(`/api/v1/policies/${id}`);
      console.log('[Policy] Get policy by ID response received:', response.data);
      return response.data;
    },

    updatePolicy: async (id: string, policyData: PolicyUpdate): Promise<PolicyPublic> => {
      console.log('[Policy] Update policy attempt for ID:', id, 'with data:', policyData);
      const response = await api.patch<PolicyPublic>(`/api/v1/policies/${id}`, policyData);
      console.log('[Policy] Update policy response received:', response.data);
      return response.data;
    },
  },

  // Claims endpoints
  claims: {
    getClaims: async (filters?: {
      status?: string,
      type?: string,
      policy_id?: string
    }) => {
      const response = await api.get('/api/v1/claims', { params: filters });
      return response.data;
    },

    getClaimById: async (id: string) => {
      const response = await api.get(`/api/v1/claims/${id}`);
      return response.data;
    },

    createClaim: async (claimData: any) => {
      const response = await api.post('/api/v1/claims', claimData);
      return response.data;
    },

    updateClaim: async (id: string, claimData: any) => {
      const response = await api.patch(`/api/v1/claims/${id}`, claimData);
      return response.data;
    },

    uploadDocument: async (claimId: string, file: any, documentType: string) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentType);

      const response = await api.post(`/api/v1/claims/${claimId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },

    getClaimDocuments: async (claimId: string) => {
      const response = await api.get(`/api/v1/claims/${claimId}/documents`);
      return response.data;
    },

    submitClaim: async (claimId: string) => {
      const response = await api.post(`/api/v1/claims/${claimId}/submit`);
      return response.data;
    },

    withdrawClaim: async (claimId: string, reason: string) => {
      const response = await api.post(`/api/v1/claims/${claimId}/withdraw`, { reason });
      return response.data;
    },

    getClaimTimeline: async (claimId: string) => {
      const response = await api.get(`/api/v1/claims/${claimId}/timeline`);
      return response.data;
    },
  },

  // Payment endpoints - simplified for document upload only
  payments: {
    uploadPaymentProof: async (applicationId: string, proofData: any) => {
      const response = await api.post(`/api/v1/payments/applications/${applicationId}/proof`, proofData);
      return response.data;
    },

    getPaymentStatus: async (applicationId: string) => {
      const response = await api.get(`/api/v1/payments/applications/${applicationId}/status`);
      return response.data;
    },

    getPaymentHistory: async (filters?: any) => {
      const response = await api.get('/api/v1/payments/history', { params: filters });
      return response.data;
    },

    getPaymentById: async (paymentId: string) => {
      const response = await api.get(`/api/v1/payments/${paymentId}`);
      return response.data;
    },

    verifyPayment: async (paymentId: string, verificationData: any) => {
      const response = await api.post(`/api/v1/payments/${paymentId}/verify`, verificationData);
      return response.data;
    },
  },

  // Chat endpoints
  chat: {
    createSession: async (sessionData: any) => {
      const response = await api.post('/api/v1/session', sessionData);
      return response.data;
    },

    getSessions: async () => {
      const response = await api.get('/api/v1/conversations');
      return response.data;
    },

    getSession: async (sessionId: string) => {
      const response = await api.get(`/api/v1/session/${sessionId}`);
      return response.data;
    },

    sendMessage: async (sessionId: string, messageData: any) => {
      const response = await api.post(`/api/v1/session/${sessionId}/message`, messageData);
      return response.data;
    },

    markMessageAsRead: async (messageId: string) => {
      const response = await api.patch(`/api/v1/message/${messageId}/read`);
      return response.data;
    },
  },

  // Claims endpoints
  claims: {
    createClaim: async (claimData: any) => {
      const response = await api.post('/api/v1/claims', claimData);
      return response.data;
    },

    getClaims: async (filters?: { status?: string; policy_id?: string }) => {
      const response = await api.get('/api/v1/claims', { params: filters });
      return response.data;
    },

    getClaimById: async (claimId: string) => {
      const response = await api.get(`/api/v1/claims/${claimId}`);
      return response.data;
    },

    updateClaim: async (claimId: string, claimData: any) => {
      const response = await api.patch(`/api/v1/claims/${claimId}`, claimData);
      return response.data;
    },

    deleteClaim: async (claimId: string) => {
      const response = await api.delete(`/api/v1/claims/${claimId}`);
      return response.data;
    },

    submitClaim: async (claimId: string) => {
      const response = await api.post(`/api/v1/claims/${claimId}/submit`);
      return response.data;
    },

    uploadDocument: async (claimId: string, file: any, documentType: string) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentType);
      formData.append('claim_id', claimId);

      const response = await api.post('/api/v1/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },

    getClaimDocuments: async (claimId: string) => {
      const response = await api.get('/api/v1/documents', {
        params: { claim_id: claimId }
      });
      return response.data;
    },

    getClaimTimeline: async (claimId: string) => {
      // This would be a custom endpoint for claim timeline
      const response = await api.get(`/api/v1/claims/${claimId}/timeline`);
      return response.data;
    },

    withdrawClaim: async (claimId: string, reason: string) => {
      const response = await api.post(`/api/v1/claims/${claimId}/withdraw`, { reason });
      return response.data;
    },
  },

  // Payment endpoints
  payments: {
    createPayment: async (paymentData: any) => {
      const response = await api.post('/api/v1/payments', paymentData);
      return response.data;
    },

    getPayments: async (filters?: { policy_id?: string; status?: string }) => {
      const response = await api.get('/api/v1/payments', { params: filters });
      return response.data;
    },

    getPaymentById: async (paymentId: string) => {
      const response = await api.get(`/api/v1/payments/${paymentId}`);
      return response.data;
    },

    updatePayment: async (paymentId: string, paymentData: any) => {
      const response = await api.patch(`/api/v1/payments/${paymentId}`, paymentData);
      return response.data;
    },
  },

  // Application endpoints
  applications: {
    getApplications: async (filters?: {
      status?: string,
      type?: string
    }) => {
      const response = await api.get('/api/v1/applications', { params: filters });
      return response.data;
    },

    getApplicationById: async (id: string) => {
      const response = await api.get(`/api/v1/applications/${id}`);
      return response.data;
    },

    createApplication: async (applicationData: any) => {
      const response = await api.post('/api/v1/applications', applicationData);
      return response.data;
    },

    updateApplication: async (id: string, applicationData: any) => {
      const response = await api.patch(`/api/v1/applications/${id}`, applicationData);
      return response.data;
    },

    uploadApplicationDocument: async (applicationId: string, file: any, documentType: string) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentType);

      const response = await api.post(`/api/v1/applications/${applicationId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },
  },

  // Notification endpoints
  notifications: {
    getNotifications: async () => {
      const response = await api.get('/api/v1/notifications');
      return response.data;
    },

    markAsRead: async (id: string) => {
      const response = await api.patch(`/api/v1/notifications/${id}/read`);
      return response.data;
    },

    markAllAsRead: async () => {
      const response = await api.patch('/api/v1/notifications/read-all');
      return response.data;
    },

    deleteNotification: async (id: string) => {
      const response = await api.delete(`/api/v1/notifications/${id}`);
      return response.data;
    },
  },

  // Admin endpoints
  admin: {
    getPolicies: async (filters?: {
      policy_status?: PolicyStatus,
      policy_provider?: PolicyProvider,
      policy_type?: PolicyType,
      limit?: number,
      offset?: number
    }): Promise<PolicyPublic[]> => {
      const response = await api.get<PolicyPublic[]>('/api/v1/admin/policies', { params: filters });
      return response.data;
    },

    getProfiles: async (filters?: {
      profile_status?: ProfileStatus,
      limit?: number,
      offset?: number
    }): Promise<UserProfilePublic[]> => {
      const response = await api.get<UserProfilePublic[]>('/api/v1/admin/profiles', { params: filters });
      return response.data;
    },

    getPolicyDocuments: async (policyId: string): Promise<DocumentPublic[]> => {
      const response = await api.get<DocumentPublic[]>(`/api/v1/admin/documents/${policyId}/policy`);
      return response.data;
    },

    viewDocument: async (id: string): Promise<any> => {
      const response = await api.get(`/api/v1/admin/documents/${id}/view`);
      return response.data;
    },

    reviewPolicy: async (policyId: string, updateData: PolicyUpdate): Promise<any> => {
      const response = await api.patch(`/api/v1/admin/policy/review/${policyId}`, updateData);
      return response.data;
    },

    reviewProfile: async (profileId: string, updateData: UserProfileUpdate): Promise<any> => {
      const response = await api.patch(`/api/v1/admin/profiles/review/${profileId}`, updateData);
      return response.data;
    },

    reviewDocument: async (documentId: string, updateData: DocumentUpdate): Promise<any> => {
      const response = await api.patch(`/api/v1/admin/document/review/${documentId}`, updateData);
      return response.data;
    },
  },

  // Health check
  health: {
    check: async (): Promise<any> => {
      const response = await api.get('/api/v1/health');
      return response.data;
    },
  },
};

export default apiService;
