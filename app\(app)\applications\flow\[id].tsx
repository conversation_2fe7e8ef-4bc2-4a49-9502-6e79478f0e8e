import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useLocalSearchParams, router } from 'expo-router';
import Animated, { FadeInDown, FadeIn } from 'react-native-reanimated';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  CreditCard,
  Shield,
  ArrowLeft,
  Download,
  ExternalLink,
  Trash2,
} from 'lucide-react-native';

import { RootState, AppDispatch } from '@/store/store';
import {
  fetchApplicationFlows,
  setCurrentFlow,
  deleteApplicationFlow,
  ApplicationFlowStage,
  getStageDisplayInfo,
  updateFlowStage,
} from '@/store/applicationFlowSlice';
import { useTheme } from '@/context/ThemeContext';
import HeaderBar from '@/components/ui/HeaderBar';
import PolicyViewer from '@/components/policies/PolicyViewer';

const ApplicationFlowDetail: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { colors } = useTheme();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [refreshing, setRefreshing] = useState(false);

  const { flows, currentFlow, isLoading, error } = useSelector(
    (state: RootState) => state.applicationFlow
  );

  useEffect(() => {
    if (id) {
      dispatch(setCurrentFlow(id));
      if (flows.length === 0) {
        dispatch(fetchApplicationFlows());
      }
    }
  }, [dispatch, id, flows.length]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchApplicationFlows());
    setRefreshing(false);
  };

  const handleBack = () => {
    router.back();
  };

  const handleDeleteApplication = () => {
    if (!currentFlow) return;

    Alert.alert(
      'Delete Application',
      `Are you sure you want to delete your ${currentFlow.type} application? This will permanently remove the application and all associated documents.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            dispatch(deleteApplicationFlow(currentFlow.id));
            router.back(); // Navigate back after deletion
          },
        },
      ]
    );
  };

  const calculateProgress = (): number => {
    if (!currentFlow) return 0;
    const totalStages = 16;
    const completedStages = Object.values(currentFlow.stages).filter(
      stage => stage?.completed
    ).length;
    return Math.round((completedStages / totalStages) * 100);
  };

  const getAllStages = (): Array<{
    stage: ApplicationFlowStage;
    info: ReturnType<typeof getStageDisplayInfo>;
    data: any;
    completed: boolean;
    current: boolean;
  }> => {
    if (!currentFlow) return [];

    const allStages: ApplicationFlowStage[] = [
      'documents_submitted',
      'documents_under_review',
      'documents_verified',
      'quote_available',
      'quote_generated',
      'quote_accepted',
      'policy_bound',
      'invoice_generated',
      'invoice_delivered',
      'payment_uploaded',
      'payment_under_review',
      'payment_verified',
      'payment_reconciled',
      'policy_issued',
      'policy_under_review',
      'policy_dispatched',
    ];

    return allStages.map(stage => ({
      stage,
      info: getStageDisplayInfo(stage),
      data: currentFlow.stages[stage],
      completed: currentFlow.stages[stage]?.completed || false,
      current: currentFlow.currentStage === stage,
    }));
  };

  const getStageIcon = (stage: ApplicationFlowStage, completed: boolean, current: boolean) => {
    const iconColor = completed
      ? colors.success[500]
      : current
        ? colors.primary[500]
        : colors.textSecondary;

    const iconProps = { size: 20, color: iconColor };

    if (stage.includes('document')) {
      return <FileText {...iconProps} />;
    } else if (stage.includes('payment')) {
      return <CreditCard {...iconProps} />;
    } else if (stage.includes('policy')) {
      return <Shield {...iconProps} />;
    } else {
      return <Clock {...iconProps} />;
    }
  };

  const renderStageItem = (stageData: ReturnType<typeof getAllStages>[0], index: number) => {
    const { stage, info, data, completed, current } = stageData;

    return (
      <Animated.View
        key={stage}
        entering={FadeInDown.delay(index * 50).springify()}
        style={[
          styles.stageItem,
          { backgroundColor: colors.surface },
          current && { borderColor: colors.primary[500], borderWidth: 2 },
        ]}
      >
        <View style={styles.stageHeader}>
          <View style={styles.stageIconContainer}>
            {completed ? (
              <CheckCircle size={24} color={colors.success[500]} />
            ) : (
              getStageIcon(stage, completed, current)
            )}
          </View>
          <View style={styles.stageInfo}>
            <Text style={[
              styles.stageTitle,
              { color: completed ? colors.success[500] : current ? colors.primary[500] : colors.text }
            ]}>
              {info.title}
            </Text>
            <Text style={[styles.stageDescription, { color: colors.textSecondary }]}>
              {info.description}
            </Text>
          </View>
          <View style={styles.stageStatus}>
            {completed && (
              <Text style={[styles.completedText, { color: colors.success[500] }]}>
                Completed
              </Text>
            )}
            {current && !completed && (
              <Text style={[styles.currentText, { color: colors.primary[500] }]}>
                In Progress
              </Text>
            )}
          </View>
        </View>

        {data?.completedAt && (
          <View style={styles.stageDetails}>
            <Text style={[styles.completedDate, { color: colors.textSecondary }]}>
              Completed: {new Date(data.completedAt).toLocaleDateString()}
            </Text>
          </View>
        )}

        {data?.estimatedCompletion && !completed && (
          <View style={styles.stageDetails}>
            <Text style={[styles.estimatedDate, { color: colors.warning[500] }]}>
              Estimated: {new Date(data.estimatedCompletion).toLocaleDateString()}
            </Text>
          </View>
        )}

        {data?.notes && (
          <View style={styles.stageDetails}>
            <Text style={[styles.stageNotes, { color: colors.textSecondary }]}>
              {data.notes}
            </Text>
          </View>
        )}
      </Animated.View>
    );
  };

  const renderApplicationHeader = () => {
    if (!currentFlow) return null;

    const progress = calculateProgress();

    return (
      <Animated.View
        entering={FadeIn}
        style={[styles.headerCard, { backgroundColor: colors.surface }]}
      >
        <View style={styles.applicationHeader}>
          <View style={styles.applicationInfo}>
            <Text style={[styles.applicationType, { color: colors.text }]}>
              {currentFlow.type} Insurance
            </Text>
            <Text style={[styles.applicationId, { color: colors.textSecondary }]}>
              Application ID: {currentFlow.id.slice(-8).toUpperCase()}
            </Text>
            <Text style={[styles.clientName, { color: colors.text }]}>
              {currentFlow.clientInfo.firstName} {currentFlow.clientInfo.lastName}
            </Text>
          </View>
          <View style={styles.progressSection}>
            <Text style={[styles.progressText, { color: colors.primary[500] }]}>
              {progress}%
            </Text>
            <Text style={[styles.progressLabel, { color: colors.textSecondary }]}>
              Complete
            </Text>
          </View>
        </View>

        <View style={[styles.progressBarContainer, { backgroundColor: colors.border }]}>
          <Animated.View
            style={[
              styles.progressBar,
              { backgroundColor: colors.primary[500], width: `${progress}%` },
            ]}
            entering={FadeIn.delay(200)}
          />
        </View>

        <View style={styles.applicationDetails}>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Premium
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {currentFlow.currency} {currentFlow.premium.toLocaleString()}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              Cover Amount
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {currentFlow.currency} {currentFlow.coverAmount.toLocaleString()}
            </Text>
          </View>
          {currentFlow.policyNumber && (
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Policy Number
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {currentFlow.policyNumber}
              </Text>
            </View>
          )}
        </View>

        {progress === 100 && currentFlow.policyNumber && (
          <PolicyViewer
            policyNumber={currentFlow.policyNumber}
            policyType={currentFlow.type}
            userType="individual" // This should come from user context
            onDownload={() => {
              console.log('Policy downloaded for flow:', currentFlow.id);
            }}
          />
        )}
      </Animated.View>
    );
  };

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <HeaderBar
          title="Application Flow"
          onBack={handleBack}
          backgroundColor={colors.surface}
        />
        <View style={[styles.errorContainer, { backgroundColor: colors.surface }]}>
          <AlertCircle size={48} color={colors.error[500]} />
          <Text style={[styles.errorText, { color: colors.error[500] }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary[500] }]}
            onPress={() => dispatch(fetchApplicationFlows())}
          >
            <Text style={[styles.retryButtonText, { color: colors.white }]}>
              Retry
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (!currentFlow) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <HeaderBar
          title="Application Flow"
          onBack={handleBack}
          backgroundColor={colors.surface}
        />
        <View style={[styles.loadingContainer, { backgroundColor: colors.surface }]}>
          <Clock size={48} color={colors.textSecondary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading application details...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <HeaderBar
        title="Application Flow"
        onBack={handleBack}
        backgroundColor={colors.surface}
        rightComponent={
          currentFlow && (
            <TouchableOpacity
              style={styles.deleteHeaderButton}
              onPress={handleDeleteApplication}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Trash2 size={20} color={colors.error[500]} />
            </TouchableOpacity>
          )
        }
      />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderApplicationHeader()}

        <View style={styles.stagesContainer}>
          <Text style={[styles.stagesTitle, { color: colors.text }]}>
            Application Progress
          </Text>
          {getAllStages().map((stageData, index) =>
            renderStageItem(stageData, index)
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  deleteHeaderButton: {
    padding: 8,
    borderRadius: 6,
  },
  headerCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  applicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  applicationInfo: {
    flex: 1,
  },
  applicationType: {
    fontSize: 18,
    fontWeight: '600',
  },
  applicationId: {
    fontSize: 12,
    marginTop: 4,
  },
  clientName: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  progressSection: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  progressLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  applicationDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  detailItem: {
    flex: 1,
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  downloadButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  stagesContainer: {
    padding: 16,
  },
  stagesTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  stageItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  stageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stageIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stageInfo: {
    flex: 1,
  },
  stageTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  stageDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  stageStatus: {
    alignItems: 'flex-end',
  },
  completedText: {
    fontSize: 12,
    fontWeight: '500',
  },
  currentText: {
    fontSize: 12,
    fontWeight: '500',
  },
  stageDetails: {
    marginTop: 8,
    paddingLeft: 52,
  },
  completedDate: {
    fontSize: 11,
  },
  estimatedDate: {
    fontSize: 11,
  },
  stageNotes: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  errorContainer: {
    flex: 1,
    margin: 16,
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    margin: 16,
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default ApplicationFlowDetail;
