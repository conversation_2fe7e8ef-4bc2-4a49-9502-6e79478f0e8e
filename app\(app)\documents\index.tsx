import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, InteractionManager, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import { useDocumentUpload } from '@/context/DocumentUploadContext';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, FileText, Upload, FileCheck, FileBarChart } from 'lucide-react-native';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import PureDocumentUploader from '@/components/documents/PureDocumentUploader';
import DocumentListSection from '@/components/documents/DocumentListSection';
import DocumentViewer from '@/components/documents/DocumentViewer';
import { Document, DocumentCategory } from '@/components/documents/types';
import TabNavigation from '@/components/navigation/TabNavigation';
import BottomNavBar from '@/components/navigation/BottomNavBar';

const DocumentsScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { pendingDocuments, verifiedDocuments, clearPendingDocuments } = useDocumentUpload();
  const params = useLocalSearchParams<{ preselectedDocumentType?: string }>();

  // State for documents and document viewer - initialize with empty array
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [viewerVisible, setViewerVisible] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Use focus effect to refresh documents when the screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('[DocumentsScreen] Screen focused, refreshing documents');
      // Force a re-render when the screen is focused
      setForceUpdate(prev => prev + 1);

      // Always update documents from context on focus
      const allDocs = [...pendingDocuments, ...verifiedDocuments];
      console.log('[DocumentsScreen] Updating documents from context:', allDocs.length);
      setDocuments(allDocs);

      return () => {
        // Cleanup when screen is unfocused
        console.log('[DocumentsScreen] Screen unfocused');
      };
    }, [pendingDocuments, verifiedDocuments])
  );

  // Also update documents whenever pendingDocuments or verifiedDocuments change
  useEffect(() => {
    console.log('[DocumentsScreen] Context documents changed, updating local state');
    const allDocs = [...pendingDocuments, ...verifiedDocuments];
    setDocuments(allDocs);
  }, [pendingDocuments, verifiedDocuments]);

  console.log('[DocumentsScreen] Rendering with', documents.length, 'documents', 'Force update:', forceUpdate);

  // Check if we have a preselected document type from navigation params
  const preselectedDocumentType = params.preselectedDocumentType;

  // Handle document upload - this is now mostly handled by the DocumentUploadContext
  // This function is only called as a backup and for compatibility
  const handleDocumentUploaded = useCallback((document: Document) => {
    console.log('[DocumentsScreen] Document uploaded via direct callback:', document);

    // We don't need to do anything here as documents are now handled by the context
    // The useEffect above will process any pending documents from the context
  }, []);

  // Handle document view
  const handleViewDocument = (document: Document) => {
    console.log('[DocumentsScreen] Viewing document:', document);
    console.log('[DocumentsScreen] Document URI:', document.uri);
    console.log('[DocumentsScreen] Document fileUrl:', document.fileUrl);
    console.log('[DocumentsScreen] Document fileType:', document.fileType);
    console.log('[DocumentsScreen] Document isImage:', document.isImage);
    console.log('[DocumentsScreen] Document ID:', document.id);

    // Make a deep copy of the document to ensure we're not affected by reference issues
    const documentCopy = JSON.parse(JSON.stringify(document));

    // Ensure we have the correct properties set
    if (!documentCopy.fileType && documentCopy.isImage) {
      documentCopy.fileType = 'image';
    }

    // Set the document and show the viewer
    setSelectedDocument(documentCopy);
    setViewerVisible(true);
  };

  // Handle document delete
  const handleDeleteDocument = (document: Document) => {
    console.log('[DocumentsScreen] Deleting document:', document);
    Alert.alert(
      'Confirm Delete',
      `Are you sure you want to delete "${document.name}"?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setDocuments(prevDocuments =>
              prevDocuments.filter(doc => doc.id !== document.id)
            );

            // Show toast notification after deletion
            Toast.show({
              type: 'success',
              text1: 'Document Deleted',
              text2: `"${document.name}" has been deleted successfully`,
              visibilityTime: 4000,
              topOffset: 60,
            });
          },
        },
      ]
    );
  };

  // Handle document re-upload
  const handleReuploadDocument = (document: Document) => {
    console.log('[DocumentsScreen] Re-uploading document:', document);
    router.push('/documents/verification');
  };

  // Define tabs for navigation
  const tabs = [
    {
      id: 'upload',
      title: 'Upload Hub',
      icon: <Upload size={16} color={colors.primary[500]} />
    },
    {
      id: 'verification',
      title: 'Verification Status',
      icon: <FileCheck size={16} color={colors.textSecondary} />
    },
    {
      id: 'policies',
      title: 'Policy Repository',
      icon: <FileBarChart size={16} color={colors.textSecondary} />
    }
  ];

  // Handle tab change
  const handleTabChange = useCallback((tabId: string) => {
    if (tabId === 'upload') {
      // Stay on current page
      return;
    } else if (tabId === 'verification') {
      console.log('[DocumentsScreen] Navigating to verification page');
      router.push('/documents/verification');
    } else if (tabId === 'policies') {
      console.log('[DocumentsScreen] Navigating to policies page');
      router.push('/documents/policies');
    }
  }, []);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    scrollView: {
      flex: 1,
      marginBottom: Platform.OS === 'ios' ? 88 : 60, // Add margin for bottom nav bar
    },
    content: {
      padding: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    navigationCard: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderLeftWidth: 4,
      borderLeftColor: colors.primary[500],
    },
    navigationIcon: {
      marginRight: spacing.md,
      backgroundColor: `${colors.primary[500]}20`,
      padding: spacing.sm,
      borderRadius: borders.radius.md,
    },
    navigationTextContainer: {
      flex: 1,
    },
    navigationTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    navigationDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Documents</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <TabNavigation
            tabs={tabs}
            activeTab="upload"
            onTabChange={handleTabChange}
            scrollable={true}
            style={{ marginBottom: spacing.md }}
          />

          <Animated.View entering={FadeInDown.delay(100).springify()}>
            {/* Use PureDocumentUploader to prevent re-renders */}
            <PureDocumentUploader
              preselectedDocumentType={preselectedDocumentType}
              onDocumentUploaded={handleDocumentUploaded}
            />
          </Animated.View>

          <Animated.View entering={FadeInDown.delay(200).springify()}>
            <TouchableOpacity
              style={styles.navigationCard}
              onPress={() => router.push('/verification')}
            >
              <View style={styles.navigationIcon}>
                <FileCheck size={24} color={colors.primary[500]} />
              </View>
              <View style={styles.navigationTextContainer}>
                <Text style={styles.navigationTitle}>Verification Center</Text>
                <Text style={styles.navigationDescription}>
                  Upload and verify your personal or business documents
                </Text>
              </View>
              <ArrowLeft size={20} color={colors.textSecondary} style={{ transform: [{ rotate: '180deg' }] }} />
            </TouchableOpacity>
          </Animated.View>

          <DocumentListSection
            title="Recent Documents"
            documents={documents}
            onViewDocument={handleViewDocument}
            onDeleteDocument={handleDeleteDocument}
            onReuploadDocument={handleReuploadDocument}
            emptyStateMessage="You haven't uploaded any documents yet"
          />
        </View>
      </ScrollView>

      {selectedDocument && (
        <DocumentViewer
          document={selectedDocument}
          visible={viewerVisible}
          onClose={() => setViewerVisible(false)}
        />
      )}

      {/* Bottom Navigation Bar */}
      <BottomNavBar />
    </SafeAreaView>
  );
};

export default DocumentsScreen;
