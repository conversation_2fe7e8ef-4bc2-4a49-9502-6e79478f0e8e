import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TextInput,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { verifyOTP, enableBiometric } from '@/store/authSlice';
import { ArrowLeft } from 'lucide-react-native';
import * as LocalAuthentication from '@/mocks/expo-local-authentication';

export default function VerifyOTPScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  // Get auth state and actions from Redux store
  const dispatch = useAppDispatch();
  const { isLoading } = useAppSelector(state => state.auth);

  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<Array<TextInput | null>>([]);

  // Set up timer for OTP resend
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer(prevTimer => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  // Handle OTP input change
  const handleOtpChange = (text: string, index: number) => {
    if (text.length > 1) {
      text = text[0]; // Only take the first character if multiple are pasted
    }

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto-focus next input
    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace key
  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      // Focus previous input when backspace is pressed on an empty input
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = async () => {
    console.log('Verify OTP button pressed');

    const otpString = otp.join('');
    if (otpString.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter a valid 6-digit OTP code.');
      return;
    }

    try {
      const resultAction = await dispatch(verifyOTP({ otp: otpString }));

      if (verifyOTP.fulfilled.match(resultAction)) {
        console.log('OTP verification successful');

        // Ask user if they want to enable biometric authentication
        Alert.alert(
          'Enable Biometric Login',
          'Would you like to enable biometric login for faster access?',
          [
            {
              text: 'Not Now',
              onPress: () => {
                console.log('Biometric login skipped');
                router.replace('/(app)/(tabs)');
              },
              style: 'cancel',
            },
            {
              text: 'Enable',
              onPress: async () => {
                console.log('Enabling biometric login');
                const biometricResult = await dispatch(enableBiometric());

                if (enableBiometric.fulfilled.match(biometricResult)) {
                  console.log('Biometric login enabled');
                  Alert.alert('Success', 'Biometric login has been enabled.');
                } else {
                  console.log('Failed to enable biometric login');
                  Alert.alert('Error', 'Failed to enable biometric login. You can enable it later in settings.');
                }
                router.replace('/(app)/(tabs)');
              },
            },
          ],
          { cancelable: false }
        );
      } else if (verifyOTP.rejected.match(resultAction)) {
        Alert.alert('Verification Failed', resultAction.payload as string || 'Invalid OTP code. Please try again.');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      Alert.alert('Verification Error', 'An unexpected error occurred. Please try again.');
    }
  };

  // Handle OTP resend
  const handleResendOTP = async () => {
    if (!canResend) return;

    console.log('Resending OTP');
    setOtp(['', '', '', '', '', '']);
    setTimer(60);
    setCanResend(false);

    // Focus first input after resend
    inputRefs.current[0]?.focus();

    try {
      // Call the verifyOTP function with resendOtp flag
      const resultAction = await dispatch(verifyOTP({ otp: '', resendOtp: true }));

      if (verifyOTP.fulfilled.match(resultAction)) {
        console.log('OTP resent successfully');
        Alert.alert('OTP Sent', 'A new verification code has been sent to your phone.');
      } else if (verifyOTP.rejected.match(resultAction)) {
        console.log('Failed to resend OTP');
        Alert.alert('Resend Failed', resultAction.payload as string || 'Failed to resend verification code. Please try again.');
        // Reset the timer if resend fails
        setTimer(0);
        setCanResend(true);
      }
    } catch (error) {
      console.error('OTP resend error:', error);
      Alert.alert('Resend Error', 'An unexpected error occurred. Please try again.');
      // Reset the timer if resend fails
      setTimer(0);
      setCanResend(true);
    }
  };

  // Format timer display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    contentContainer: {
      flex: 1,
      padding: spacing.lg,
      justifyContent: 'center',
    },
    header: {
      marginBottom: spacing.xl,
    },
    backButton: {
      marginBottom: spacing.md,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes['2xl'],
      color: colors.text,
      marginBottom: spacing.xs,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      marginBottom: spacing.lg,
    },
    otpContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xl,
    },
    otpInput: {
      width: 45,
      height: 55,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      backgroundColor: colors.card,
      textAlign: 'center',
      fontSize: typography.sizes.xl,
      fontFamily: typography.fonts.bold,
      color: colors.text,
    },
    otpInputFocused: {
      borderColor: colors.primary[500],
      borderWidth: 2,
    },
    timerContainer: {
      alignItems: 'center',
      marginBottom: spacing.xl,
    },
    timerText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      marginBottom: spacing.sm,
    },
    resendButton: {
      padding: spacing.sm,
    },
    resendButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: canResend ? colors.primary[500] : colors.textSecondary,
    },
    verifyButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    verifyButtonText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={styles.title}>Verification</Text>
            <Text style={styles.subtitle}>
              We've sent a verification code to your phone. Please enter it below.
            </Text>
          </View>

          <View style={styles.otpContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref: TextInput | null) => { inputRefs.current[index] = ref }}
                style={[
                  styles.otpInput,
                  digit ? { borderColor: colors.primary[500] } : {}
                ]}
                value={digit}
                onChangeText={text => handleOtpChange(text, index)}
                onKeyPress={e => handleKeyPress(e, index)}
                keyboardType="number-pad"
                maxLength={1}
                selectTextOnFocus
              />
            ))}
          </View>

          <View style={styles.timerContainer}>
            <Text style={styles.timerText}>
              {canResend ? 'Didn\'t receive the code?' : `Resend code in ${formatTime(timer)}`}
            </Text>
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResendOTP}
              disabled={!canResend}
            >
              <Text style={styles.resendButtonText}>
                Resend Code
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.verifyButton}
            onPress={handleVerifyOTP}
            disabled={isLoading || otp.some(digit => !digit)}
          >
            {isLoading ? (
              <ActivityIndicator color={colors.white} />
            ) : (
              <Text style={styles.verifyButtonText}>Verify</Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
