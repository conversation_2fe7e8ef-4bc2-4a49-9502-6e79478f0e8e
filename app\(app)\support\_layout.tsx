import React from 'react';
import { Stack } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function SupportLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  console.log('[SupportLayout] Rendering support layout');

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: colors.background },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="faq" />
      <Stack.Screen name="contact" />
      <Stack.Screen name="chat" />
      <Stack.Screen name="chat/[id]" />
    </Stack>
  );
}
