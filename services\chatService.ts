import { apiService } from './api';
import {
  ChatSessionCreate,
  ChatSessionPublic,
  ChatMessageCreate,
  ChatMessagePublic,
  MessageParticipant
} from '@/types/backend';

export class ChatService {
  // Create a new chat session
  async createSession(sessionData: ChatSessionCreate): Promise<ChatSessionPublic> {
    try {
      console.log('[ChatService] Creating new chat session:', sessionData);
      const response = await apiService.chat.createSession(sessionData);
      console.log('[ChatService] Chat session created successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ChatService] Error creating chat session:', error);
      throw error;
    }
  }

  // Get all chat sessions for the current user
  async getSessions(): Promise<ChatSessionPublic[]> {
    try {
      console.log('[ChatService] Fetching chat sessions');
      const response = await apiService.chat.getSessions();
      console.log('[ChatService] Chat sessions fetched successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[ChatService] Error fetching chat sessions:', error);
      throw error;
    }
  }

  // Get a specific chat session with messages
  async getSession(sessionId: string): Promise<ChatSessionPublic> {
    try {
      console.log('[ChatService] Fetching chat session:', sessionId);
      const response = await apiService.chat.getSession(sessionId);
      console.log('[ChatService] Chat session fetched successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ChatService] Error fetching chat session:', error);
      throw error;
    }
  }

  // Send a message in a chat session
  async sendMessage(sessionId: string, messageData: ChatMessageCreate): Promise<ChatMessagePublic> {
    try {
      console.log('[ChatService] Sending message to session:', sessionId, messageData);
      const response = await apiService.chat.sendMessage(sessionId, messageData);
      console.log('[ChatService] Message sent successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ChatService] Error sending message:', error);
      throw error;
    }
  }

  // Mark a message as read
  async markMessageAsRead(messageId: string): Promise<void> {
    try {
      console.log('[ChatService] Marking message as read:', messageId);
      await apiService.chat.markMessageAsRead(messageId);
      console.log('[ChatService] Message marked as read successfully');
    } catch (error) {
      console.error('[ChatService] Error marking message as read:', error);
      throw error;
    }
  }

  // Create a support chat session
  async createSupportSession(title?: string): Promise<ChatSessionPublic> {
    const sessionData: ChatSessionCreate = {
      title: title || 'Support Request',
      participant_type: MessageParticipant.USER
    };
    return this.createSession(sessionData);
  }

  // Send a user message
  async sendUserMessage(sessionId: string, content: string): Promise<ChatMessagePublic> {
    const messageData: ChatMessageCreate = {
      content,
      participant: MessageParticipant.USER
    };
    return this.sendMessage(sessionId, messageData);
  }

  // Get unread message count for a session
  getUnreadCount(session: ChatSessionPublic): number {
    return session.messages.filter(message => 
      !message.is_read && message.participant === MessageParticipant.AGENT
    ).length;
  }

  // Get the last message in a session
  getLastMessage(session: ChatSessionPublic): ChatMessagePublic | null {
    if (session.messages.length === 0) return null;
    return session.messages[session.messages.length - 1];
  }

  // Format message timestamp
  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  // Check if user is typing (placeholder for future real-time implementation)
  async setTypingStatus(sessionId: string, isTyping: boolean): Promise<void> {
    // This would be implemented with WebSocket or Server-Sent Events
    console.log(`[ChatService] User typing status in session ${sessionId}:`, isTyping);
  }
}

export const chatService = new ChatService();
