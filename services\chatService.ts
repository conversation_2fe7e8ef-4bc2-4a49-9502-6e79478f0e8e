import { apiService } from './api';
import {
  ChatSessionCreate,
  ChatSessionPublic,
  ChatMessageCreate,
  ChatMessagePublic,
  MessageParticipant
} from '@/types/backend';

export class ChatService {
  // Create a new chat session
  async createSession(sessionData: ChatSessionCreate): Promise<ChatSessionPublic> {
    try {
      console.log('[ChatService] Creating new chat session:', sessionData);
      const response = await apiService.chat.createSession(sessionData);
      console.log('[ChatService] Chat session created successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ChatService] Error creating chat session:', error);
      throw error;
    }
  }

  // Get all chat sessions for the current user
  async getSessions(): Promise<ChatSessionPublic[]> {
    try {
      console.log('[ChatService] Fetching chat sessions');
      const response = await apiService.chat.getSessions();
      console.log('[ChatService] Chat sessions fetched successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[ChatService] Error fetching chat sessions:', error);
      throw error;
    }
  }

  // Get a specific chat session with messages
  async getSession(sessionId: string): Promise<ChatSessionPublic> {
    try {
      console.log('[ChatService] Fetching chat session:', sessionId);
      const response = await apiService.chat.getSession(sessionId);
      console.log('[ChatService] Chat session fetched successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ChatService] Error fetching chat session:', error);
      throw error;
    }
  }

  // Send a message in a chat session
  async sendMessage(sessionId: string, messageData: ChatMessageCreate): Promise<ChatMessagePublic> {
    try {
      console.log('[ChatService] Sending message to session:', sessionId, messageData);
      const response = await apiService.chat.sendMessage(sessionId, messageData);
      console.log('[ChatService] Message sent successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ChatService] Error sending message:', error);
      throw error;
    }
  }

  // Mark a message as read
  async markMessageAsRead(messageId: string): Promise<void> {
    try {
      console.log('[ChatService] Marking message as read:', messageId);
      await apiService.chat.markMessageAsRead(messageId);
      console.log('[ChatService] Message marked as read successfully');
    } catch (error) {
      console.error('[ChatService] Error marking message as read:', error);
      throw error;
    }
  }

  // Create a support chat session
  async createSupportSession(title?: string): Promise<ChatSessionPublic> {
    const sessionData: ChatSessionCreate = {
      title: title || 'Support Request',
      participant_type: MessageParticipant.USER
    };
    return this.createSession(sessionData);
  }

  // Send a user message
  async sendUserMessage(sessionId: string, content: string): Promise<ChatMessagePublic> {
    const messageData: ChatMessageCreate = {
      content,
      participant: MessageParticipant.USER
    };
    return this.sendMessage(sessionId, messageData);
  }

  // Send a quick message for common support topics
  async sendQuickMessage(sessionId: string, topic: string): Promise<ChatMessagePublic> {
    const quickMessages = {
      'claim_status': 'I would like to check the status of my insurance claim.',
      'policy_info': 'I need information about my insurance policy.',
      'payment_help': 'I need help with making a payment.',
      'document_upload': 'I need assistance with uploading documents.',
      'general_inquiry': 'I have a general inquiry about my insurance.',
      'technical_support': 'I need technical support with the app.',
      'billing_inquiry': 'I have a question about my billing.',
      'coverage_details': 'I need details about my coverage.',
    };

    const messageContent = quickMessages[topic as keyof typeof quickMessages] ||
                          'I need assistance with my insurance.';

    return this.sendMessage(sessionId, {
      content: messageContent,
      participant: MessageParticipant.USER
    });
  }

  // Get unread message count for a session
  getUnreadCount(session: ChatSessionPublic): number {
    return session.messages?.filter(message =>
      !message.is_read && message.participant === MessageParticipant.AGENT
    ).length || 0;
  }

  // Get total unread count across all sessions
  getTotalUnreadCount(sessions: ChatSessionPublic[]): number {
    return sessions.reduce((total, session) => total + this.getUnreadCount(session), 0);
  }

  // Get the last message in a session
  getLastMessage(session: ChatSessionPublic): ChatMessagePublic | null {
    if (!session.messages || session.messages.length === 0) return null;
    return session.messages[session.messages.length - 1];
  }

  // Format message timestamp
  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 168) { // Less than a week
      const days = Math.floor(diffInHours / 24);
      return `${days}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  // Format detailed timestamp for message details
  formatDetailedTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleString();
  }

  // Check if session has recent activity (within last 24 hours)
  hasRecentActivity(session: ChatSessionPublic): boolean {
    const lastMessage = this.getLastMessage(session);
    if (!lastMessage) return false;

    const lastMessageTime = new Date(lastMessage.created_at);
    const now = new Date();
    const diffInHours = (now.getTime() - lastMessageTime.getTime()) / (1000 * 60 * 60);

    return diffInHours < 24;
  }

  // Get session status based on last message and activity
  getSessionStatus(session: ChatSessionPublic): 'active' | 'waiting' | 'resolved' | 'inactive' {
    const lastMessage = this.getLastMessage(session);
    if (!lastMessage) return 'inactive';

    const unreadCount = this.getUnreadCount(session);
    const hasRecentActivity = this.hasRecentActivity(session);

    if (unreadCount > 0) return 'waiting';
    if (hasRecentActivity) return 'active';
    if (lastMessage.participant === MessageParticipant.AGENT) return 'resolved';
    return 'inactive';
  }

  // Mark all messages in a session as read
  async markSessionAsRead(sessionId: string): Promise<void> {
    try {
      console.log('[ChatService] Marking all messages in session as read:', sessionId);
      // In a real implementation, this would be a batch operation
      // For now, we'll rely on individual message marking
      console.log('[ChatService] Session marked as read (placeholder implementation)');
    } catch (error) {
      console.error('[ChatService] Error marking session as read:', error);
      throw error;
    }
  }

  // Search messages within a session
  searchMessages(session: ChatSessionPublic, query: string): ChatMessagePublic[] {
    if (!session.messages || !query.trim()) return [];

    const searchTerm = query.toLowerCase().trim();
    return session.messages.filter(message =>
      message.content.toLowerCase().includes(searchTerm)
    );
  }

  // Get message statistics for a session
  getSessionStats(session: ChatSessionPublic): {
    totalMessages: number;
    userMessages: number;
    agentMessages: number;
    unreadMessages: number;
    lastActivity: string | null;
  } {
    const messages = session.messages || [];
    const lastMessage = this.getLastMessage(session);

    return {
      totalMessages: messages.length,
      userMessages: messages.filter(m => m.participant === MessageParticipant.USER).length,
      agentMessages: messages.filter(m => m.participant === MessageParticipant.AGENT).length,
      unreadMessages: this.getUnreadCount(session),
      lastActivity: lastMessage ? this.formatDetailedTime(lastMessage.created_at) : null,
    };
  }

  // Check if user is typing (placeholder for future real-time implementation)
  async setTypingStatus(sessionId: string, isTyping: boolean): Promise<void> {
    // This would be implemented with WebSocket or Server-Sent Events
    console.log(`[ChatService] User typing status in session ${sessionId}:`, isTyping);
  }
}

export const chatService = new ChatService();
