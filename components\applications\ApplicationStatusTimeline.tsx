import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, ScrollView } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  Check, Clock, AlertCircle, FileText, CreditCard,
  FileCheck, ClipboardList, Shield, ChevronRight,
  Calendar, Info, ChevronDown, ExternalLink,
  RefreshCw, CheckCircle2, AlertTriangle, MoreHorizontal,
  ArrowRight, Hourglass, FileSearch, FileWarning, User
} from 'lucide-react-native';
import Animated, { FadeIn, FadeInDown, FadeInRight } from 'react-native-reanimated';
import { router } from 'expo-router';

interface TimelineAction {
  label: string;
  action: string;
  route?: string;
  params?: Record<string, string>;
  icon?: string;
  priority?: 'high' | 'medium' | 'low';
}

interface TimelineStep {
  id: string;
  title: string;
  description: string;
  date: string;
  status: 'completed' | 'current' | 'upcoming' | 'rejected' | 'warning' | 'info' | 'in_progress';
  icon?: string;
  actions?: TimelineAction[];
  details?: string;
  progress?: number; // Progress percentage (0-100)
  subSteps?: Array<{
    id: string;
    title: string;
    description?: string;
    status: 'completed' | 'current' | 'upcoming' | 'rejected' | 'warning' | 'in_progress';
    date?: string;
    icon?: string;
    details?: string;
  }>;
  estimatedCompletionDate?: string;
  assignedTo?: string;
}

interface ApplicationStatusTimelineProps {
  steps: TimelineStep[];
}

const ApplicationStatusTimeline: React.FC<ApplicationStatusTimelineProps> = ({
  steps,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [expandedStep, setExpandedStep] = useState<string | null>(null);

  // Toggle step expansion
  const toggleStepExpansion = (stepId: string) => {
    if (expandedStep === stepId) {
      setExpandedStep(null);
    } else {
      setExpandedStep(stepId);
    }
  };

  // Handle action button press
  const handleActionPress = (action: TimelineAction) => {
    if (action.route) {
      if (action.params) {
        router.push({
          pathname: action.route,
          params: action.params
        });
      } else {
        router.push(action.route);
      }
    }
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      marginVertical: spacing.md,
    },
    timelineItem: {
      flexDirection: 'row',
      marginBottom: spacing.md,
    },
    timelineItemLast: {
      marginBottom: 0,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    iconCompleted: {
      backgroundColor: colors.success[500],
    },
    iconCurrent: {
      backgroundColor: colors.primary[500],
    },
    iconInProgress: {
      backgroundColor: colors.info[500],
    },
    iconUpcoming: {
      backgroundColor: colors.neutral[400],
    },
    iconRejected: {
      backgroundColor: colors.error[500],
    },
    iconWarning: {
      backgroundColor: colors.warning[500],
    },
    iconInfo: {
      backgroundColor: colors.info[500],
    },
    lineContainer: {
      position: 'absolute',
      left: 20,
      top: 40,
      bottom: 0,
      width: 2,
    },
    line: {
      flex: 1,
      width: 2,
    },
    lineCompleted: {
      backgroundColor: colors.success[500],
    },
    lineCurrent: {
      backgroundColor: colors.primary[500],
    },
    lineInProgress: {
      backgroundColor: colors.info[500],
    },
    lineUpcoming: {
      backgroundColor: colors.neutral[300],
    },
    lineRejected: {
      backgroundColor: colors.error[500],
    },
    lineWarning: {
      backgroundColor: colors.warning[500],
    },
    lineInfo: {
      backgroundColor: colors.info[500],
    },
    content: {
      flex: 1,
      paddingBottom: spacing.md,
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    titleContainer: {
      flex: 1,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    description: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    date: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textTertiary,
    },
    detailsContainer: {
      marginTop: spacing.sm,
      padding: spacing.sm,
      backgroundColor: isDarkMode ? colors.neutral[800] : colors.neutral[100],
      borderRadius: borders.radius.md,
      marginBottom: spacing.sm,
    },
    detailsText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    actionsContainer: {
      marginTop: spacing.sm,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
      marginBottom: spacing.xs,
      alignSelf: 'flex-start',
    },
    actionButtonHighPriority: {
      backgroundColor: colors.error[500],
    },
    actionButtonMediumPriority: {
      backgroundColor: colors.warning[500],
    },
    actionButtonLowPriority: {
      backgroundColor: colors.info[500],
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
      marginRight: spacing.xs,
    },
    progressContainer: {
      marginTop: spacing.sm,
      width: '100%',
    },
    progressBackground: {
      height: 6,
      backgroundColor: isDarkMode ? colors.neutral[700] : colors.neutral[200],
      borderRadius: 3,
      overflow: 'hidden',
    },
    progressBar: {
      height: 6,
      borderRadius: 3,
    },
    progressText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: 4,
      textAlign: 'right',
    },
    subStepsContainer: {
      marginTop: spacing.sm,
      marginLeft: spacing.md,
      borderLeftWidth: 1,
      borderLeftColor: isDarkMode ? colors.neutral[700] : colors.neutral[300],
      paddingLeft: spacing.sm,
    },
    subStepItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: spacing.sm,
      paddingVertical: spacing.xs,
    },
    subStepIconContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.sm,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: {
          elevation: 1,
        },
      }),
    },
    subStepIconCompleted: {
      backgroundColor: colors.success[500],
    },
    subStepIconCurrent: {
      backgroundColor: colors.primary[500],
    },
    subStepIconInProgress: {
      backgroundColor: colors.info[500],
    },
    subStepIconUpcoming: {
      backgroundColor: colors.neutral[400],
    },
    subStepIconRejected: {
      backgroundColor: colors.error[500],
    },
    subStepIconWarning: {
      backgroundColor: colors.warning[500],
    },
    subStepContent: {
      flex: 1,
    },
    subStepTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: 2,
    },
    subStepDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginBottom: 2,
    },
    subStepDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textTertiary,
    },
    subStepDetailsContainer: {
      marginTop: spacing.xs,
      padding: spacing.xs,
      backgroundColor: isDarkMode ? colors.neutral[800] : colors.neutral[100],
      borderRadius: borders.radius.sm,
    },
    subStepDetailsText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
    },
    metadataContainer: {
      marginTop: spacing.xs,
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'center',
    },
    metadataItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: spacing.md,
      marginBottom: spacing.xs,
    },
    metadataText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginLeft: 4,
    },
    timelineHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
      paddingHorizontal: spacing.xs,
    },
    timelineHeaderText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    filterButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.xs,
      borderRadius: borders.radius.sm,
      backgroundColor: isDarkMode ? colors.neutral[800] : colors.neutral[100],
    },
    filterButtonText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginRight: spacing.xs,
    },
  });

  const getStatusIcon = (step: TimelineStep | TimelineStep['subSteps'][0], size: number) => {
    // First check if the step has a custom icon
    if (step.icon) {
      switch (step.icon) {
        case 'check-circle':
          return <CheckCircle2 size={size} color={colors.white} />;
        case 'file-text':
          return <FileText size={size} color={colors.white} />;
        case 'credit-card':
          return <CreditCard size={size} color={colors.white} />;
        case 'file-check':
          return <FileCheck size={size} color={colors.white} />;
        case 'clipboard':
          return <ClipboardList size={size} color={colors.white} />;
        case 'shield':
          return <Shield size={size} color={colors.white} />;
        case 'alert-circle':
          return <AlertCircle size={size} color={colors.white} />;
        case 'calendar':
          return <Calendar size={size} color={colors.white} />;
        case 'info':
          return <Info size={size} color={colors.white} />;
        case 'refresh':
          return <RefreshCw size={size} color={colors.white} />;
        case 'hourglass':
          return <Hourglass size={size} color={colors.white} />;
        case 'file-search':
          return <FileSearch size={size} color={colors.white} />;
        case 'file-warning':
          return <FileWarning size={size} color={colors.white} />;
        case 'alert-triangle':
          return <AlertTriangle size={size} color={colors.white} />;
        case 'more-horizontal':
          return <MoreHorizontal size={size} color={colors.white} />;
        case 'arrow-right':
          return <ArrowRight size={size} color={colors.white} />;
      }
    }

    // Fall back to status-based icons
    switch (step.status) {
      case 'completed':
        return <Check size={size} color={colors.white} />;
      case 'current':
        return <Clock size={size} color={colors.white} />;
      case 'in_progress':
        return <RefreshCw size={size} color={colors.white} />;
      case 'upcoming':
        return <Clock size={size} color={colors.white} />;
      case 'rejected':
        return <AlertCircle size={size} color={colors.white} />;
      case 'warning':
        return <AlertTriangle size={size} color={colors.white} />;
      case 'info':
        return <Info size={size} color={colors.white} />;
      default:
        return null;
    }
  };

  const getIconStyle = (status: TimelineStep['status']) => {
    switch (status) {
      case 'completed':
        return styles.iconCompleted;
      case 'current':
        return styles.iconCurrent;
      case 'in_progress':
        return styles.iconInProgress;
      case 'upcoming':
        return styles.iconUpcoming;
      case 'rejected':
        return styles.iconRejected;
      case 'warning':
        return styles.iconWarning;
      case 'info':
        return styles.iconInfo;
      default:
        return {};
    }
  };

  const getLineStyle = (status: TimelineStep['status']) => {
    switch (status) {
      case 'completed':
        return styles.lineCompleted;
      case 'current':
        return styles.lineCurrent;
      case 'in_progress':
        return styles.lineInProgress;
      case 'upcoming':
        return styles.lineUpcoming;
      case 'rejected':
        return styles.lineRejected;
      case 'warning':
        return styles.lineWarning;
      case 'info':
        return styles.lineInfo;
      default:
        return {};
    }
  };

  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');

  // Filter steps based on the selected filter
  const filteredSteps = steps.filter(step => {
    if (filter === 'all') return true;
    if (filter === 'active') return step.status === 'current' || step.status === 'in_progress';
    if (filter === 'completed') return step.status === 'completed';
    return true;
  });

  return (
    <Animated.View
      style={styles.container}
      entering={FadeIn.duration(500)}
    >
      <View style={styles.timelineHeader}>
        <Text style={styles.timelineHeaderText}>Application Timeline</Text>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => {
            // Cycle through filter options
            if (filter === 'all') setFilter('active');
            else if (filter === 'active') setFilter('completed');
            else setFilter('all');
          }}
        >
          <Text style={styles.filterButtonText}>
            {filter === 'all' ? 'All Steps' :
             filter === 'active' ? 'Active Steps' :
             'Completed Steps'}
          </Text>
          <RefreshCw size={14} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {filteredSteps.map((step, index) => {
        const isLast = index === filteredSteps.length - 1;
        const isExpanded = expandedStep === step.id;
        const hasActions = step.actions && step.actions.length > 0;
        const hasDetails = !!step.details;
        const hasSubSteps = step.subSteps && step.subSteps.length > 0;
        const hasMetadata = step.assignedTo || step.estimatedCompletionDate;
        const isExpandable = hasActions || hasDetails || hasSubSteps || hasMetadata;

        return (
          <Animated.View
            key={step.id}
            style={[
              styles.timelineItem,
              isLast && styles.timelineItemLast,
            ]}
            entering={FadeInDown.delay(index * 100).springify()}
          >
            <View style={[styles.iconContainer, getIconStyle(step.status)]}>
              {getStatusIcon(step, 20)}
            </View>

            {!isLast && (
              <View style={styles.lineContainer}>
                <View style={[styles.line, getLineStyle(step.status)]} />
              </View>
            )}

            <View style={styles.content}>
              <TouchableOpacity
                style={styles.headerContainer}
                onPress={() => isExpandable && toggleStepExpansion(step.id)}
                activeOpacity={isExpandable ? 0.7 : 1}
                disabled={!isExpandable}
              >
                <View style={styles.titleContainer}>
                  <Text style={styles.title}>{step.title}</Text>
                  <Text style={styles.description}>{step.description}</Text>
                  {step.date && <Text style={styles.date}>{step.date}</Text>}

                  {step.progress !== undefined && (
                    <View style={styles.progressContainer}>
                      <View style={styles.progressBackground}>
                        <View
                          style={[
                            styles.progressBar,
                            {
                              width: `${step.progress}%`,
                              backgroundColor:
                                step.status === 'completed' ? colors.success[500] :
                                step.status === 'current' ? colors.primary[500] :
                                step.status === 'in_progress' ? colors.info[500] :
                                step.status === 'warning' ? colors.warning[500] :
                                step.status === 'rejected' ? colors.error[500] :
                                colors.neutral[400]
                            }
                          ]}
                        />
                      </View>
                      <Text style={styles.progressText}>{step.progress}% Complete</Text>
                    </View>
                  )}
                </View>

                {isExpandable && (
                  <ChevronRight
                    size={20}
                    color={colors.textSecondary}
                    style={{
                      transform: [{ rotate: isExpanded ? '90deg' : '0deg' }],
                    }}
                  />
                )}
              </TouchableOpacity>

              {isExpanded && (
                <Animated.View entering={FadeInDown.duration(300)}>
                  {hasMetadata && (
                    <View style={styles.metadataContainer}>
                      {step.assignedTo && (
                        <View style={styles.metadataItem}>
                          <User size={14} color={colors.textSecondary} />
                          <Text style={styles.metadataText}>{step.assignedTo}</Text>
                        </View>
                      )}
                      {step.estimatedCompletionDate && (
                        <View style={styles.metadataItem}>
                          <Calendar size={14} color={colors.textSecondary} />
                          <Text style={styles.metadataText}>Est. completion: {step.estimatedCompletionDate}</Text>
                        </View>
                      )}
                    </View>
                  )}

                  {hasDetails && (
                    <View style={styles.detailsContainer}>
                      <Text style={styles.detailsText}>{step.details}</Text>
                    </View>
                  )}

                  {hasSubSteps && (
                    <View style={styles.subStepsContainer}>
                      {step.subSteps.map((subStep, subStepIndex) => (
                        <Animated.View
                          key={subStep.id}
                          style={styles.subStepItem}
                          entering={FadeInRight.delay(subStepIndex * 50).duration(300)}
                        >
                          <View style={[
                            styles.subStepIconContainer,
                            subStep.status === 'completed' ? styles.subStepIconCompleted :
                            subStep.status === 'current' ? styles.subStepIconCurrent :
                            subStep.status === 'in_progress' ? styles.subStepIconInProgress :
                            subStep.status === 'rejected' ? styles.subStepIconRejected :
                            subStep.status === 'warning' ? styles.subStepIconWarning :
                            styles.subStepIconUpcoming
                          ]}>
                            {getStatusIcon(subStep, 14)}
                          </View>
                          <View style={styles.subStepContent}>
                            <Text style={styles.subStepTitle}>{subStep.title}</Text>
                            {subStep.description && (
                              <Text style={styles.subStepDescription}>{subStep.description}</Text>
                            )}
                            {subStep.date && <Text style={styles.subStepDate}>{subStep.date}</Text>}

                            {subStep.details && (
                              <View style={styles.subStepDetailsContainer}>
                                <Text style={styles.subStepDetailsText}>{subStep.details}</Text>
                              </View>
                            )}
                          </View>
                        </Animated.View>
                      ))}
                    </View>
                  )}

                  {hasActions && (
                    <View style={styles.actionsContainer}>
                      {step.actions?.map((action, actionIndex) => (
                        <TouchableOpacity
                          key={`${step.id}-action-${actionIndex}`}
                          style={[
                            styles.actionButton,
                            action.priority === 'high' && styles.actionButtonHighPriority,
                            action.priority === 'medium' && styles.actionButtonMediumPriority,
                            action.priority === 'low' && styles.actionButtonLowPriority,
                          ]}
                          onPress={() => handleActionPress(action)}
                        >
                          <Text style={styles.actionButtonText}>{action.label}</Text>
                          {action.icon ? (
                            action.icon === 'external-link' ? <ExternalLink size={16} color={colors.white} /> :
                            action.icon === 'arrow-right' ? <ArrowRight size={16} color={colors.white} /> :
                            <ChevronRight size={16} color={colors.white} />
                          ) : (
                            action.route ?
                              <ExternalLink size={16} color={colors.white} /> :
                              <ChevronRight size={16} color={colors.white} />
                          )}
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </Animated.View>
              )}
            </View>
          </Animated.View>
        );
      })}
    </Animated.View>
  );
};

export default ApplicationStatusTimeline;
