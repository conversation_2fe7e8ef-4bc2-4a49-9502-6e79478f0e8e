import React from 'react';
import { StyleSheet, Text, TouchableOpacity, ActivityIndicator, View } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

type ButtonProps = {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: any;
  textStyle?: any;
};

export default function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
}: ButtonProps) {
  // Add error handling for theme context
  const themeContext = useTheme();
  const isDarkMode = themeContext?.isDarkMode ?? false;

  // Create theme with fallback
  const theme = createTheme(isDarkMode);
  const { colors, spacing, typography, borders } = theme;

  // Create styles with the current theme
  const styles = getStyles(colors, spacing, typography, borders);

  // Determine button style based on variant
  const getButtonStyle = () => {
    switch (variant) {
      case 'primary':
        return styles.primaryButton;
      case 'secondary':
        return styles.secondaryButton;
      case 'outline':
        return styles.outlineButton;
      case 'text':
        return styles.textButton;
      default:
        return styles.primaryButton;
    }
  };

  // Determine text style based on variant
  const getTextStyle = () => {
    switch (variant) {
      case 'primary':
        return styles.primaryButtonText;
      case 'secondary':
        return styles.secondaryButtonText;
      case 'outline':
        return styles.outlineButtonText;
      case 'text':
        return styles.textButtonText;
      default:
        return styles.primaryButtonText;
    }
  };

  // Determine size style
  const getSizeStyle = () => {
    switch (size) {
      case 'small':
        return styles.smallButton;
      case 'medium':
        return styles.mediumButton;
      case 'large':
        return styles.largeButton;
      default:
        return styles.mediumButton;
    }
  };

  // Determine text size style
  const getTextSizeStyle = () => {
    switch (size) {
      case 'small':
        return styles.smallButtonText;
      case 'medium':
        return styles.mediumButtonText;
      case 'large':
        return styles.largeButtonText;
      default:
        return styles.mediumButtonText;
    }
  };

  // Safe onPress handler with error handling
  const handlePress = () => {
    try {
      if (onPress) {
        onPress();
      }
    } catch (error) {
      console.error('Error in button onPress handler:', error);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        getSizeStyle(),
        fullWidth && styles.fullWidth,
        disabled && styles.disabledButton,
        style,
      ]}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={variant === 'outline' || variant === 'text' ? colors.primary[500] : colors.white} />
      ) : (
        <View style={styles.contentContainer}>
          {icon && iconPosition === 'left' && <View style={styles.leftIcon}>{icon}</View>}
          <Text
            style={[
              getTextStyle(),
              getTextSizeStyle(),
              disabled && styles.disabledButtonText,
              textStyle,
            ]}
          >
            {title}
          </Text>
          {icon && iconPosition === 'right' && <View style={styles.rightIcon}>{icon}</View>}
        </View>
      )}
    </TouchableOpacity>
  );
}

// Define styles with a function to access theme values
const getStyles = (colors, spacing, typography, borders) => StyleSheet.create({
  button: {
    borderRadius: borders.radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Variants
  primaryButton: {
    backgroundColor: colors.primary[500],
    borderWidth: 0,
  },
  secondaryButton: {
    backgroundColor: colors.secondary[500],
    borderWidth: 0,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary[500],
  },
  textButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  // Sizes
  smallButton: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    minHeight: 32,
  },
  mediumButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    minHeight: 40,
  },
  largeButton: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    minHeight: 48,
  },
  // Text styles
  primaryButtonText: {
    color: colors.white,
    fontFamily: typography.fonts.medium,
    textAlign: 'center',
  },
  secondaryButtonText: {
    color: colors.white,
    fontFamily: typography.fonts.medium,
    textAlign: 'center',
  },
  outlineButtonText: {
    color: colors.primary[500],
    fontFamily: typography.fonts.medium,
    textAlign: 'center',
  },
  textButtonText: {
    color: colors.primary[500],
    fontFamily: typography.fonts.medium,
    textAlign: 'center',
  },
  // Text sizes
  smallButtonText: {
    fontSize: typography.sizes.xs,
  },
  mediumButtonText: {
    fontSize: typography.sizes.sm,
  },
  largeButtonText: {
    fontSize: typography.sizes.md,
  },
  // States
  disabledButton: {
    backgroundColor: colors.neutral[300],
    borderColor: colors.neutral[300],
    opacity: 0.5,
  },
  disabledButtonText: {
    color: colors.neutral[600],
  },
  // Width
  fullWidth: {
    width: '100%',
  },
  // Icon positioning
  leftIcon: {
    marginRight: spacing.xs,
  },
  rightIcon: {
    marginLeft: spacing.xs,
  },
});
