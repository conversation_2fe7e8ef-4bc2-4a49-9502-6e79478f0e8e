import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Alert,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { router } from 'expo-router';
import HeaderBar from '@/components/ui/HeaderBar';
import { 
  FileText, 
  Shield, 
  Plus, 
  Check, 
  X, 
  ChevronDown, 
  ChevronUp,
  Users,
  Clock,
  Edit,
  Trash,
} from 'lucide-react-native';
import useLegalStore, { LegalDocumentType, LegalDocumentVersion } from '@/store/legalStore';
import { showToast } from '@/utils/toast';
import { format } from 'date-fns';
import { useAppSelector } from '@/store/hooks';

export default function LegalManagementScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  
  // Get user from auth state to check if admin
  const user = useAppSelector(state => state.auth.user);
  const isAdmin = user?.role === 'admin'; // In a real app, check for admin role
  
  // State
  const [activeTab, setActiveTab] = useState<LegalDocumentType>('terms');
  const [showAddForm, setShowAddForm] = useState(false);
  const [expandedVersionId, setExpandedVersionId] = useState<string | null>(null);
  
  // Form state
  const [newVersion, setNewVersion] = useState({
    version: '',
    publishDate: new Date().toISOString().split('T')[0],
    content: '',
    isActive: false,
  });
  
  // Get legal store methods
  const {
    fetchLegalDocuments,
    getDocumentVersions,
    getCurrentVersion,
    addNewVersion,
    setActiveVersion,
    getAcceptanceRate,
    isLoading,
  } = useLegalStore();
  
  // Load legal documents on mount
  useEffect(() => {
    fetchLegalDocuments();
  }, []);
  
  // Get versions for the active tab
  const versions = getDocumentVersions(activeTab);
  const currentVersion = getCurrentVersion(activeTab);
  
  // Handle adding a new version
  const handleAddVersion = async () => {
    if (!newVersion.version.trim()) {
      showToast('error', 'Validation Error', 'Version number is required');
      return;
    }
    
    if (!newVersion.content.trim()) {
      showToast('error', 'Validation Error', 'Content is required');
      return;
    }
    
    try {
      await addNewVersion(activeTab, newVersion);
      
      // Reset form
      setNewVersion({
        version: '',
        publishDate: new Date().toISOString().split('T')[0],
        content: '',
        isActive: false,
      });
      
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding version:', error);
    }
  };
  
  // Handle setting a version as active
  const handleSetActive = async (id: string) => {
    try {
      await setActiveVersion(activeTab, id);
    } catch (error) {
      console.error('Error setting active version:', error);
    }
  };
  
  // Toggle expanded version
  const toggleExpandVersion = (id: string) => {
    setExpandedVersionId(expandedVersionId === id ? null : id);
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
      padding: spacing.md,
    },
    tabsContainer: {
      flexDirection: 'row',
      marginBottom: spacing.md,
    },
    tab: {
      flex: 1,
      paddingVertical: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      borderBottomWidth: 2,
      borderBottomColor: colors.border,
      flexDirection: 'row',
    },
    activeTab: {
      borderBottomColor: colors.primary[500],
    },
    tabText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      marginLeft: spacing.xs,
    },
    activeTabText: {
      color: colors.primary[500],
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    addButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
    },
    addButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
      marginLeft: spacing.xs,
    },
    formContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.md,
    },
    formTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.md,
    },
    formGroup: {
      marginBottom: spacing.md,
    },
    formLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    input: {
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.sm,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    textArea: {
      height: 150,
      textAlignVertical: 'top',
    },
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    switchLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.md,
    },
    cancelButton: {
      backgroundColor: colors.error[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
      marginRight: spacing.md,
    },
    cancelButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
    },
    saveButton: {
      backgroundColor: colors.success[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
    },
    saveButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
    },
    versionsList: {
      marginTop: spacing.md,
    },
    versionItem: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      marginBottom: spacing.md,
      overflow: 'hidden',
    },
    versionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    versionHeaderLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    versionNumber: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
    },
    versionDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginLeft: spacing.sm,
    },
    versionActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    versionAction: {
      padding: spacing.xs,
      marginLeft: spacing.sm,
    },
    versionContent: {
      padding: spacing.md,
    },
    versionContentText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    versionStats: {
      flexDirection: 'row',
      padding: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    versionStat: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: spacing.lg,
    },
    versionStatText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginLeft: spacing.xs,
    },
    activeVersionBadge: {
      backgroundColor: colors.success[500],
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.sm,
      marginLeft: spacing.sm,
    },
    activeVersionText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.white,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    notAdminContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    notAdminText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      textAlign: 'center',
      marginTop: spacing.md,
    },
  });

  // If not admin, show access denied
  if (!isAdmin) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? 'light' : 'dark'} />
        <HeaderBar 
          title="Legal Document Management" 
          showBackButton 
          onBackPress={() => router.back()} 
        />
        <View style={styles.notAdminContainer}>
          <Shield size={48} color={colors.error[500]} />
          <Text style={styles.notAdminText}>
            You do not have permission to access this page. Administrator access is required.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <HeaderBar 
        title="Legal Document Management" 
        showBackButton 
        onBackPress={() => router.back()} 
      />
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      ) : (
        <ScrollView style={styles.content}>
          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'terms' && styles.activeTab]}
              onPress={() => setActiveTab('terms')}
            >
              <FileText size={20} color={activeTab === 'terms' ? colors.primary[500] : colors.textSecondary} />
              <Text style={[styles.tabText, activeTab === 'terms' && styles.activeTabText]}>
                Terms & Conditions
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'privacy' && styles.activeTab]}
              onPress={() => setActiveTab('privacy')}
            >
              <Shield size={20} color={activeTab === 'privacy' ? colors.primary[500] : colors.textSecondary} />
              <Text style={[styles.tabText, activeTab === 'privacy' && styles.activeTabText]}>
                Privacy Policy
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              {activeTab === 'terms' ? 'Terms & Conditions Versions' : 'Privacy Policy Versions'}
            </Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddForm(!showAddForm)}
            >
              {showAddForm ? (
                <>
                  <X size={16} color={colors.white} />
                  <Text style={styles.addButtonText}>Cancel</Text>
                </>
              ) : (
                <>
                  <Plus size={16} color={colors.white} />
                  <Text style={styles.addButtonText}>Add Version</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
          
          {showAddForm && (
            <View style={styles.formContainer}>
              <Text style={styles.formTitle}>
                Add New {activeTab === 'terms' ? 'Terms & Conditions' : 'Privacy Policy'} Version
              </Text>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Version Number</Text>
                <TextInput
                  style={styles.input}
                  value={newVersion.version}
                  onChangeText={(text) => setNewVersion({ ...newVersion, version: text })}
                  placeholder="e.g. 1.0, 2.1"
                  placeholderTextColor={colors.textSecondary}
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Publish Date</Text>
                <TextInput
                  style={styles.input}
                  value={newVersion.publishDate}
                  onChangeText={(text) => setNewVersion({ ...newVersion, publishDate: text })}
                  placeholder="YYYY-MM-DD"
                  placeholderTextColor={colors.textSecondary}
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Content</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={newVersion.content}
                  onChangeText={(text) => setNewVersion({ ...newVersion, content: text })}
                  placeholder="Enter the full content of the document..."
                  placeholderTextColor={colors.textSecondary}
                  multiline
                  numberOfLines={10}
                />
              </View>
              
              <View style={styles.formGroup}>
                <View style={styles.switchContainer}>
                  <Text style={styles.switchLabel}>Make Active Version</Text>
                  <Switch
                    value={newVersion.isActive}
                    onValueChange={(value) => setNewVersion({ ...newVersion, isActive: value })}
                    trackColor={{ false: colors.border, true: colors.primary[500] }}
                    thumbColor={colors.white}
                  />
                </View>
              </View>
              
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowAddForm(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={handleAddVersion}
                >
                  <Text style={styles.saveButtonText}>Save Version</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
          
          <View style={styles.versionsList}>
            {versions.map((version) => (
              <View key={version.id} style={styles.versionItem}>
                <TouchableOpacity
                  style={styles.versionHeader}
                  onPress={() => toggleExpandVersion(version.id)}
                >
                  <View style={styles.versionHeaderLeft}>
                    <FileText size={20} color={colors.primary[500]} />
                    <Text style={styles.versionNumber}>Version {version.version}</Text>
                    {version.isActive && (
                      <View style={styles.activeVersionBadge}>
                        <Text style={styles.activeVersionText}>Active</Text>
                      </View>
                    )}
                    <Text style={styles.versionDate}>
                      {format(new Date(version.publishDate), 'MMM d, yyyy')}
                    </Text>
                  </View>
                  <View style={styles.versionActions}>
                    {!version.isActive && (
                      <TouchableOpacity
                        style={styles.versionAction}
                        onPress={() => handleSetActive(version.id)}
                      >
                        <Check size={20} color={colors.success[500]} />
                      </TouchableOpacity>
                    )}
                    {expandedVersionId === version.id ? (
                      <ChevronUp size={20} color={colors.textSecondary} />
                    ) : (
                      <ChevronDown size={20} color={colors.textSecondary} />
                    )}
                  </View>
                </TouchableOpacity>
                
                {expandedVersionId === version.id && (
                  <>
                    <View style={styles.versionContent}>
                      <Text style={styles.versionContentText}>
                        {version.content.length > 500
                          ? `${version.content.substring(0, 500)}...`
                          : version.content}
                      </Text>
                    </View>
                    <View style={styles.versionStats}>
                      <View style={styles.versionStat}>
                        <Users size={16} color={colors.textSecondary} />
                        <Text style={styles.versionStatText}>
                          {getAcceptanceRate(activeTab, version.id)} Acceptances
                        </Text>
                      </View>
                      <View style={styles.versionStat}>
                        <Clock size={16} color={colors.textSecondary} />
                        <Text style={styles.versionStatText}>
                          {version.isActive ? 'Current Version' : 'Archived'}
                        </Text>
                      </View>
                    </View>
                  </>
                )}
              </View>
            ))}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}
