import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { 
  Claim, 
  ClaimStatus, 
  ClaimTimelineEvent, 
  CLAIM_TYPE_DISPLAY_NAMES 
} from '@/types/claim.types';
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  FileText, 
  DollarSign,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  HelpCircle,
  Send
} from 'lucide-react-native';
import { formatCurrency } from '@/utils/quoteCalculations';
import { format, parseISO } from 'date-fns';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  FadeIn,
  FadeInDown
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

interface ClaimTrackingViewProps {
  claim: Claim;
  onAddNote?: (note: string) => Promise<void>;
  onUploadDocument?: () => void;
  onViewDocument?: (documentId: string) => void;
}

const ClaimTrackingView: React.FC<ClaimTrackingViewProps> = ({
  claim,
  onAddNote,
  onUploadDocument,
  onViewDocument
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  
  const [expandedSections, setExpandedSections] = useState<string[]>(['status', 'timeline']);
  const [newNote, setNewNote] = useState<string>('');
  const [isSubmittingNote, setIsSubmittingNote] = useState<boolean>(false);
  
  const windowWidth = Dimensions.get('window').width;
  
  // Toggle section expansion
  const toggleSection = (section: string) => {
    if (expandedSections.includes(section)) {
      setExpandedSections(expandedSections.filter(s => s !== section));
    } else {
      setExpandedSections([...expandedSections, section]);
    }
  };
  
  // Get status color
  const getStatusColor = (status: ClaimStatus): string => {
    switch (status) {
      case 'draft':
        return colors.textSecondary;
      case 'submitted':
        return colors.info[500];
      case 'under_review':
        return colors.warning[500];
      case 'additional_info':
        return colors.warning[700];
      case 'approved':
        return colors.success[500];
      case 'partially_approved':
        return colors.success[300];
      case 'rejected':
        return colors.error[500];
      case 'paid':
        return colors.success[700];
      case 'closed':
        return colors.textSecondary;
      default:
        return colors.textSecondary;
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: ClaimStatus) => {
    const color = getStatusColor(status);
    
    switch (status) {
      case 'draft':
        return <FileText size={24} color={color} />;
      case 'submitted':
        return <Clock size={24} color={color} />;
      case 'under_review':
        return <HelpCircle size={24} color={color} />;
      case 'additional_info':
        return <AlertCircle size={24} color={color} />;
      case 'approved':
        return <CheckCircle size={24} color={color} />;
      case 'partially_approved':
        return <CheckCircle size={24} color={color} />;
      case 'rejected':
        return <XCircle size={24} color={color} />;
      case 'paid':
        return <DollarSign size={24} color={color} />;
      case 'closed':
        return <CheckCircle size={24} color={color} />;
      default:
        return <Clock size={24} color={color} />;
    }
  };
  
  // Get status display name
  const getStatusDisplayName = (status: ClaimStatus): string => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'submitted':
        return 'Submitted';
      case 'under_review':
        return 'Under Review';
      case 'additional_info':
        return 'Additional Info Required';
      case 'approved':
        return 'Approved';
      case 'partially_approved':
        return 'Partially Approved';
      case 'rejected':
        return 'Rejected';
      case 'paid':
        return 'Paid';
      case 'closed':
        return 'Closed';
      default:
        return status.replace('_', ' ');
    }
  };
  
  // Format date
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), 'dd MMM yyyy');
    } catch (error) {
      return dateString;
    }
  };
  
  // Handle adding a note
  const handleAddNote = async () => {
    if (!newNote.trim() || !onAddNote) return;
    
    setIsSubmittingNote(true);
    
    try {
      await onAddNote(newNote);
      setNewNote('');
    } catch (error) {
      console.error('Error adding note:', error);
    } finally {
      setIsSubmittingNote(false);
    }
  };
  
  // Calculate progress percentage
  const calculateProgress = (): number => {
    const statusOrder: ClaimStatus[] = [
      'draft',
      'submitted',
      'under_review',
      'additional_info',
      'approved',
      'partially_approved',
      'rejected',
      'paid',
      'closed'
    ];
    
    const currentIndex = statusOrder.indexOf(claim.status);
    
    // If rejected, return 100% (process is complete)
    if (claim.status === 'rejected') return 100;
    
    // For other statuses, calculate percentage based on position in flow
    // Exclude 'rejected' from calculation
    const totalSteps = statusOrder.filter(s => s !== 'rejected').length;
    const currentStep = currentIndex >= 0 ? currentIndex + 1 : 1;
    
    return Math.min(Math.round((currentStep / totalSteps) * 100), 100);
  };
  
  const progressPercentage = calculateProgress();
  
  return (
    <ScrollView style={styles.container}>
      {/* Claim Header */}
      <View style={styles.claimHeader}>
        <View style={styles.claimTypeContainer}>
          <Text style={styles.claimType}>
            {CLAIM_TYPE_DISPLAY_NAMES[claim.type]}
          </Text>
          <Text style={styles.claimReference}>
            Ref: {claim.reference}
          </Text>
        </View>
        <View style={styles.claimAmountContainer}>
          <Text style={styles.claimAmountLabel}>Claim Amount</Text>
          <Text style={styles.claimAmount}>
            {formatCurrency(claim.claimAmount, claim.currency)}
          </Text>
          {claim.approvedAmount !== undefined && (
            <Text style={styles.approvedAmount}>
              Approved: {formatCurrency(claim.approvedAmount, claim.currency)}
            </Text>
          )}
        </View>
      </View>
      
      {/* Status Section */}
      <TouchableOpacity 
        style={styles.sectionHeader}
        onPress={() => toggleSection('status')}
      >
        <Text style={styles.sectionTitle}>Claim Status</Text>
        {expandedSections.includes('status') ? (
          <ChevronUp size={20} color={colors.text} />
        ) : (
          <ChevronDown size={20} color={colors.text} />
        )}
      </TouchableOpacity>
      
      {expandedSections.includes('status') && (
        <Animated.View 
          style={styles.sectionContent}
          entering={FadeIn.duration(300)}
        >
          <View style={styles.statusContainer}>
            <View style={styles.statusHeader}>
              {getStatusIcon(claim.status)}
              <View style={styles.statusTextContainer}>
                <Text style={[styles.statusText, { color: getStatusColor(claim.status) }]}>
                  {getStatusDisplayName(claim.status)}
                </Text>
                <Text style={styles.statusDate}>
                  Updated: {formatDate(claim.date)}
                </Text>
              </View>
            </View>
            
            {/* Progress Bar */}
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <View 
                  style={[
                    styles.progressBarFill, 
                    { 
                      width: `${progressPercentage}%`,
                      backgroundColor: getStatusColor(claim.status)
                    }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>{progressPercentage}% Complete</Text>
            </View>
            
            {/* Required Actions */}
            {claim.requiredActions && claim.requiredActions.length > 0 && (
              <View style={styles.requiredActionsContainer}>
                <Text style={styles.requiredActionsTitle}>Required Actions</Text>
                {claim.requiredActions.map((action, index) => (
                  <View 
                    key={action.id} 
                    style={[
                      styles.requiredActionItem,
                      action.completed && styles.completedActionItem
                    ]}
                  >
                    <View style={styles.actionPriorityIndicator}>
                      <View 
                        style={[
                          styles.priorityDot,
                          { 
                            backgroundColor: action.priority === 'high' 
                              ? colors.error[500] 
                              : action.priority === 'medium'
                                ? colors.warning[500]
                                : colors.info[500]
                          }
                        ]} 
                      />
                    </View>
                    <View style={styles.actionContent}>
                      <Text 
                        style={[
                          styles.actionDescription,
                          action.completed && styles.completedActionText
                        ]}
                      >
                        {action.description}
                      </Text>
                      {action.dueDate && (
                        <Text style={styles.actionDueDate}>
                          Due by: {formatDate(action.dueDate)}
                        </Text>
                      )}
                    </View>
                    {action.completed ? (
                      <CheckCircle size={20} color={colors.success[500]} />
                    ) : (
                      <TouchableOpacity 
                        style={styles.actionButton}
                        onPress={() => {
                          // Handle action based on description
                          if (action.description.toLowerCase().includes('document')) {
                            onUploadDocument && onUploadDocument();
                          }
                        }}
                      >
                        <Text style={styles.actionButtonText}>Take Action</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                ))}
              </View>
            )}
          </View>
        </Animated.View>
      )}
      
      {/* Timeline Section */}
      <TouchableOpacity 
        style={styles.sectionHeader}
        onPress={() => toggleSection('timeline')}
      >
        <Text style={styles.sectionTitle}>Claim Timeline</Text>
        {expandedSections.includes('timeline') ? (
          <ChevronUp size={20} color={colors.text} />
        ) : (
          <ChevronDown size={20} color={colors.text} />
        )}
      </TouchableOpacity>
      
      {expandedSections.includes('timeline') && (
        <Animated.View 
          style={styles.sectionContent}
          entering={FadeIn.duration(300)}
        >
          <View style={styles.timelineContainer}>
            {claim.timeline.map((event, index) => (
              <Animated.View 
                key={event.id}
                style={styles.timelineEvent}
                entering={FadeInDown.delay(index * 100).duration(300)}
              >
                <View style={styles.timelineIconContainer}>
                  <View 
                    style={[
                      styles.timelineIcon,
                      { 
                        backgroundColor: event.status === 'completed' 
                          ? colors.success[100] 
                          : event.status === 'current'
                            ? colors.info[100]
                            : colors.textSecondary + '20'
                      }
                    ]}
                  >
                    {event.status === 'completed' ? (
                      <CheckCircle size={20} color={colors.success[500]} />
                    ) : event.status === 'current' ? (
                      <Clock size={20} color={colors.info[500]} />
                    ) : (
                      <Clock size={20} color={colors.textSecondary} />
                    )}
                  </View>
                  {index < claim.timeline.length - 1 && (
                    <View 
                      style={[
                        styles.timelineConnector,
                        { 
                          backgroundColor: event.status === 'completed' 
                            ? colors.success[500] 
                            : colors.textSecondary + '40'
                        }
                      ]} 
                    />
                  )}
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineTitle}>{event.title}</Text>
                  <Text style={styles.timelineDescription}>{event.description}</Text>
                  {event.date && (
                    <Text style={styles.timelineDate}>{formatDate(event.date)}</Text>
                  )}
                </View>
              </Animated.View>
            ))}
          </View>
        </Animated.View>
      )}
      
      {/* Documents Section */}
      <TouchableOpacity 
        style={styles.sectionHeader}
        onPress={() => toggleSection('documents')}
      >
        <Text style={styles.sectionTitle}>Claim Documents</Text>
        {expandedSections.includes('documents') ? (
          <ChevronUp size={20} color={colors.text} />
        ) : (
          <ChevronDown size={20} color={colors.text} />
        )}
      </TouchableOpacity>
      
      {expandedSections.includes('documents') && (
        <Animated.View 
          style={styles.sectionContent}
          entering={FadeIn.duration(300)}
        >
          <View style={styles.documentsContainer}>
            {claim.documents.length === 0 ? (
              <View style={styles.noDocumentsContainer}>
                <FileText size={40} color={colors.textSecondary} />
                <Text style={styles.noDocumentsText}>No documents uploaded yet</Text>
                <TouchableOpacity 
                  style={styles.uploadDocumentButton}
                  onPress={() => onUploadDocument && onUploadDocument()}
                >
                  <Text style={styles.uploadDocumentButtonText}>Upload Document</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <>
                <View style={styles.documentsList}>
                  {claim.documents.map((document, index) => (
                    <TouchableOpacity 
                      key={document.id}
                      style={styles.documentItem}
                      onPress={() => onViewDocument && onViewDocument(document.id)}
                    >
                      <FileText size={24} color={colors.primary[500]} />
                      <View style={styles.documentInfo}>
                        <Text style={styles.documentName}>{document.name}</Text>
                        <Text style={styles.documentDate}>
                          Uploaded: {formatDate(document.date)}
                        </Text>
                      </View>
                      <View 
                        style={[
                          styles.documentStatus,
                          { 
                            backgroundColor: document.status === 'verified' 
                              ? colors.success[100] 
                              : document.status === 'rejected'
                                ? colors.error[100]
                                : colors.warning[100]
                          }
                        ]}
                      >
                        <Text 
                          style={[
                            styles.documentStatusText,
                            { 
                              color: document.status === 'verified' 
                                ? colors.success[700] 
                                : document.status === 'rejected'
                                  ? colors.error[700]
                                  : colors.warning[700]
                            }
                          ]}
                        >
                          {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
                <TouchableOpacity 
                  style={styles.addDocumentButton}
                  onPress={() => onUploadDocument && onUploadDocument()}
                >
                  <Text style={styles.addDocumentButtonText}>+ Add Document</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </Animated.View>
      )}
      
      {/* Notes Section */}
      <TouchableOpacity 
        style={styles.sectionHeader}
        onPress={() => toggleSection('notes')}
      >
        <Text style={styles.sectionTitle}>Notes & Communication</Text>
        {expandedSections.includes('notes') ? (
          <ChevronUp size={20} color={colors.text} />
        ) : (
          <ChevronDown size={20} color={colors.text} />
        )}
      </TouchableOpacity>
      
      {expandedSections.includes('notes') && (
        <Animated.View 
          style={styles.sectionContent}
          entering={FadeIn.duration(300)}
        >
          <View style={styles.notesContainer}>
            {(!claim.notes || claim.notes.length === 0) ? (
              <View style={styles.noNotesContainer}>
                <Text style={styles.noNotesText}>No notes or messages yet</Text>
              </View>
            ) : (
              <View style={styles.notesList}>
                {claim.notes.map((note, index) => (
                  <View key={index} style={styles.noteItem}>
                    <Text style={styles.noteText}>{note}</Text>
                  </View>
                ))}
              </View>
            )}
            
            {onAddNote && (
              <View style={styles.addNoteContainer}>
                <TextInput
                  style={styles.addNoteInput}
                  placeholder="Add a note or question..."
                  placeholderTextColor={colors.textSecondary}
                  value={newNote}
                  onChangeText={setNewNote}
                  multiline
                />
                <TouchableOpacity 
                  style={[
                    styles.addNoteButton,
                    (!newNote.trim() || isSubmittingNote) && { opacity: 0.5 }
                  ]}
                  onPress={handleAddNote}
                  disabled={!newNote.trim() || isSubmittingNote}
                >
                  {isSubmittingNote ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <Send size={20} color="#ffffff" />
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
        </Animated.View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  claimHeader: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  claimTypeContainer: {
    flex: 1,
  },
  claimType: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  claimReference: {
    fontSize: 14,
    color: '#757575',
  },
  claimAmountContainer: {
    alignItems: 'flex-end',
  },
  claimAmountLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 2,
  },
  claimAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  approvedAmount: {
    fontSize: 14,
    color: '#4caf50',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionContent: {
    marginBottom: 16,
  },
  statusContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginBottom: 8,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusTextContainer: {
    marginLeft: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusDate: {
    fontSize: 12,
    color: '#757575',
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#757575',
    marginTop: 4,
    textAlign: 'right',
  },
  requiredActionsContainer: {
    marginTop: 8,
  },
  requiredActionsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  requiredActionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginBottom: 8,
  },
  completedActionItem: {
    backgroundColor: '#e8f5e9',
  },
  actionPriorityIndicator: {
    marginRight: 12,
  },
  priorityDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  actionContent: {
    flex: 1,
  },
  actionDescription: {
    fontSize: 14,
    marginBottom: 2,
  },
  completedActionText: {
    textDecorationLine: 'line-through',
    color: '#757575',
  },
  actionDueDate: {
    fontSize: 12,
    color: '#757575',
  },
  actionButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  timelineContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  timelineEvent: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineIconContainer: {
    alignItems: 'center',
    width: 40,
  },
  timelineIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  timelineConnector: {
    width: 2,
    flex: 1,
    marginTop: 4,
    marginBottom: -8,
  },
  timelineContent: {
    flex: 1,
    paddingBottom: 16,
  },
  timelineTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  timelineDescription: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  timelineDate: {
    fontSize: 12,
    color: '#757575',
  },
  documentsContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  noDocumentsContainer: {
    alignItems: 'center',
    padding: 24,
  },
  noDocumentsText: {
    fontSize: 14,
    color: '#757575',
    marginTop: 8,
    marginBottom: 16,
  },
  uploadDocumentButton: {
    backgroundColor: '#6200ee',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  uploadDocumentButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  documentsList: {
    marginBottom: 16,
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginBottom: 8,
  },
  documentInfo: {
    flex: 1,
    marginLeft: 12,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  documentDate: {
    fontSize: 12,
    color: '#757575',
  },
  documentStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  documentStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  addDocumentButton: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  addDocumentButtonText: {
    color: '#6200ee',
    fontSize: 14,
    fontWeight: 'bold',
  },
  notesContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  noNotesContainer: {
    alignItems: 'center',
    padding: 24,
  },
  noNotesText: {
    fontSize: 14,
    color: '#757575',
  },
  notesList: {
    marginBottom: 16,
  },
  noteItem: {
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginBottom: 8,
  },
  noteText: {
    fontSize: 14,
  },
  addNoteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addNoteInput: {
    flex: 1,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    marginRight: 8,
    fontSize: 14,
  },
  addNoteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#6200ee',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ClaimTrackingView;
