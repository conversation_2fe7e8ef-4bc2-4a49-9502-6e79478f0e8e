import React, { useState, useEffect } from 'react';
import { View, Alert } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { Mail, Phone, User, MapPin, Briefcase, Calendar, DollarSign, Hash } from 'lucide-react-native';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField from '@/components/quotes/DynamicFormField';
import useQuoteStore from '@/store/quoteStore';
import { ClientInfo } from '@/types/quote.types';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function GeneralInfoScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const { quoteId } = useLocalSearchParams();

  // Get quote store functions
  const {
    getQuoteById,
    updateClientInfo,
    updateQuote,
    setCurrentQuote,
    currentQuote,
    isLoading
  } = useQuoteStore();

  // Form state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [occupation, setOccupation] = useState('');
  const [idNumber, setIdNumber] = useState('');
  const [paymentPreference, setPaymentPreference] = useState('annual');

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);

  // Load quote data
  useEffect(() => {
    if (quoteId) {
      const quote = getQuoteById(quoteId as string);
      if (quote) {
        setCurrentQuote(quote);

        // Populate form with client info if available
        if (quote.clientInfo) {
          setFirstName(quote.clientInfo.firstName || '');
          setLastName(quote.clientInfo.lastName || '');
          setEmail(quote.clientInfo.email || '');
          setPhone(quote.clientInfo.phone || '');
          setAddress(quote.clientInfo.address || '');
          setDateOfBirth(quote.clientInfo.dateOfBirth || '');
          setOccupation(quote.clientInfo.occupation || '');
          setIdNumber(quote.clientInfo.idNumber || '');

          // Set payment preference if available in additionalInfo
          if (quote.additionalInfo?.paymentPreference) {
            setPaymentPreference(quote.additionalInfo.paymentPreference);
          }
        }
      } else {
        Alert.alert('Error', 'Quote not found');
        router.back();
      }
    }
  }, [quoteId, getQuoteById, setCurrentQuote]);

  // Validate form
  useEffect(() => {
    validateForm();
  }, [firstName, lastName, email, phone, idNumber]);

  // Validate form fields
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate first name
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Validate last name
    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Validate email
    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Validate phone
    if (!phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[0-9]{10,15}$/.test(phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Validate ID number
    if (!idNumber.trim()) {
      newErrors.idNumber = 'National ID / Passport No. is required';
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  };

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  // Handle save
  const handleSave = async () => {
    if (!currentQuote) return;

    try {
      const clientInfo: ClientInfo = {
        firstName,
        lastName,
        email,
        phone,
        address,
        dateOfBirth,
        occupation,
        idNumber,
      };

      // Also update the payment preference in additionalInfo
      const additionalInfo = {
        ...(currentQuote.additionalInfo || {}),
        paymentPreference,
      };

      // Update both client info and additional info
      await updateClientInfo(clientInfo);
      await updateQuote({ additionalInfo });
    } catch (error) {
      console.error('Error saving client info:', error);
      Alert.alert('Error', 'Failed to save client information');
    }
  };

  // Handle next
  const handleNext = async () => {
    if (!currentQuote) {
      console.error('Cannot navigate: currentQuote is null');
      Alert.alert('Error', 'Quote information is missing. Please try again.');
      return;
    }

    try {
      console.log('Saving client info and navigating to quote form');
      console.log('Current quote details:', JSON.stringify({
        id: currentQuote.id,
        type: currentQuote.type,
        status: currentQuote.status
      }));

      // Save client info
      const clientInfo: ClientInfo = {
        firstName,
        lastName,
        email,
        phone,
        address,
        dateOfBirth,
        occupation,
        idNumber,
      };

      // Also update the payment preference in additionalInfo
      const additionalInfo = {
        ...(currentQuote.additionalInfo || {}),
        paymentPreference,
      };

      // Update both client info and additional info
      await updateClientInfo(clientInfo);
      await updateQuote({ additionalInfo });

      console.log('Client info saved, navigating to:', currentQuote.type);

      // Navigate to the type-specific form using the dynamic route
      // Use a timeout to ensure state is updated before navigation
      setTimeout(() => {
        try {
          console.log('Navigating to specific quote type:', currentQuote.type);
          console.log('Navigation params:', {
            type: currentQuote.type,
            quoteId: currentQuote.id
          });

          // Use a direct approach to the type-specific form
          console.log('Using direct navigation to type-specific form');

          // First, make sure the current quote is set in the store
          setCurrentQuote(currentQuote);

          // Store the quote ID in AsyncStorage for fallback retrieval
          const storeQuoteData = async () => {
            try {
              await AsyncStorage.setItem('currentQuoteId', currentQuote.id);
              await AsyncStorage.setItem('currentQuoteType', currentQuote.type);
              console.log('Quote data stored in AsyncStorage');
            } catch (storageError) {
              console.error('AsyncStorage error:', storageError);
              // Continue anyway, as we'll try to pass the ID via params
            }
          };

          // Execute the async function
          storeQuoteData();

          // Navigate based on the quote type
          switch (currentQuote.type) {
            case 'motor':
              // Navigate directly to the motor form
              console.log('Navigating to motor form with quoteId:', currentQuote.id);
              router.push({
                pathname: '/(app)/quotes/motor/form',
                params: { quoteId: currentQuote.id }
              });
              break;

            case 'life':
              // Navigate directly to the life form
              console.log('Navigating to life form with quoteId:', currentQuote.id);
              router.push({
                pathname: '/(app)/quotes/life/form',
                params: { quoteId: currentQuote.id }
              });
              break;

            case 'allRisks':
              // Navigate directly to the all risks form
              console.log('Navigating to all risks form with quoteId:', currentQuote.id);
              router.push({
                pathname: '/(app)/quotes/allRisks/form',
                params: { quoteId: currentQuote.id }
              });
              break;

            case 'houseowners':
              // Navigate directly to the houseowners form
              console.log('Navigating to houseowners form with quoteId:', currentQuote.id);
              router.push({
                pathname: '/(app)/quotes/houseowners/form',
                params: { quoteId: currentQuote.id }
              });
              break;

            case 'householdContents':
              // Navigate directly to the household contents form
              console.log('Navigating to household contents form with quoteId:', currentQuote.id);
              router.push({
                pathname: '/(app)/quotes/householdContents/form',
                params: { quoteId: currentQuote.id }
              });
              break;

            case 'scheme':
              // Navigate directly to the scheme form
              console.log('Navigating to scheme form with quoteId:', currentQuote.id);
              router.push({
                pathname: '/(app)/quotes/scheme/form',
                params: { quoteId: currentQuote.id }
              });
              break;

            default:
              // For types without specific forms, use the dynamic route
              console.log('No specific form for type:', currentQuote.type);
              console.log('Falling back to dynamic route');

              router.push({
                pathname: '/(app)/quotes/[type]/index',
                params: {
                  type: currentQuote.type,
                  quoteId: currentQuote.id
                }
              });
              break;
          }
        } catch (navError) {
          console.error('Navigation error:', navError);
          Alert.alert('Navigation Error', 'Failed to navigate to quote form. Please try again.');
        }
      }, 500); // Increased timeout to ensure state updates
    } catch (error) {
      console.error('Error saving client info:', error);
      Alert.alert('Error', 'Failed to save client information');
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  return (
    <QuoteFormContainer
      title="Client Information"
      subtitle="Please provide your personal details"
      steps={steps}
      currentStep={0}
      completedSteps={[]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isLoading}
      nextDisabled={!formValid}
      infoText="This information will be used to generate your insurance quote. All fields marked with * are required."
    >
      <View>
        <DynamicFormField
          label="First Name"
          type="text"
          value={firstName}
          onChange={setFirstName}
          placeholder="Enter your first name"
          error={errors.firstName}
          required
          icon={<User size={20} color={colors.textSecondary} />}
          autoCapitalize="words"
        />

        <DynamicFormField
          label="Last Name"
          type="text"
          value={lastName}
          onChange={setLastName}
          placeholder="Enter your last name"
          error={errors.lastName}
          required
          icon={<User size={20} color={colors.textSecondary} />}
          autoCapitalize="words"
        />

        <DynamicFormField
          label="National ID / Passport No."
          type="text"
          value={idNumber}
          onChange={setIdNumber}
          placeholder="Enter your ID or passport number"
          error={errors.idNumber}
          required
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Email"
          type="email"
          value={email}
          onChange={setEmail}
          placeholder="Enter your email address"
          error={errors.email}
          required
          icon={<Mail size={20} color={colors.textSecondary} />}
          keyboardType="email-address"
        />

        <DynamicFormField
          label="Phone Number"
          type="phone"
          value={phone}
          onChange={setPhone}
          placeholder="Enter your phone number"
          error={errors.phone}
          required
          icon={<Phone size={20} color={colors.textSecondary} />}
          keyboardType="phone-pad"
        />

        <DynamicFormField
          label="Residential / Business Address"
          type="text"
          value={address}
          onChange={setAddress}
          placeholder="Enter your address"
          required
          icon={<MapPin size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={dateOfBirth}
          onChange={setDateOfBirth}
          placeholder="Select your date of birth"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Occupation / Nature of Business"
          type="text"
          value={occupation}
          onChange={setOccupation}
          placeholder="Enter your occupation or business type"
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Payment Preference"
          type="select"
          value={paymentPreference}
          onChange={setPaymentPreference}
          options={[
            { value: 'annual', label: 'Annual (EFT only)' },
            { value: 'monthly', label: 'Monthly (Direct Debit Order)' },
          ]}
          icon={<DollarSign size={20} color={colors.textSecondary} />}
        />
      </View>
    </QuoteFormContainer>
  );
}
