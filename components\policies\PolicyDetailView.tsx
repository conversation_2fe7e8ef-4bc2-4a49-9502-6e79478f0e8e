import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  Shield, FileText, CreditCard, Calendar, AlertCircle,
  Download, Share2, Clock, CheckCircle, FileCheck
} from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { Policy, PolicyDocument, PolicyPayment, CoverageItem } from '@/store/policyStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import { showToast } from '@/utils/toast';
import PolicyRenewalSection from './PolicyRenewalSection';
import CoverageDetailsList from './CoverageDetailsList';

interface PolicyDetailViewProps {
  policy: Policy;
  onDownloadDocument?: (document: PolicyDocument) => void;
  onShareDocument?: (document: PolicyDocument) => void;
  onMakePayment?: (payment: PolicyPayment) => void;
  onRenewPolicy?: () => void;
  onFileClaimPress?: () => void;
}

const PolicyDetailView: React.FC<PolicyDetailViewProps> = ({
  policy,
  onDownloadDocument,
  onShareDocument,
  onMakePayment,
  onRenewPolicy,
  onFileClaimPress,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [activeTab, setActiveTab] = useState<'overview' | 'documents' | 'payments' | 'coverage'>('overview');

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate days until renewal
  const getDaysUntilRenewal = () => {
    const today = new Date();
    const renewalDate = new Date(policy.renewalDate);
    const diffTime = renewalDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return colors.success[500];
      case 'pending_renewal':
        return colors.warning[500];
      case 'renewed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      case 'expired':
        return colors.error[500];
      default:
        return colors.textSecondary;
    }
  };

  // Get payment status color
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return colors.success[500];
      case 'due':
        return colors.warning[500];
      case 'overdue':
        return colors.error[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.textSecondary;
    }
  };

  // Get payment status icon
  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle size={16} color={colors.success[500]} />;
      case 'due':
        return <Clock size={16} color={colors.warning[500]} />;
      case 'overdue':
        return <AlertCircle size={16} color={colors.error[500]} />;
      case 'cancelled':
        return <AlertCircle size={16} color={colors.error[500]} />;
      default:
        return null;
    }
  };

  // Handle document download
  const handleDownloadDocument = (document: PolicyDocument) => {
    if (onDownloadDocument) {
      onDownloadDocument(document);
    } else {
      showToast(
        'info',
        'Download Started',
        'Your document is being downloaded',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle document share
  const handleShareDocument = (document: PolicyDocument) => {
    if (onShareDocument) {
      onShareDocument(document);
    } else {
      showToast(
        'info',
        'Share',
        'Sharing document...',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle make payment
  const handleMakePayment = (payment: PolicyPayment) => {
    if (onMakePayment) {
      onMakePayment(payment);
    } else {
      showToast(
        'info',
        'Payment',
        'Redirecting to payment...',
        { visibilityTime: 3000 }
      );
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    headerTop: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.primary[50],
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    headerContent: {
      flex: 1,
    },
    policyType: {
      ...typography.h3,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    policyNumber: {
      ...typography.body,
      color: colors.textSecondary,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${getStatusColor(policy.status)}20`,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
      alignSelf: 'flex-start',
      marginTop: spacing.xs,
    },
    statusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: getStatusColor(policy.status),
      marginRight: spacing.xs,
    },
    statusText: {
      ...typography.caption,
      color: getStatusColor(policy.status),
      fontWeight: 'bold',
    },
    tabsContainer: {
      flexDirection: 'row',
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      marginBottom: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    tab: {
      flex: 1,
      paddingVertical: spacing.sm,
      alignItems: 'center',
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: colors.primary[500],
    },
    tabText: {
      ...typography.button,
      color: colors.textSecondary,
    },
    activeTabText: {
      color: colors.primary[500],
    },
    contentContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    sectionTitle: {
      ...typography.h4,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: spacing.xs,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    infoLabel: {
      ...typography.body,
      color: colors.textSecondary,
    },
    infoValue: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
      textAlign: 'right',
    },
    renewalContainer: {
      backgroundColor: colors.warning[50],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
    },
    renewalTitle: {
      ...typography.h4,
      color: colors.warning[700],
      marginBottom: spacing.xs,
    },
    renewalText: {
      ...typography.body,
      color: colors.warning[700],
      marginBottom: spacing.sm,
    },
    renewButton: {
      backgroundColor: colors.warning[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      alignItems: 'center',
      alignSelf: 'flex-start',
    },
    renewButtonText: {
      ...typography.button,
      color: colors.white,
    },
    actionButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
    },
    actionButton: {
      flex: 1,
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      alignItems: 'center',
      marginHorizontal: spacing.xs,
    },
    actionButtonText: {
      ...typography.button,
      color: colors.white,
    },
    documentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    documentIcon: {
      marginRight: spacing.sm,
    },
    documentInfo: {
      flex: 1,
    },
    documentName: {
      ...typography.body,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    documentDate: {
      ...typography.caption,
      color: colors.textSecondary,
    },
    documentActions: {
      flexDirection: 'row',
    },
    documentAction: {
      padding: spacing.xs,
      marginLeft: spacing.xs,
    },
    paymentItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    paymentInfo: {
      flex: 1,
    },
    paymentDate: {
      ...typography.body,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    paymentReference: {
      ...typography.caption,
      color: colors.textSecondary,
    },
    paymentAmount: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
      marginRight: spacing.sm,
    },
    paymentStatusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    paymentStatusText: {
      ...typography.caption,
      marginLeft: spacing.xs,
    },
    paymentAction: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      marginLeft: spacing.sm,
    },
    paymentActionText: {
      ...typography.caption,
      color: colors.white,
    },
    coverageItem: {
      padding: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    coverageName: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
      marginBottom: spacing.xs,
    },
    coverageDescription: {
      ...typography.body,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    coverageDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    coverageAmount: {
      ...typography.caption,
      color: colors.text,
    },
    coveragePremium: {
      ...typography.caption,
      color: colors.primary[500],
    },
    addOnBadge: {
      backgroundColor: colors.primary[50],
      borderRadius: borders.radius.sm,
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      marginLeft: spacing.xs,
    },
    addOnText: {
      ...typography.caption,
      color: colors.primary[700],
    },
    emptyState: {
      alignItems: 'center',
      padding: spacing.lg,
    },
    emptyStateText: {
      ...typography.body,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.sm,
    },
  });

  return (
    <Animated.View style={styles.container} entering={FadeIn.duration(300)}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View style={styles.iconContainer}>
            <Shield size={24} color={colors.primary[500]} />
          </View>
          <View style={styles.headerContent}>
            <Text style={styles.policyType}>{policy.type} Insurance</Text>
            <Text style={styles.policyNumber}>Policy: {policy.policyNumber}</Text>
          </View>
        </View>

        <View style={styles.statusContainer}>
          <View style={styles.statusDot} />
          <Text style={styles.statusText}>
            {policy.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
      </View>

      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>
            Overview
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'documents' && styles.activeTab]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[styles.tabText, activeTab === 'documents' && styles.activeTabText]}>
            Documents
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'payments' && styles.activeTab]}
          onPress={() => setActiveTab('payments')}
        >
          <Text style={[styles.tabText, activeTab === 'payments' && styles.activeTabText]}>
            Payments
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'coverage' && styles.activeTab]}
          onPress={() => setActiveTab('coverage')}
        >
          <Text style={[styles.tabText, activeTab === 'coverage' && styles.activeTabText]}>
            Coverage
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView>
        {activeTab === 'overview' && (
          <View style={styles.contentContainer}>
            <Text style={styles.sectionTitle}>Policy Details</Text>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Policy Holder</Text>
              <Text style={styles.infoValue}>
                {policy.clientInfo.firstName} {policy.clientInfo.lastName}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Start Date</Text>
              <Text style={styles.infoValue}>{formatDate(policy.startDate)}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>End Date</Text>
              <Text style={styles.infoValue}>{formatDate(policy.endDate)}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Premium</Text>
              <Text style={styles.infoValue}>
                {formatCurrency(policy.premium, policy.currency)}/{policy.paymentFrequency}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Coverage Amount</Text>
              <Text style={styles.infoValue}>
                {formatCurrency(policy.coverAmount, policy.currency)}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Underwriter</Text>
              <Text style={styles.infoValue}>{policy.underwriter}</Text>
            </View>

            <PolicyRenewalSection
              policy={policy}
              onRenewPolicy={onRenewPolicy || (() => Promise.resolve())}
            />

            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={onFileClaimPress}
              >
                <Text style={styles.actionButtonText}>File a Claim</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {activeTab === 'documents' && (
          <View style={styles.contentContainer}>
            <Text style={styles.sectionTitle}>Policy Documents</Text>

            {policy.documents.length > 0 ? (
              policy.documents.map((document) => (
                <View key={document.id} style={styles.documentItem}>
                  <FileText size={24} color={colors.primary[500]} style={styles.documentIcon} />

                  <View style={styles.documentInfo}>
                    <Text style={styles.documentName}>{document.name}</Text>
                    <Text style={styles.documentDate}>
                      Issued: {formatDate(document.issueDate)}
                    </Text>
                  </View>

                  <View style={styles.documentActions}>
                    <TouchableOpacity
                      style={styles.documentAction}
                      onPress={() => handleDownloadDocument(document)}
                    >
                      <Download size={20} color={colors.primary[500]} />
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.documentAction}
                      onPress={() => handleShareDocument(document)}
                    >
                      <Share2 size={20} color={colors.primary[500]} />
                    </TouchableOpacity>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <FileCheck size={48} color={colors.textSecondary} />
                <Text style={styles.emptyStateText}>
                  No documents available for this policy yet.
                </Text>
              </View>
            )}
          </View>
        )}

        {activeTab === 'payments' && (
          <View style={styles.contentContainer}>
            <Text style={styles.sectionTitle}>Payment History</Text>

            {policy.payments.length > 0 ? (
              policy.payments.map((payment) => (
                <View key={payment.id} style={styles.paymentItem}>
                  <View style={styles.paymentInfo}>
                    <Text style={styles.paymentDate}>
                      {payment.paidDate ? formatDate(payment.paidDate) : formatDate(payment.dueDate)}
                    </Text>
                    {payment.reference && (
                      <Text style={styles.paymentReference}>
                        Ref: {payment.reference}
                      </Text>
                    )}
                  </View>

                  <Text style={styles.paymentAmount}>
                    {formatCurrency(payment.amount, payment.currency)}
                  </Text>

                  <View style={styles.paymentStatusContainer}>
                    {getPaymentStatusIcon(payment.status)}
                    <Text
                      style={[
                        styles.paymentStatusText,
                        { color: getPaymentStatusColor(payment.status) },
                      ]}
                    >
                      {payment.status.toUpperCase()}
                    </Text>
                  </View>

                  {(payment.status === 'due' || payment.status === 'overdue') && (
                    <TouchableOpacity
                      style={styles.paymentAction}
                      onPress={() => handleMakePayment(payment)}
                    >
                      <Text style={styles.paymentActionText}>Pay</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <CreditCard size={48} color={colors.textSecondary} />
                <Text style={styles.emptyStateText}>
                  No payment records available for this policy yet.
                </Text>
              </View>
            )}
          </View>
        )}

        {activeTab === 'coverage' && (
          <View style={styles.contentContainer}>
            <Text style={styles.sectionTitle}>Coverage Details</Text>

            <CoverageDetailsList
              coverageItems={policy.coverage}
              currency={policy.currency}
              totalCoverAmount={policy.coverAmount}
              totalPremium={policy.premium}
            />
          </View>
        )}
      </ScrollView>
    </Animated.View>
  );
};

export default PolicyDetailView;
