import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import notificationService from '@/services/notificationService';

// Define notification types
export type NotificationType = 'info' | 'warning' | 'error' | 'success' | 'policy' | 'claim' | 'application' | 'document' | 'payment' | 'renewal';

// Define notification interface
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt: string;
  actionRoute?: string;
  actionLabel?: string;
  actionData?: any;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  expiresAt?: string;
  userId?: string;
}

// Define notification preferences
export interface NotificationPreferences {
  enablePush: boolean;
  enableEmail: boolean;
  enableSMS: boolean;
  categories: {
    [key: string]: boolean;
  };
}

// Define notification store state
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  preferences: NotificationPreferences;

  // Actions
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAllNotifications: () => Promise<void>;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => Promise<void>;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
  getNotificationsByCategory: (category: string) => Notification[];
  getUnreadNotificationsByCategory: (category: string) => Notification[];

  // Specific notification triggers for different events
  notifyDocumentVerified: (documentName: string, documentId: string) => Promise<void>;
  notifyDocumentRejected: (documentName: string, documentId: string, reason: string) => Promise<void>;
  notifyApplicationStatusChange: (applicationId: string, status: string, message: string) => Promise<void>;
  notifyPaymentDue: (policyNumber: string, amount: number, dueDate: string) => Promise<void>;
  notifyPaymentReceived: (policyNumber: string, amount: number) => Promise<void>;
  notifyPolicyIssued: (policyNumber: string, policyType: string) => Promise<void>;
  notifyClaimUpdate: (claimNumber: string, status: string, message: string) => Promise<void>;
  notifyPromotion: (title: string, message: string, actionRoute?: string) => Promise<void>;
}

// Default notification preferences
const defaultPreferences: NotificationPreferences = {
  enablePush: true,
  enableEmail: true,
  enableSMS: false,
  categories: {
    policy: true,
    claim: true,
    payment: true,
    document: true,
    application: true,
    renewal: true,
    system: true,
  },
}

// Create the store
const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  preferences: defaultPreferences,

  // Fetch all notifications
  fetchNotifications: async () => {
    set({ isLoading: true, error: null });
    try {
      // Fetch notifications from service
      const notifications = await notificationService.getNotifications();

      // Calculate unread count
      const unreadCount = notifications.filter(notification => !notification.read).length;

      // Update state
      set({
        notifications,
        unreadCount,
        isLoading: false
      });

      // Load preferences
      try {
        const preferencesJson = await AsyncStorage.getItem('notificationPreferences');
        if (preferencesJson) {
          const preferences = JSON.parse(preferencesJson);
          set({ preferences });
        }
      } catch (error) {
        console.error('Error loading notification preferences:', error);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      set({
        error: 'Failed to fetch notifications. Please try again.',
        isLoading: false
      });
    }
  },

  // Mark notification as read
  markAsRead: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Mark notification as read in service
      await notificationService.markAsRead(id);

      // Update state
      set(state => {
        const updatedNotifications = state.notifications.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        );

        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(notification => !notification.read).length,
          isLoading: false
        };
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      set({
        error: 'Failed to mark notification as read. Please try again.',
        isLoading: false
      });
    }
  },

  // Mark all notifications as read
  markAllAsRead: async () => {
    set({ isLoading: true, error: null });
    try {
      // Mark all notifications as read in service
      await notificationService.markAllAsRead();

      // Update state
      set(state => ({
        notifications: state.notifications.map(notification => ({ ...notification, read: true })),
        unreadCount: 0,
        isLoading: false
      }));

      // Show success toast
      showToast(
        'success',
        'All Notifications Read',
        'All notifications have been marked as read',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      set({
        error: 'Failed to mark all notifications as read. Please try again.',
        isLoading: false
      });
    }
  },

  // Delete notification
  deleteNotification: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Delete notification in service
      await notificationService.deleteNotification(id);

      // Update state
      set(state => {
        const updatedNotifications = state.notifications.filter(notification => notification.id !== id);

        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(notification => !notification.read).length,
          isLoading: false
        };
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      set({
        error: 'Failed to delete notification. Please try again.',
        isLoading: false
      });
    }
  },

  // Clear all notifications
  clearAllNotifications: async () => {
    set({ isLoading: true, error: null });
    try {
      // Clear all notifications in service
      await notificationService.clearAllNotifications();

      // Update state
      set({
        notifications: [],
        unreadCount: 0,
        isLoading: false
      });
    } catch (error) {
      console.error('Error clearing notifications:', error);
      set({
        error: 'Failed to clear notifications. Please try again.',
        isLoading: false
      });
    }
  },

  // Add a new notification
  addNotification: async (notification: Omit<Notification, 'id' | 'createdAt'>) => {
    set({ isLoading: true, error: null });
    try {
      // Create new notification
      const newNotification: Notification = {
        ...notification,
        id: `notification-${Date.now()}`,
        createdAt: new Date().toISOString(),
      };

      // Update state
      set(state => {
        const updatedNotifications = [newNotification, ...state.notifications];

        return {
          notifications: updatedNotifications,
          unreadCount: updatedNotifications.filter(notification => !notification.read).length,
          isLoading: false
        };
      });

      // Send local notification if push is enabled
      if (get().preferences.enablePush) {
        await notificationService.sendLocalNotification(
          notification.title,
          notification.message,
          notification.actionData
        );
      }
    } catch (error) {
      console.error('Error adding notification:', error);
      set({
        error: 'Failed to add notification. Please try again.',
        isLoading: false
      });
    }
  },

  // Update notification preferences
  updatePreferences: async (preferences: Partial<NotificationPreferences>) => {
    try {
      const updatedPreferences = {
        ...get().preferences,
        ...preferences
      };

      // Save to AsyncStorage
      await AsyncStorage.setItem('notificationPreferences', JSON.stringify(updatedPreferences));

      // Update state
      set({ preferences: updatedPreferences });

      // Show success toast
      showToast(
        'success',
        'Preferences Updated',
        'Your notification preferences have been updated',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      showToast(
        'error',
        'Update Failed',
        'Failed to update notification preferences',
        { visibilityTime: 2000 }
      );
    }
  },

  // Get notifications by category
  getNotificationsByCategory: (category: string) => {
    return get().notifications.filter(notification => notification.category === category);
  },

  // Get unread notifications by category
  getUnreadNotificationsByCategory: (category: string) => {
    return get().notifications.filter(
      notification => notification.category === category && !notification.read
    );
  },

  // Specific notification triggers
  notifyDocumentVerified: async (documentName: string, documentId: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.document) return;

    await addNotification({
      title: 'Document Verified ✅',
      message: `Your ${documentName} has been successfully verified and approved.`,
      type: 'success',
      read: false,
      category: 'document',
      priority: 'medium',
      actionRoute: `/documents/${documentId}`,
      actionLabel: 'View Document',
      actionData: { documentId, documentName },
    });
  },

  notifyDocumentRejected: async (documentName: string, documentId: string, reason: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.document) return;

    await addNotification({
      title: 'Document Rejected ❌',
      message: `Your ${documentName} was rejected. Reason: ${reason}. Please upload a new document.`,
      type: 'error',
      read: false,
      category: 'document',
      priority: 'high',
      actionRoute: `/documents/upload`,
      actionLabel: 'Upload New Document',
      actionData: { documentId, documentName, reason },
    });
  },

  notifyApplicationStatusChange: async (applicationId: string, status: string, message: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.application) return;

    const getTypeAndPriority = (status: string) => {
      switch (status.toLowerCase()) {
        case 'approved':
          return { type: 'success' as NotificationType, priority: 'high' as const };
        case 'rejected':
          return { type: 'error' as NotificationType, priority: 'high' as const };
        case 'under_review':
          return { type: 'info' as NotificationType, priority: 'medium' as const };
        default:
          return { type: 'info' as NotificationType, priority: 'medium' as const };
      }
    };

    const { type, priority } = getTypeAndPriority(status);

    await addNotification({
      title: `Application ${status.replace('_', ' ').toUpperCase()}`,
      message,
      type,
      read: false,
      category: 'application',
      priority,
      actionRoute: `/applications/${applicationId}`,
      actionLabel: 'View Application',
      actionData: { applicationId, status },
    });
  },

  notifyPaymentDue: async (policyNumber: string, amount: number, dueDate: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.payment) return;

    await addNotification({
      title: 'Payment Due 💰',
      message: `Payment of BWP ${amount.toFixed(2)} for policy ${policyNumber} is due on ${dueDate}.`,
      type: 'warning',
      read: false,
      category: 'payment',
      priority: 'high',
      actionRoute: `/payment/new`,
      actionLabel: 'Make Payment',
      actionData: { policyNumber, amount, dueDate },
      expiresAt: dueDate,
    });
  },

  notifyPaymentReceived: async (policyNumber: string, amount: number) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.payment) return;

    await addNotification({
      title: 'Payment Received ✅',
      message: `Your payment of BWP ${amount.toFixed(2)} for policy ${policyNumber} has been received and processed.`,
      type: 'success',
      read: false,
      category: 'payment',
      priority: 'medium',
      actionRoute: `/policies/${policyNumber}`,
      actionLabel: 'View Policy',
      actionData: { policyNumber, amount },
    });
  },

  notifyPolicyIssued: async (policyNumber: string, policyType: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.policy) return;

    await addNotification({
      title: 'Policy Issued 🎉',
      message: `Your ${policyType} policy ${policyNumber} has been issued and is now active.`,
      type: 'success',
      read: false,
      category: 'policy',
      priority: 'high',
      actionRoute: `/policies/${policyNumber}`,
      actionLabel: 'View Policy',
      actionData: { policyNumber, policyType },
    });
  },

  notifyClaimUpdate: async (claimNumber: string, status: string, message: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.claim) return;

    const getTypeAndPriority = (status: string) => {
      switch (status.toLowerCase()) {
        case 'approved':
        case 'paid':
          return { type: 'success' as NotificationType, priority: 'high' as const };
        case 'rejected':
          return { type: 'error' as NotificationType, priority: 'high' as const };
        case 'under_review':
          return { type: 'info' as NotificationType, priority: 'medium' as const };
        default:
          return { type: 'info' as NotificationType, priority: 'medium' as const };
      }
    };

    const { type, priority } = getTypeAndPriority(status);

    await addNotification({
      title: `Claim ${status.replace('_', ' ').toUpperCase()}`,
      message: `Claim ${claimNumber}: ${message}`,
      type,
      read: false,
      category: 'claim',
      priority,
      actionRoute: `/claims/${claimNumber}`,
      actionLabel: 'View Claim',
      actionData: { claimNumber, status },
    });
  },

  notifyPromotion: async (title: string, message: string, actionRoute?: string) => {
    const { addNotification, preferences } = get();

    if (!preferences.categories.system) return;

    await addNotification({
      title: `🎁 ${title}`,
      message,
      type: 'info',
      read: false,
      category: 'system',
      priority: 'low',
      actionRoute,
      actionLabel: actionRoute ? 'Learn More' : undefined,
      actionData: { title, isPromotion: true },
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    });
  },
}));

export default useNotificationStore;
