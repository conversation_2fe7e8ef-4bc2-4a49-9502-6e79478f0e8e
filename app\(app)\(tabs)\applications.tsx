import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { FileText, Clock, Check, AlertCircle, ListChecks, FileBarChart } from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import useApplicationStore, { Application } from '@/store/applicationStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import TabNavigation from '@/components/navigation/TabNavigation';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function ApplicationsTabScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { applications, fetchApplications, isLoading } = useApplicationStore();
  const [activeTab, setActiveTab] = useState('all');

  // Load applications
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  // Handle application selection
  const handleApplicationSelect = (application: Application) => {
    router.push({
      pathname: '/applications/[id]',
      params: { id: application.id }
    });
  };

  // Get status color and icon
  const getStatusInfo = (status: Application['status']) => {
    switch (status) {
      case 'quote_accepted':
        return {
          color: colors.info[500],
          bgColor: colors.info[50],
          icon: <Check size={16} color={colors.info[500]} />,
          text: 'Quote Accepted',
        };
      case 'submitted':
        return {
          color: colors.primary[500],
          bgColor: colors.primary[50],
          icon: <FileText size={16} color={colors.primary[500]} />,
          text: 'Submitted',
        };
      case 'payment_pending':
        return {
          color: colors.warning[500],
          bgColor: colors.warning[50],
          icon: <Clock size={16} color={colors.warning[500]} />,
          text: 'Payment Pending',
        };
      case 'payment_verified':
        return {
          color: colors.primary[500],
          bgColor: colors.primary[50],
          icon: <Check size={16} color={colors.primary[500]} />,
          text: 'Payment Verified',
        };
      case 'underwriting':
        return {
          color: colors.primary[700],
          bgColor: colors.primary[50],
          icon: <FileText size={16} color={colors.primary[700]} />,
          text: 'Underwriting',
        };
      case 'approved':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Approved',
        };
      case 'policy_issued':
        return {
          color: colors.success[700],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[700]} />,
          text: 'Policy Issued',
        };
      default:
        return {
          color: colors.textSecondary,
          bgColor: colors.card,
          icon: <Clock size={16} color={colors.textSecondary} />,
          text: status.replace(/_/g, ' '),
        };
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
    },
    applicationCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderLeftWidth: 4,
    },
    applicationHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    applicationType: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    applicationReference: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    applicationDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    applicationPremium: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginTop: spacing.xs,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.full,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs,
    },
    documentsRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.sm,
    },
    documentIcon: {
      marginRight: spacing.xs,
    },
    documentText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl * 2,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  // Render application item
  const renderApplicationItem = ({ item, index }: { item: Application; index: number }) => {
    const statusInfo = getStatusInfo(item.status);

    // Count document statuses
    const docCounts = item.documents.reduce((acc, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalDocs = item.documents.length;
    const verifiedDocs = docCounts.verified || 0;
    const pendingDocs = docCounts.pending || 0;
    const rejectedDocs = docCounts.rejected || 0;

    return (
      <Animated.View entering={FadeInDown.delay(100 + index * 50).springify()}>
        <TouchableOpacity
          style={[styles.applicationCard, { borderLeftColor: statusInfo.color }]}
          onPress={() => handleApplicationSelect(item)}
        >
          <View style={styles.applicationHeader}>
            <Text style={styles.applicationType}>{item.type}</Text>
            <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
              {statusInfo.icon}
              <Text style={[styles.statusText, { color: statusInfo.color }]}>
                {statusInfo.text}
              </Text>
            </View>
          </View>

          <Text style={styles.applicationReference}>Ref: {item.reference}</Text>
          <Text style={styles.applicationDate}>Date: {item.date}</Text>
          <Text style={styles.applicationPremium}>
            Premium: {formatCurrency(item.premium, item.currency)}
          </Text>

          <View style={styles.documentsRow}>
            <FileText size={16} color={colors.textSecondary} style={styles.documentIcon} />
            <Text style={styles.documentText}>
              Documents: {verifiedDocs}/{totalDocs} verified
              {pendingDocs > 0 && `, ${pendingDocs} pending`}
              {rejectedDocs > 0 && `, ${rejectedDocs} rejected`}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Define tabs
  const tabs = [
    {
      id: 'all',
      title: 'All Applications',
      icon: <ListChecks size={16} color={activeTab === 'all' ? colors.primary[500] : colors.textSecondary} />
    },
    {
      id: 'status',
      title: 'Status Dashboard',
      icon: <FileBarChart size={16} color={activeTab === 'status' ? colors.primary[500] : colors.textSecondary} />
    }
  ];

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Render content based on loading state and data
  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={{ marginTop: 10, color: colors.text }}>Loading applications...</Text>
        </View>
      );
    }

    if (applications.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <FileText size={48} color={colors.textSecondary} />
          <Text style={styles.emptyText}>
            No applications yet
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={applications}
        renderItem={renderApplicationItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Applications</Text>
      </View>

      <View style={styles.content}>
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />

        {renderContent()}
      </View>

    </SafeAreaView>
  );
}
