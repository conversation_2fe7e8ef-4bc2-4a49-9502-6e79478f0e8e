import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import usePaymentStore from '@/store/paymentStore';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';
// Payment service removed - using document upload context instead
import { useDocumentUpload } from '@/context/DocumentUploadContext';
import HeaderBar from '@/components/ui/HeaderBar';
import { Document } from '@/components/documents/types';
import DocumentUploader from '@/components/documents/DocumentUploader';
import { CreditCard, Info } from 'lucide-react-native';

// No mock data - we'll use the API or local cache

export default function PaymentVerificationScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const paymentId = params.id as string;

  // Get payment store methods
  const {
    addPaymentProof,
    isLoading
  } = usePaymentStore();

  // State
  const [payment, setPayment] = useState<any>(null);
  const [isLoadingPayment, setIsLoadingPayment] = useState(true);

  // Load payment details
  useEffect(() => {
    const loadPayment = async () => {
      setIsLoadingPayment(true);
      try {
        // Create a default payment object with the ID since API is not implemented
        const defaultPayment = {
          id: paymentId,
          reference: `REF-${Math.random().toString(36).substring(2, 9).toUpperCase()}`,
          amount: 1500,
          currency: 'BWP',
          date: new Date().toISOString().split('T')[0],
          status: 'pending',
          method: 'eft',
          description: 'Premium payment',
        };

        setPayment(defaultPayment);

        // Show toast to inform user
        showToast(
          'info',
          'Using Local Data',
          'Payment details are being loaded from local storage',
          { visibilityTime: 3000 }
        );
      } catch (error) {
        console.error('Error loading payment:', error);
        showToast(
          'error',
          'Error',
          'Failed to load payment details. Please try again.',
          { visibilityTime: 3000 }
        );
        router.back();
      } finally {
        setIsLoadingPayment(false);
      }
    };

    loadPayment();
  }, [paymentId]);

  // Import document upload context
  const { uploadDocument } = useDocumentUpload();

  // Handle upload proof
  const handleUploadProof = async (documentUri: string, notes?: string) => {
    try {
      if (!payment) {
        throw new Error('Payment not found');
      }

      // Upload document using document upload context
      const document = await uploadDocument({
        uri: documentUri,
        type: 'payment_proof',
        name: `Payment Proof - ${payment.reference}`,
        metadata: {
          paymentId: payment.id,
          reference: payment.reference,
          amount: payment.amount,
          currency: payment.currency,
          date: new Date().toISOString().split('T')[0],
          notes: notes || '',
        }
      });

      if (document) {
        // Add payment proof to payment store
        await addPaymentProof({
          applicationId: payment.applicationId || '',
          reference: payment.reference,
          amount: payment.amount,
          currency: payment.currency,
          date: new Date().toISOString().split('T')[0],
          documentId: document.id
        });

        // Show success toast
        showToast(
          'success',
          'Proof Uploaded',
          'Your proof of payment has been uploaded successfully and is pending verification',
          { visibilityTime: 3000 }
        );

        // Navigate back to payment history
        router.push('/payment/history');
      }
    } catch (error) {
      console.error('Error uploading proof of payment:', error);
      showToast(
        'error',
        'Upload Error',
        'Failed to upload proof of payment. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle cancel
  const handleCancel = () => {
    router.back();
  };

  // Handle document uploaded
  const handleDocumentUploaded = async (document: Document) => {
    try {
      if (!payment) {
        throw new Error('Payment not found');
      }

      console.log('Document uploaded:', document);

      // Add payment metadata to the document
      document.metadata = {
        ...document.metadata,
        paymentId: payment.id,
        reference: payment.reference,
        amount: payment.amount,
        currency: payment.currency,
        paymentType: 'premium',
        notes: '',
      };

      // Add payment proof to payment store
      await addPaymentProof({
        applicationId: payment.applicationId || '',
        reference: payment.reference,
        amount: payment.amount,
        currency: payment.currency,
        date: new Date().toISOString().split('T')[0],
        documentId: document.id
      });

      // Show success toast
      showToast(
        'success',
        'Proof Uploaded',
        'Your proof of payment has been uploaded successfully and is pending verification',
        { visibilityTime: 3000 }
      );

      // Navigate back to payment history
      router.push('/payment/history');
    } catch (error) {
      console.error('Error processing payment proof:', error);
      showToast(
        'error',
        'Processing Error',
        'Failed to process payment proof. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <HeaderBar
        title="Upload Payment Proof"
        showBackButton
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.content}>
        {isLoadingPayment ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
          </View>
        ) : payment ? (
          <View style={styles.paymentContainer}>
            {/* Payment Details */}
            <View style={styles.paymentDetailsCard}>
              <Text style={styles.cardTitle}>Payment Details</Text>

              <View style={styles.paymentDetails}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Reference</Text>
                  <Text style={styles.detailValue}>{payment.reference}</Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Amount</Text>
                  <Text style={styles.detailValue}>{payment.currency} {payment.amount.toFixed(2)}</Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Date</Text>
                  <Text style={styles.detailValue}>{new Date(payment.date).toLocaleDateString()}</Text>
                </View>

                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Description</Text>
                  <Text style={styles.detailValue}>{payment.description}</Text>
                </View>
              </View>
            </View>

            {/* Document Uploader */}
            <View style={styles.uploaderCard}>
              <Text style={styles.cardTitle}>Upload Proof of Payment</Text>
              <Text style={styles.cardDescription}>
                Please upload a document showing proof of your payment. This can be a screenshot,
                photo, or PDF of your payment confirmation.
              </Text>

              <DocumentUploader
                onDocumentUploaded={handleDocumentUploaded}
                preselectedDocumentType="Payment Proof - Premium"
              />

              <View style={styles.infoContainer}>
                <Info size={20} color={colors.info[500]} style={styles.infoIcon} />
                <Text style={styles.infoText}>
                  Your payment proof will be verified by our team. This process may take up to 24 hours.
                  You will receive a notification once your payment has been verified.
                </Text>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Payment not found</Text>
          </View>
        )}
      </ScrollView>

      <BottomNavBar currentRoute="payments" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  paymentContainer: {
    flex: 1,
  },
  paymentDetailsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  uploaderCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  cardTitle: {
    fontWeight: '600',
    fontSize: 18,
    color: '#000000',
    marginBottom: 16,
  },
  cardDescription: {
    fontWeight: '400',
    fontSize: 16,
    color: '#666666',
    marginBottom: 16,
  },
  paymentDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontWeight: '400',
    fontSize: 16,
    color: '#666666',
  },
  detailValue: {
    fontWeight: '600',
    fontSize: 16,
    color: '#000000',
  },
  infoContainer: {
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
    padding: 16,
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  infoText: {
    fontWeight: '400',
    fontSize: 14,
    color: '#000000',
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontWeight: '600',
    fontSize: 18,
    color: '#f44336',
    textAlign: 'center',
  },
});
