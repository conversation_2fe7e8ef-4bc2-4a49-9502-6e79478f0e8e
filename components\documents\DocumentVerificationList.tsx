import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  FileText, AlertCircle, Check, Clock, Upload,
  ChevronRight, Eye, RefreshCw, X
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Document } from './types';
import { showToast } from '@/utils/toast';

interface DocumentVerificationListProps {
  documents: Document[];
  onViewDocument?: (document: Document) => void;
  onReuploadDocument?: (document: Document) => void;
  emptyStateMessage?: string;
  showVerificationProgress?: boolean;
}

const DocumentVerificationList: React.FC<DocumentVerificationListProps> = ({
  documents,
  onViewDocument,
  onReuploadDocument,
  emptyStateMessage = 'No documents found',
  showVerificationProgress = true,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Get status icon and color
  const getStatusInfo = (status: 'pending' | 'verified' | 'rejected') => {
    switch (status) {
      case 'verified':
        return {
          icon: <Check size={16} color={colors.success[500]} />,
          color: colors.success[500],
          bgColor: colors.success[50],
          text: 'Verified',
        };
      case 'rejected':
        return {
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          color: colors.error[500],
          bgColor: colors.error[50],
          text: 'Rejected',
        };
      case 'pending':
      default:
        return {
          icon: <Clock size={16} color={colors.warning[500]} />,
          color: colors.warning[500],
          bgColor: colors.warning[50],
          text: 'Pending',
        };
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Render document item
  const renderDocumentItem = ({ item, index }: { item: Document; index: number }) => {
    const statusInfo = getStatusInfo(item.status);

    return (
      <Animated.View
        entering={FadeInDown.delay(100 + index * 50).springify()}
      >
        <View style={styles.documentCard}>
          <View style={styles.documentHeader}>
            <View style={styles.documentIconContainer}>
              <FileText size={20} color={colors.primary[500]} />
            </View>
            <View style={styles.documentInfo}>
              <Text style={styles.documentName}>{item.name}</Text>
              <Text style={styles.documentType}>{item.type}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
              {statusInfo.icon}
              <Text style={[styles.statusText, { color: statusInfo.color }]}>
                {statusInfo.text}
              </Text>
            </View>
          </View>

          <View style={styles.documentDetails}>
            <Text style={styles.documentDate}>
              Uploaded: {formatDate(item.date)}
            </Text>
            
            {item.status === 'rejected' && item.reason && (
              <View style={styles.rejectionReason}>
                <AlertCircle size={14} color={colors.error[500]} style={{ marginRight: spacing.xs }} />
                <Text style={styles.rejectionText}>{item.reason}</Text>
              </View>
            )}

            {showVerificationProgress && item.status === 'pending' && (
              <View style={styles.verificationProgress}>
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View style={styles.progressIndicator} />
                  </View>
                  <Text style={styles.progressText}>Verification in progress...</Text>
                </View>
              </View>
            )}
          </View>

          <View style={styles.documentActions}>
            {onViewDocument && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => onViewDocument(item)}
              >
                <Eye size={16} color={colors.primary[500]} />
                <Text style={styles.actionText}>View</Text>
              </TouchableOpacity>
            )}

            {item.status === 'rejected' && onReuploadDocument && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => onReuploadDocument(item)}
              >
                <RefreshCw size={16} color={colors.primary[500]} />
                <Text style={styles.actionText}>Re-upload</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    documentCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      marginBottom: spacing.md,
      overflow: 'hidden',
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    documentHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    documentIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary[50],
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.sm,
    },
    documentInfo: {
      flex: 1,
    },
    documentName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    documentType: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs,
    },
    documentDetails: {
      padding: spacing.md,
    },
    documentDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    rejectionReason: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.error[50],
      padding: spacing.sm,
      borderRadius: borders.radius.md,
      marginTop: spacing.xs,
    },
    rejectionText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.error[700],
      flex: 1,
    },
    verificationProgress: {
      marginTop: spacing.sm,
    },
    progressContainer: {
      marginTop: spacing.xs,
    },
    progressBar: {
      height: 4,
      backgroundColor: colors.neutral[200],
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressIndicator: {
      height: '100%',
      width: '30%',
      backgroundColor: colors.warning[500],
      borderRadius: 2,
    },
    progressText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: 4,
    },
    documentActions: {
      flexDirection: 'row',
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.sm,
      borderRightWidth: 1,
      borderRightColor: colors.border,
    },
    actionText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    emptyIcon: {
      marginBottom: spacing.md,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  // If no documents, show empty state
  if (documents.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <FileText
          size={48}
          color={colors.textSecondary}
          style={styles.emptyIcon}
        />
        <Text style={styles.emptyText}>{emptyStateMessage}</Text>
      </View>
    );
  }

  // Render document list
  return (
    <View style={styles.container}>
      <FlatList
        data={documents}
        renderItem={renderDocumentItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />
    </View>
  );
};

export default DocumentVerificationList;
