import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Modal,
  Dimensions,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Bell, X, Check, AlertCircle, Info, Clock, ChevronRight } from 'lucide-react-native';
import Animated, { FadeIn, FadeInDown, SlideInRight } from 'react-native-reanimated';
import { useNotificationStore, Notification } from '@/store/notificationStore';
import { formatDistanceToNow } from 'date-fns';
import { showToast } from '@/utils/toast';
import { router } from 'expo-router';

interface NotificationCenterProps {
  isVisible: boolean;
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ isVisible, onClose }) => {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const { width } = Dimensions.get('window');

  // Get notifications from store
  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
  } = useNotificationStore();

  // Handle notification action
  const handleNotificationAction = (notification: Notification) => {
    // Mark as read first
    markAsRead(notification.id);

    // Handle navigation based on notification type
    if (notification.actionRoute) {
      router.push(notification.actionRoute);
      onClose();
    }

    // If notification has custom action data, handle it
    if (notification.actionData) {
      console.log('Notification action data:', notification.actionData);
      // Handle specific actions based on actionData
    }
  };

  // Get icon based on notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <Check size={20} color={colors.success[500]} />;
      case 'error':
        return <AlertCircle size={20} color={colors.error[500]} />;
      case 'warning':
        return <AlertCircle size={20} color={colors.warning[500]} />;
      case 'info':
      default:
        return <Info size={20} color={colors.info[500]} />;
    }
  };

  // Format notification date
  const formatNotificationDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Unknown date';
    }
  };

  // Render notification item
  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <Animated.View entering={FadeInDown.delay(100).springify()}>
      <TouchableOpacity
        style={[
          styles.notificationItem,
          { backgroundColor: item.read ? colors.card : `${colors.primary[500]}10` },
        ]}
        onPress={() => handleNotificationAction(item)}
      >
        <View style={styles.notificationIconContainer}>
          {getNotificationIcon(item.type)}
        </View>
        <View style={styles.notificationContent}>
          <Text style={[styles.notificationTitle, { color: colors.text }]}>
            {item.title}
          </Text>
          <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>
            {item.message}
          </Text>
          <View style={styles.notificationFooter}>
            <View style={styles.notificationTime}>
              <Clock size={12} color={colors.textSecondary} />
              <Text style={[styles.notificationTimeText, { color: colors.textSecondary }]}>
                {formatNotificationDate(item.createdAt)}
              </Text>
            </View>
            {item.actionLabel && (
              <View style={styles.notificationAction}>
                <Text style={[styles.notificationActionText, { color: colors.primary[500] }]}>
                  {item.actionLabel}
                </Text>
                <ChevronRight size={12} color={colors.primary[500]} />
              </View>
            )}
          </View>
        </View>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteNotification(item.id)}
        >
          <X size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );

  // Create styles
  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    notificationPanel: {
      position: 'absolute',
      top: 0,
      right: 0,
      width: width * 0.85,
      height: '100%',
      backgroundColor: colors.background,
      shadowColor: '#000',
      shadowOffset: { width: -2, height: 0 },
      shadowOpacity: 0.25,
      shadowRadius: 5,
      elevation: 5,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    closeButton: {
      padding: spacing.sm,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    actionButton: {
      padding: spacing.xs,
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
    },
    notificationList: {
      flex: 1,
    },
    notificationItem: {
      flexDirection: 'row',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    notificationIconContainer: {
      marginRight: spacing.md,
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.card,
      alignItems: 'center',
      justifyContent: 'center',
    },
    notificationContent: {
      flex: 1,
    },
    notificationTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginBottom: spacing.xs,
    },
    notificationMessage: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      marginBottom: spacing.xs,
    },
    notificationFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    notificationTime: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    notificationTimeText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs,
    },
    notificationAction: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    notificationActionText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginRight: spacing.xs,
    },
    deleteButton: {
      padding: spacing.xs,
    },
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
    },
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <Animated.View 
          style={styles.notificationPanel}
          entering={SlideInRight.duration(300)}
        >
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Notifications</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={markAllAsRead}
            >
              <Text style={styles.actionButtonText}>Mark all as read</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => {
                clearAllNotifications();
                showToast('success', 'Notifications cleared', 'All notifications have been cleared');
              }}
            >
              <Text style={styles.actionButtonText}>Clear all</Text>
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary[500]} />
            </View>
          ) : notifications.length > 0 ? (
            <FlatList
              data={notifications}
              renderItem={renderNotificationItem}
              keyExtractor={(item) => item.id}
              style={styles.notificationList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Bell size={48} color={colors.textSecondary} />
              <Text style={styles.emptyText}>
                You don't have any notifications yet
              </Text>
            </View>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

export default NotificationCenter;
