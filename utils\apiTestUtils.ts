import apiService from '@/services/api';
import { showToast } from './toast';

/**
 * Utility to test API endpoints and report results
 */
export const apiTestUtils = {
  /**
   * Tests a specific API endpoint
   * @param {string} name - Name of the endpoint to display in results
   * @param {Function} apiCall - The API function to call
   * @param {any} params - Parameters to pass to the API call
   * @returns {Promise<{success: boolean, data?: any, error?: any}>} Test result
   */
  testEndpoint: async (
    name: string,
    apiCall: Function,
    params?: any
  ): Promise<{ success: boolean; data?: any; error?: any }> => {
    console.log(`Testing API endpoint: ${name}`);
    try {
      const result = await apiCall(params);
      console.log(`API endpoint ${name} test successful:`, result);
      return { success: true, data: result };
    } catch (error) {
      console.error(`API endpoint ${name} test failed:`, error);
      return { success: false, error };
    }
  },

  /**
   * Tests multiple API endpoints and returns results
   * @param {Array<{name: string, apiCall: Function, params?: any}>} endpoints - Endpoints to test
   * @returns {Promise<Array<{name: string, success: boolean, data?: any, error?: any}>>} Test results
   */
  testMultipleEndpoints: async (
    endpoints: Array<{ name: string; apiCall: Function; params?: any }>
  ): Promise<Array<{ name: string; success: boolean; data?: any; error?: any }>> => {
    const results = [];

    for (const endpoint of endpoints) {
      const result = await apiTestUtils.testEndpoint(
        endpoint.name,
        endpoint.apiCall,
        endpoint.params
      );
      results.push({ name: endpoint.name, ...result });
    }

    return results;
  },

  /**
   * Tests all critical API endpoints and returns a summary
   * @param {boolean} showToasts - Whether to show toast notifications for results
   * @returns {Promise<{success: number, failed: number, results: Array}>} Test summary
   */
  testCriticalEndpoints: async (showToasts = false): Promise<{
    success: number;
    failed: number;
    results: Array<{ name: string; success: boolean; data?: any; error?: any }>;
  }> => {
    // Define critical endpoints to test
    const criticalEndpoints = [
      { name: 'Health Check', apiCall: apiService.health.check },
      { name: 'User Profile', apiCall: apiService.userProfile.getProfile },
      { name: 'Documents', apiCall: apiService.documents.getAllDocuments },
      { name: 'Policies', apiCall: apiService.policies.getUserPolicies },
      { name: 'Claims', apiCall: apiService.claims.getClaims },
      { name: 'Applications', apiCall: apiService.applications.getApplications },
      { name: 'Notifications', apiCall: apiService.notifications.getNotifications },
    ];

    // Test all endpoints
    const results = await apiTestUtils.testMultipleEndpoints(criticalEndpoints);

    // Count successes and failures
    const success = results.filter((r) => r.success).length;
    const failed = results.length - success;

    // Show toast if requested
    if (showToasts) {
      if (failed === 0) {
        showToast(
          'success',
          'API Test Successful',
          `All ${results.length} critical endpoints are working properly`,
          { visibilityTime: 3000 }
        );
      } else {
        showToast(
          'error',
          'API Test Failed',
          `${failed} out of ${results.length} critical endpoints failed`,
          { visibilityTime: 4000 }
        );
      }
    }

    return { success, failed, results };
  },

  /**
   * Tests authentication endpoints
   * @param {string} email - Test email to use
   * @param {string} password - Test password to use
   * @returns {Promise<{success: boolean, message: string, data?: any}>} Test result
   */
  testAuthentication: async (
    email: string,
    password: string
  ): Promise<{ success: boolean; message: string; data?: any }> => {
    try {
      // Test login
      const loginResult = await apiTestUtils.testEndpoint(
        'Login',
        apiService.auth.login,
        [email, password]
      );

      if (!loginResult.success) {
        return {
          success: false,
          message: 'Login failed',
          data: loginResult.error,
        };
      }

      // Test get current user
      const userResult = await apiTestUtils.testEndpoint(
        'Get Current User',
        apiService.auth.getCurrentUser
      );

      if (!userResult.success) {
        return {
          success: false,
          message: 'Get current user failed',
          data: userResult.error,
        };
      }

      return {
        success: true,
        message: 'Authentication endpoints working correctly',
        data: {
          login: loginResult.data,
          user: userResult.data,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Authentication test failed with an unexpected error',
        data: error,
      };
    }
  },
};

export default apiTestUtils;
