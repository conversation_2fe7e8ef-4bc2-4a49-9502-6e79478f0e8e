import { create } from 'zustand';
import {
  Quote,
  InsuranceProductType,
  ClientInfo,
  QuoteStatus
} from '@/types/quote.types';
import { showToast } from '@/utils/toast';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the store state
interface QuoteState {
  quotes: Quote[];
  currentQuote: Quote | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchQuotes: () => Promise<void>;
  getQuoteById: (id: string) => Quote | undefined;
  createQuote: (type: InsuranceProductType) => Promise<Quote>;
  updateQuote: (quote: Partial<Quote>) => Promise<void>;
  updateClientInfo: (clientInfo: Partial<ClientInfo>) => Promise<void>;
  updateQuoteStatus: (id: string, status: QuoteStatus) => Promise<void>;
  deleteQuote: (id: string) => Promise<void>;
  setCurrentQuote: (quote: Quote | null) => void;
  clearCurrentQuote: () => void;
}

// Helper functions for AsyncStorage
const saveQuotesToStorage = async (quotes: Quote[]) => {
  try {
    await AsyncStorage.setItem('quotes', JSON.stringify(quotes));
    console.log('Quotes saved to AsyncStorage:', quotes.length);
  } catch (error) {
    console.error('Error saving quotes to AsyncStorage:', error);
  }
};

const loadQuotesFromStorage = async (): Promise<Quote[]> => {
  try {
    const quotesJson = await AsyncStorage.getItem('quotes');
    if (quotesJson) {
      const quotes = JSON.parse(quotesJson);
      console.log('Quotes loaded from AsyncStorage:', quotes.length);
      return quotes;
    }
  } catch (error) {
    console.error('Error loading quotes from AsyncStorage:', error);
  }
  return [];
};

// Create the store
const useQuoteStore = create<QuoteState>((set, get) => ({
  quotes: [],
  currentQuote: null,
  isLoading: false,
  error: null,

  // Fetch all quotes
  fetchQuotes: async () => {
    set({ isLoading: true, error: null });
    try {
      // First, try to load quotes from AsyncStorage
      const storedQuotes = await loadQuotesFromStorage();

      // If we have stored quotes, use them
      if (storedQuotes && storedQuotes.length > 0) {
        console.log('Using quotes from AsyncStorage:', storedQuotes.length);
        set({ quotes: storedQuotes, isLoading: false });
        return;
      }

      // If no stored quotes, just use an empty array
      set(state => {
        // If we already have quotes in the state, keep them
        if (state.quotes.length > 0) {
          console.log('Using quotes from state:', state.quotes.length);
          return { ...state, isLoading: false };
        }

        // Otherwise, use an empty array
        console.log('No quotes found, starting with empty array');
        return { ...state, quotes: [], isLoading: false };
      });
    } catch (error) {
      console.error('Error fetching quotes:', error);
      set({
        error: 'Failed to fetch quotes. Please try again.',
        isLoading: false
      });
    }
  },

  // Get a quote by ID
  getQuoteById: (id: string) => {
    return get().quotes.find(quote => quote.id === id);
  },

  // Create a new quote
  createQuote: async (type: InsuranceProductType) => {
    console.log('createQuote called with type:', type);
    set({ isLoading: true, error: null });

    try {
      // Validate the insurance type
      const validTypes = [
        'motor', 'houseowners', 'householdContents', 'allRisks',
        'travel', 'health', 'life', 'funeral', 'scheme', 'business'
      ];

      if (!type || !validTypes.includes(type)) {
        console.error(`Invalid insurance type: ${type}`);
        throw new Error(`Invalid insurance type: ${type}`);
      }

      console.log('Creating quote for valid type:', type);

      // In a real app, this would be an API call
      // For now, we'll create a new quote locally
      const timestamp = Date.now();
      const newQuote: Quote = {
        id: timestamp.toString(),
        type,
        status: 'draft',
        createdAt: new Date(timestamp).toISOString().split('T')[0],
        updatedAt: new Date(timestamp).toISOString().split('T')[0],
        expiresAt: new Date(timestamp + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        clientInfo: {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
        },
        documents: [],
      };

      console.log('New quote created:', newQuote.id);

      // Update state with new quote
      let updatedQuotes: Quote[] = [];
      set(state => {
        console.log('Updating state with new quote');
        const newQuotes = [newQuote, ...state.quotes];
        updatedQuotes = newQuotes; // Save for AsyncStorage
        return {
          quotes: newQuotes,
          currentQuote: newQuote,
          isLoading: false
        };
      });

      // Save to AsyncStorage
      await saveQuotesToStorage(updatedQuotes);
      console.log('Quote created and saved to AsyncStorage:', newQuote.id);

      // Show success toast
      try {
        showToast(
          'success',
          'Quote Created',
          `New ${type} quote has been created`,
          { visibilityTime: 3000 }
        );
      } catch (toastError) {
        console.error('Error showing toast:', toastError);
      }

      console.log('Returning new quote:', newQuote.id);
      return newQuote;
    } catch (error) {
      console.error('Error creating quote:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create quote. Please try again.';

      set({
        error: errorMessage,
        isLoading: false
      });

      try {
        showToast(
          'error',
          'Error',
          errorMessage,
          { visibilityTime: 4000 }
        );
      } catch (toastError) {
        console.error('Error showing toast:', toastError);
      }

      throw error;
    }
  },

  // Update a quote
  updateQuote: async (quoteUpdate: Partial<Quote>) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      // For now, we'll update the quote locally
      const { currentQuote } = get();

      if (!currentQuote) {
        throw new Error('No current quote to update');
      }

      const updatedQuote = {
        ...currentQuote,
        ...quoteUpdate,
        updatedAt: new Date().toISOString().split('T')[0],
      };

      // Update the state
      let updatedQuotes: Quote[] = [];
      set(state => {
        const newQuotes = state.quotes.map(q =>
          q.id === updatedQuote.id ? updatedQuote : q
        );
        updatedQuotes = newQuotes; // Save for AsyncStorage
        return {
          quotes: newQuotes,
          currentQuote: updatedQuote,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveQuotesToStorage(updatedQuotes);
      console.log('Quote updated and saved to AsyncStorage:', updatedQuote.id);

      showToast(
        'success',
        'Quote Updated',
        'Your quote has been updated successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error updating quote:', error);
      set({
        error: 'Failed to update quote. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update client info in the current quote
  updateClientInfo: async (clientInfoUpdate: Partial<ClientInfo>) => {
    const { currentQuote, updateQuote } = get();

    if (!currentQuote) {
      set({
        error: 'No current quote to update client info',
        isLoading: false
      });
      return;
    }

    const updatedClientInfo = {
      ...currentQuote.clientInfo,
      ...clientInfoUpdate,
    };

    console.log('Updating client info:', updatedClientInfo);
    await updateQuote({
      clientInfo: updatedClientInfo,
      // Update the timestamp to ensure it's at the top of the list
      updatedAt: new Date().toISOString().split('T')[0]
    });
  },

  // Update quote status
  updateQuoteStatus: async (id: string, status: QuoteStatus) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      let updatedQuotes: Quote[] = [];
      set(state => {
        const newQuotes = state.quotes.map(q =>
          q.id === id ? { ...q, status, updatedAt: new Date().toISOString().split('T')[0] } : q
        );
        updatedQuotes = newQuotes; // Save for AsyncStorage
        return {
          quotes: newQuotes,
          currentQuote: state.currentQuote?.id === id
            ? { ...state.currentQuote, status, updatedAt: new Date().toISOString().split('T')[0] }
            : state.currentQuote,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveQuotesToStorage(updatedQuotes);
      console.log('Quote status updated and saved to AsyncStorage');

      showToast(
        'success',
        'Status Updated',
        `Quote status changed to ${status}`,
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error updating quote status:', error);
      set({
        error: 'Failed to update quote status. Please try again.',
        isLoading: false
      });
    }
  },

  // Delete a quote
  deleteQuote: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      let updatedQuotes: Quote[] = [];
      set(state => {
        const newQuotes = state.quotes.filter(q => q.id !== id);
        updatedQuotes = newQuotes; // Save for AsyncStorage
        return {
          quotes: newQuotes,
          currentQuote: state.currentQuote?.id === id ? null : state.currentQuote,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveQuotesToStorage(updatedQuotes);
      console.log('Quote deleted and quotes saved to AsyncStorage');

      showToast(
        'success',
        'Quote Deleted',
        'The quote has been deleted successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error deleting quote:', error);
      set({
        error: 'Failed to delete quote. Please try again.',
        isLoading: false
      });
    }
  },

  // Set current quote
  setCurrentQuote: (quote: Quote | null) => {
    set({ currentQuote: quote });
  },

  // Clear current quote
  clearCurrentQuote: () => {
    set({ currentQuote: null });
  },
}));

export default useQuoteStore;
