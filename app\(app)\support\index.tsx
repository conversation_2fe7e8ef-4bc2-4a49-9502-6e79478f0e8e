import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  MessageCircle,
  HelpCircle,
  Phone,
  Mail,
  FileText,
  Clock,
  Users,
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface SupportOption {
  id: string;
  title: string;
  description: string;
  icon: any;
  color: string;
  route: string;
}

export default function SupportScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  const supportOptions: SupportOption[] = [
    {
      id: 'chat',
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      icon: MessageCircle,
      color: colors.primary[500],
      route: '/support/chat',
    },
    {
      id: 'faq',
      title: 'FAQ',
      description: 'Find answers to frequently asked questions',
      icon: HelpCircle,
      color: colors.secondary[500],
      route: '/support/faq',
    },
    {
      id: 'contact',
      title: 'Contact Us',
      description: 'Get in touch through phone, email, or form',
      icon: Phone,
      color: colors.success[500],
      route: '/support/contact',
    },
  ];

  const quickLinks = [
    {
      id: 'claims',
      title: 'File a Claim',
      description: 'Submit a new insurance claim',
      icon: FileText,
      route: '/claims/new',
    },
    {
      id: 'policies',
      title: 'My Policies',
      description: 'View and manage your policies',
      icon: FileText,
      route: '/policies',
    },
    {
      id: 'quotes',
      title: 'Get a Quote',
      description: 'Request a new insurance quote',
      icon: FileText,
      route: '/quotes',
    },
  ];

  const renderSupportOption = (option: SupportOption, index: number) => {
    const Icon = option.icon;

    return (
      <Animated.View
        key={option.id}
        entering={FadeInDown.delay(index * 100).springify()}
      >
        <TouchableOpacity
          style={[styles.supportOption, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => router.push(option.route as any)}
        >
          <View style={[styles.supportIcon, { backgroundColor: `${option.color}20` }]}>
            <Icon size={32} color={option.color} />
          </View>
          <View style={styles.supportContent}>
            <Text style={[styles.supportTitle, { color: colors.text }]}>
              {option.title}
            </Text>
            <Text style={[styles.supportDescription, { color: colors.textSecondary }]}>
              {option.description}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderQuickLink = (link: any, index: number) => {
    const Icon = link.icon;

    return (
      <Animated.View
        key={link.id}
        entering={FadeInDown.delay((supportOptions.length + index) * 100).springify()}
      >
        <TouchableOpacity
          style={[styles.quickLink, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => router.push(link.route as any)}
        >
          <Icon size={24} color={colors.primary[500]} />
          <View style={styles.quickLinkContent}>
            <Text style={[styles.quickLinkTitle, { color: colors.text }]}>
              {link.title}
            </Text>
            <Text style={[styles.quickLinkDescription, { color: colors.textSecondary }]}>
              {link.description}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: spacing.sm,
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
    },
    section: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    supportOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.lg,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      marginBottom: spacing.md,
    },
    supportIcon: {
      width: 64,
      height: 64,
      borderRadius: 32,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.lg,
    },
    supportContent: {
      flex: 1,
    },
    supportTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      marginBottom: spacing.xs,
    },
    supportDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      lineHeight: 20,
    },
    quickLink: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      marginBottom: spacing.sm,
    },
    quickLinkContent: {
      flex: 1,
      marginLeft: spacing.md,
    },
    quickLinkTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginBottom: spacing.xs,
    },
    quickLinkDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
    },
    infoContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      borderColor: colors.border,
      padding: spacing.lg,
      marginBottom: spacing.lg,
    },
    infoTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    infoText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      lineHeight: 20,
    },
    hoursContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.md,
    },
    hoursIcon: {
      marginRight: spacing.sm,
    },
    hoursText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Support Center</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Animated.View entering={FadeInDown.delay(50).springify()}>
            <View style={styles.infoContainer}>
              <Text style={styles.infoTitle}>How can we help you?</Text>
              <Text style={styles.infoText}>
                Our support team is here to assist you with any questions about your insurance policies, claims, or account. Choose from the options below to get the help you need.
              </Text>
              <View style={styles.hoursContainer}>
                <Clock size={16} color={colors.primary[500]} style={styles.hoursIcon} />
                <Text style={styles.hoursText}>
                  Available Mon-Fri 8AM-5PM, Emergency claims 24/7
                </Text>
              </View>
            </View>
          </Animated.View>

          <Text style={styles.sectionTitle}>Get Support</Text>
          {supportOptions.map(renderSupportOption)}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          {quickLinks.map(renderQuickLink)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
