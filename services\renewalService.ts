import { Policy } from '@/store/policyStore';
import { differenceInDays, addDays, parseISO } from 'date-fns';
import useNotificationStore from '@/store/notificationStore';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define renewal thresholds in days
const RENEWAL_THRESHOLDS = [60, 30, 14, 7, 3, 1];

// Define renewal notification types
export type RenewalNotificationType = 'upcoming' | 'due' | 'overdue';

// Define renewal notification
export interface RenewalNotification {
  policyId: string;
  policyNumber: string;
  policyType: string;
  daysUntilRenewal: number;
  renewalDate: string;
  notificationType: RenewalNotificationType;
  dismissed: boolean;
}

// Check if a policy is due for renewal
export const isPolicyDueForRenewal = (policy: Policy): boolean => {
  if (!policy.expiryDate) return false;
  
  const today = new Date();
  const expiryDate = parseISO(policy.expiryDate);
  const daysUntilExpiry = differenceInDays(expiryDate, today);
  
  return daysUntilExpiry <= 60 && daysUntilExpiry >= 0;
};

// Check if a policy is overdue for renewal
export const isPolicyOverdueForRenewal = (policy: Policy): boolean => {
  if (!policy.expiryDate) return false;
  
  const today = new Date();
  const expiryDate = parseISO(policy.expiryDate);
  
  return today > expiryDate;
};

// Get days until renewal
export const getDaysUntilRenewal = (policy: Policy): number => {
  if (!policy.expiryDate) return -1;
  
  const today = new Date();
  const expiryDate = parseISO(policy.expiryDate);
  
  return differenceInDays(expiryDate, today);
};

// Get renewal notification type
export const getRenewalNotificationType = (daysUntilRenewal: number): RenewalNotificationType => {
  if (daysUntilRenewal < 0) return 'overdue';
  if (daysUntilRenewal <= 7) return 'due';
  return 'upcoming';
};

// Check if a renewal notification should be sent
export const shouldSendRenewalNotification = async (policy: Policy): Promise<boolean> => {
  if (!policy.expiryDate) return false;
  
  const daysUntilRenewal = getDaysUntilRenewal(policy);
  
  // Check if days until renewal matches any threshold
  if (!RENEWAL_THRESHOLDS.includes(daysUntilRenewal)) return false;
  
  // Check if notification has already been sent for this threshold
  const sentNotificationsJson = await AsyncStorage.getItem(`renewal_notifications_${policy.id}`);
  const sentNotifications = sentNotificationsJson ? JSON.parse(sentNotificationsJson) : [];
  
  return !sentNotifications.includes(daysUntilRenewal);
};

// Mark renewal notification as sent
export const markRenewalNotificationAsSent = async (policy: Policy, daysUntilRenewal: number): Promise<void> => {
  const sentNotificationsJson = await AsyncStorage.getItem(`renewal_notifications_${policy.id}`);
  const sentNotifications = sentNotificationsJson ? JSON.parse(sentNotificationsJson) : [];
  
  sentNotifications.push(daysUntilRenewal);
  
  await AsyncStorage.setItem(`renewal_notifications_${policy.id}`, JSON.stringify(sentNotifications));
};

// Send renewal notification
export const sendRenewalNotification = async (policy: Policy): Promise<void> => {
  const daysUntilRenewal = getDaysUntilRenewal(policy);
  const notificationType = getRenewalNotificationType(daysUntilRenewal);
  
  // Get notification store
  const { addNotification } = useNotificationStore.getState();
  
  // Create notification title based on type
  let title = '';
  let message = '';
  
  switch (notificationType) {
    case 'overdue':
      title = 'Policy Renewal Overdue';
      message = `Your ${policy.type} policy (${policy.policyNumber}) is overdue for renewal. Please renew as soon as possible to maintain coverage.`;
      break;
    case 'due':
      title = 'Policy Renewal Due Soon';
      message = `Your ${policy.type} policy (${policy.policyNumber}) is due for renewal in ${daysUntilRenewal} day${daysUntilRenewal !== 1 ? 's' : ''}. Please renew to maintain coverage.`;
      break;
    case 'upcoming':
      title = 'Policy Renewal Reminder';
      message = `Your ${policy.type} policy (${policy.policyNumber}) is due for renewal in ${daysUntilRenewal} days. Start the renewal process to ensure continuous coverage.`;
      break;
  }
  
  // Add notification
  await addNotification({
    title,
    message,
    type: 'renewal',
    read: false,
    category: 'renewal',
    priority: notificationType === 'overdue' ? 'high' : notificationType === 'due' ? 'medium' : 'low',
    actionRoute: `/policies/${policy.id}/renew`,
    actionLabel: 'Renew Now',
    actionData: {
      policyId: policy.id,
      renewalType: notificationType,
    },
  });
  
  // Mark notification as sent
  await markRenewalNotificationAsSent(policy, daysUntilRenewal);
};

// Check all policies for renewal notifications
export const checkPoliciesForRenewal = async (policies: Policy[]): Promise<void> => {
  for (const policy of policies) {
    if (await shouldSendRenewalNotification(policy)) {
      await sendRenewalNotification(policy);
    }
  }
};

// Get all renewal notifications for a policy
export const getRenewalNotifications = async (policy: Policy): Promise<RenewalNotification[]> => {
  const daysUntilRenewal = getDaysUntilRenewal(policy);
  const notificationType = getRenewalNotificationType(daysUntilRenewal);
  
  // Get dismissed notifications
  const dismissedNotificationsJson = await AsyncStorage.getItem(`dismissed_renewal_notifications_${policy.id}`);
  const dismissedNotifications = dismissedNotificationsJson ? JSON.parse(dismissedNotificationsJson) : [];
  
  // Create renewal notification
  const renewalNotification: RenewalNotification = {
    policyId: policy.id,
    policyNumber: policy.policyNumber,
    policyType: policy.type,
    daysUntilRenewal,
    renewalDate: policy.expiryDate,
    notificationType,
    dismissed: dismissedNotifications.includes(daysUntilRenewal),
  };
  
  return [renewalNotification];
};

// Dismiss renewal notification
export const dismissRenewalNotification = async (policy: Policy, daysUntilRenewal: number): Promise<void> => {
  const dismissedNotificationsJson = await AsyncStorage.getItem(`dismissed_renewal_notifications_${policy.id}`);
  const dismissedNotifications = dismissedNotificationsJson ? JSON.parse(dismissedNotificationsJson) : [];
  
  dismissedNotifications.push(daysUntilRenewal);
  
  await AsyncStorage.setItem(`dismissed_renewal_notifications_${policy.id}`, JSON.stringify(dismissedNotifications));
};

export default {
  isPolicyDueForRenewal,
  isPolicyOverdueForRenewal,
  getDaysUntilRenewal,
  getRenewalNotificationType,
  shouldSendRenewalNotification,
  markRenewalNotificationAsSent,
  sendRenewalNotification,
  checkPoliciesForRenewal,
  getRenewalNotifications,
  dismissRenewalNotification,
};
