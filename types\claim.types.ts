/**
 * Claim Types
 *
 * This file contains types and interfaces related to claims management.
 */

// Document status types (reusing from application store)
export type DocumentStatus = 'pending' | 'verified' | 'rejected';

// Define claim status types
export type ClaimStatus =
  | 'draft'               // Initial status when claim is being created
  | 'submitted'           // Claim submitted
  | 'under_review'        // Claim is being reviewed
  | 'additional_info'     // Additional information requested
  | 'approved'            // Claim approved
  | 'partially_approved'  // Claim partially approved
  | 'rejected'            // Claim rejected
  | 'paid'                // Claim payment processed
  | 'closed';             // Claim closed

// Define claim types
export type ClaimType =
  | 'motor_accident'      // Vehicle accident
  | 'motor_theft'         // Vehicle theft
  | 'motor_damage'        // Vehicle damage
  | 'property_damage'     // Property damage
  | 'property_theft'      // Property theft
  | 'household_damage'    // Household contents damage
  | 'all_risks'           // All risks claim
  | 'medical'             // Medical claim
  | 'life'                // Life insurance claim
  | 'other';              // Other claim types

// Claim document type
export type ClaimDocument = {
  id: string;
  name: string;
  type: string;
  status: DocumentStatus;
  date: string;
  verificationDate?: string;
  rejectionReason?: string;
  required: boolean;
  documentId?: string;
};

// Claim timeline event type
export type ClaimTimelineEvent = {
  id: string;
  date: string;
  title: string;
  description: string;
  status: 'completed' | 'current' | 'upcoming';
  icon?: string;
  actions?: Array<{
    label: string;
    action: string;
  }>;
};

// Main claim interface
export interface Claim {
  id: string;
  policyId: string;
  policyNumber: string;
  type: ClaimType;
  status: ClaimStatus;
  reference: string;
  date: string;
  incidentDate: string;
  description: string;
  claimAmount: number;
  approvedAmount?: number;
  currency: string;
  documents: ClaimDocument[];
  timeline: ClaimTimelineEvent[];
  notes?: string[];
  requiredActions?: Array<{
    id: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    dueDate?: string;
    completed: boolean;
  }>;
  assessorName?: string;
  assessorContact?: string;
  assessmentDate?: string;
  paymentDate?: string;
  paymentReference?: string;
  rejectionReason?: string;
}

// Claim creation input type
export interface ClaimInput {
  policyId: string;
  policyNumber: string;
  type: ClaimType;
  incidentDate: string;
  description: string;
  claimAmount: number;
  currency: string;
}

// Required documents by claim type
export const REQUIRED_DOCUMENTS_BY_CLAIM_TYPE: Record<ClaimType, Array<{ name: string; type: string; required: boolean }>> = {
  motor_accident: [
    { name: 'Police Report', type: 'Legal', required: true },
    { name: 'Driver\'s License', type: 'ID', required: true },
    { name: 'Vehicle Registration', type: 'Vehicle', required: true },
    { name: 'Accident Photos', type: 'Other', required: true },
    { name: 'Repair Estimate', type: 'Other', required: true },
    { name: 'Medical Report (if applicable)', type: 'Medical', required: false }
  ],
  motor_theft: [
    { name: 'Police Report', type: 'Legal', required: true },
    { name: 'Vehicle Registration', type: 'Vehicle', required: true },
    { name: 'Theft Declaration', type: 'Legal', required: true },
    { name: 'Keys Handover Receipt', type: 'Other', required: true }
  ],
  motor_damage: [
    { name: 'Vehicle Registration', type: 'Vehicle', required: true },
    { name: 'Damage Photos', type: 'Other', required: true },
    { name: 'Repair Estimate', type: 'Other', required: true }
  ],
  property_damage: [
    { name: 'Property Ownership Document', type: 'Property', required: true },
    { name: 'Damage Photos', type: 'Other', required: true },
    { name: 'Repair Estimate', type: 'Other', required: true },
    { name: 'Police Report (if applicable)', type: 'Legal', required: false }
  ],
  property_theft: [
    { name: 'Police Report', type: 'Legal', required: true },
    { name: 'Property Ownership Document', type: 'Property', required: true },
    { name: 'List of Stolen Items', type: 'Other', required: true },
    { name: 'Proof of Ownership for Stolen Items', type: 'Other', required: false }
  ],
  household_damage: [
    { name: 'Proof of Residence', type: 'Proof of Address', required: true },
    { name: 'Damage Photos', type: 'Other', required: true },
    { name: 'Repair/Replacement Estimate', type: 'Other', required: true },
    { name: 'Inventory List', type: 'Other', required: true }
  ],
  all_risks: [
    { name: 'Proof of Ownership', type: 'Other', required: true },
    { name: 'Police Report (if theft)', type: 'Legal', required: false },
    { name: 'Damage Photos (if damage)', type: 'Other', required: false },
    { name: 'Replacement Estimate', type: 'Other', required: true }
  ],
  medical: [
    { name: 'Medical Report', type: 'Medical', required: true },
    { name: 'Medical Bills', type: 'Financial', required: true },
    { name: 'Prescription', type: 'Medical', required: false },
    { name: 'Hospital Discharge Summary', type: 'Medical', required: false }
  ],
  life: [
    { name: 'Death Certificate', type: 'Legal', required: true },
    { name: 'Beneficiary ID', type: 'ID', required: true },
    { name: 'Medical Report (if applicable)', type: 'Medical', required: false }
  ],
  other: [
    { name: 'Supporting Document 1', type: 'Other', required: true },
    { name: 'Supporting Document 2', type: 'Other', required: false }
  ]
};

// Claim type display names
export const CLAIM_TYPE_DISPLAY_NAMES: Record<ClaimType, string> = {
  motor_accident: 'Motor Vehicle Accident',
  motor_theft: 'Motor Vehicle Theft',
  motor_damage: 'Motor Vehicle Damage',
  property_damage: 'Property Damage',
  property_theft: 'Property Theft',
  household_damage: 'Household Contents Damage',
  all_risks: 'All Risks Claim',
  medical: 'Medical Claim',
  life: 'Life Insurance Claim',
  other: 'Other Claim'
};

// Claim status display names
export const CLAIM_STATUS_DISPLAY_NAMES: Record<ClaimStatus, string> = {
  draft: 'Draft',
  submitted: 'Submitted',
  under_review: 'Under Review',
  additional_info: 'Additional Info Required',
  approved: 'Approved',
  partially_approved: 'Partially Approved',
  rejected: 'Rejected',
  paid: 'Paid',
  closed: 'Closed'
};
