import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { File, FileText, Eye, Trash2, UploadCloud, FileImage, Tag, AlertCircle } from 'lucide-react-native';
import { Document, PolicyDocument } from './types';
import VerificationStatusBadge from './VerificationStatusBadge';

interface DocumentItemProps {
  document: Document;
  onView?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  onReupload?: (document: Document) => void;
}

const DocumentItem: React.FC<DocumentItemProps> = ({
  document,
  onView,
  onDelete,
  onReupload,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  console.log(`[DocumentItem] Rendering document: ${document.name}, status: ${document.status}`);

  // Determine border color based on status
  let borderColor;
  switch (document.status) {
    case 'verified':
      borderColor = colors.success[500];
      break;
    case 'pending':
      borderColor = colors.warning[500];
      break;
    case 'rejected':
      borderColor = colors.error[500];
      break;
    default:
      borderColor = colors.border;
  }

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderLeftWidth: 4,
      borderLeftColor: borderColor,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start', // Align to top instead of center
      marginBottom: spacing.sm,
    },
    titleContainer: {
      flex: 1,
      marginRight: spacing.sm, // Add margin to prevent overlap with status badge
    },
    name: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      flexWrap: 'wrap', // Allow text to wrap
      flex: 1, // Take up available space
    },
    type: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: spacing.xs,
      flexWrap: 'wrap', // Allow text to wrap
    },
    fileName: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: spacing.xs,
      flexWrap: 'wrap', // Allow text to wrap
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.sm,
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    dateText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginLeft: spacing.xs,
    },
    reasonContainer: {
      backgroundColor: colors.error[50],
      padding: spacing.sm,
      borderRadius: borders.radius.md,
      marginTop: spacing.sm,
    },
    reasonText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.error[700],
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.md,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.xs,
      marginLeft: spacing.sm,
    },
    actionText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.xs,
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${colors.primary[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      marginRight: spacing.xs,
      marginBottom: spacing.xs,
    },
    tagText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.primary[500],
      marginLeft: 2,
    },
    importanceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: spacing.md,
    },
    importanceHigh: {
      backgroundColor: `${colors.error[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    importanceMedium: {
      backgroundColor: `${colors.warning[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    importanceLow: {
      backgroundColor: `${colors.success[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    importanceText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: 2,
    },
    importanceTextHigh: {
      color: colors.error[500],
    },
    importanceTextMedium: {
      color: colors.warning[500],
    },
    importanceTextLow: {
      color: colors.success[500],
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.name}>{document.name}</Text>
          <Text style={styles.type}>{document.type}</Text>
          {document.fileName && (
            <Text style={styles.fileName}>File: {document.fileName}</Text>
          )}
        </View>
        <VerificationStatusBadge status={document.status} />
      </View>

      <View style={styles.infoRow}>
        <View style={styles.dateContainer}>
          {document.fileType === 'pdf' ? (
            <FileText size={14} color={colors.primary[500]} />
          ) : document.fileType === 'image' || document.isImage ? (
            <FileImage size={14} color={colors.primary[500]} />
          ) : (
            <File size={14} color={colors.primary[500]} />
          )}
          <Text style={styles.dateText}>Submitted: {document.date}</Text>
        </View>

        {/* Display importance indicator if available */}
        {'importance' in document && document.importance && (
          <View style={styles.importanceContainer}>
            <View
              style={
                document.importance === 'high'
                  ? styles.importanceHigh
                  : document.importance === 'medium'
                    ? styles.importanceMedium
                    : styles.importanceLow
              }
            >
              <AlertCircle
                size={12}
                color={
                  document.importance === 'high'
                    ? colors.error[500]
                    : document.importance === 'medium'
                      ? colors.warning[500]
                      : colors.success[500]
                }
              />
              <Text
                style={[
                  styles.importanceText,
                  document.importance === 'high'
                    ? styles.importanceTextHigh
                    : document.importance === 'medium'
                      ? styles.importanceTextMedium
                      : styles.importanceTextLow
                ]}
              >
                {document.importance.charAt(0).toUpperCase() + document.importance.slice(1)} Priority
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Display tags if available */}
      {document.tags && document.tags.length > 0 && (
        <View style={styles.tagContainer}>
          {document.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Tag size={12} color={colors.primary[500]} />
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
      )}

      {document.reason && (
        <View style={styles.reasonContainer}>
          <Text style={styles.reasonText}>Reason: {document.reason}</Text>
        </View>
      )}

      <View style={styles.actionsContainer}>
        {onView && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onView(document)}
          >
            <Eye size={16} color={colors.primary[500]} />
            <Text style={styles.actionText}>View</Text>
          </TouchableOpacity>
        )}

        {onDelete && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onDelete(document)}
          >
            <Trash2 size={16} color={colors.error[500]} />
            <Text style={[styles.actionText, { color: colors.error[500] }]}>Delete</Text>
          </TouchableOpacity>
        )}

        {onReupload && document.status === 'rejected' && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onReupload(document)}
          >
            <UploadCloud size={16} color={colors.primary[500]} />
            <Text style={styles.actionText}>Re-upload</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default DocumentItem;
