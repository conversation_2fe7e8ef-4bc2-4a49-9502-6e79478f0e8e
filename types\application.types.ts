// Application status types
export type ApplicationStatus =
  | 'quote_accepted'      // Initial status when quote is accepted
  | 'submitted'           // Application submitted
  | 'payment_pending'     // Waiting for payment
  | 'payment_verified'    // Payment has been verified
  | 'underwriting'        // In underwriting process
  | 'additional_info'     // Additional information requested
  | 'approved'            // Application approved
  | 'approved_with_terms' // Approved with modified terms
  | 'policy_issued'       // Policy has been issued
  | 'rejected';           // Application rejected

// Document status types
export type DocumentStatus = 'pending' | 'verified' | 'rejected';

// Payment method types
export type PaymentMethod = 'eft' | 'direct_debit' | 'none';

// Payment status types
export type PaymentStatus =
  | 'not_started'    // No payment initiated
  | 'pending'        // Payment initiated but not verified
  | 'verified'       // Payment verified
  | 'failed';        // Payment failed

// Timeline action interface
export interface TimelineAction {
  label: string;
  action: string;
  route?: string;
  params?: Record<string, string>;
  icon?: string;
  priority?: 'high' | 'medium' | 'low';
}

// Timeline step interface
export interface TimelineStep {
  id: string;
  title: string;
  description: string;
  date: string;
  status: 'completed' | 'current' | 'upcoming' | 'rejected' | 'warning' | 'info' | 'in_progress';
  icon?: string;
  actions?: TimelineAction[];
  details?: string;
  progress?: number; // Progress percentage (0-100)
  subSteps?: Array<{
    id: string;
    title: string;
    description?: string;
    status: 'completed' | 'current' | 'upcoming' | 'rejected' | 'warning' | 'in_progress';
    date?: string;
    icon?: string;
    details?: string;
  }>;
  estimatedCompletionDate?: string;
  assignedTo?: string;
}

// Document interface
export interface Document {
  id: string;
  name: string;
  type: string;
  status: DocumentStatus;
  date: string;
  verificationDate?: string;
  rejectionReason?: string;
  required: boolean;
  documentId?: string; // Reference to document in document system
}

// Payment details interface
export interface PaymentDetails {
  method: PaymentMethod;
  status: PaymentStatus;
  amount: number;
  currency: string;
  dueDate?: string;
  paidDate?: string;
  reference?: string;
  proofOfPaymentId?: string; // Reference to uploaded proof of payment
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    branchCode: string;
    reference: string;
  };
  debitOrderDetails?: {
    accountHolder: string;
    accountNumber: string;
    bankName: string;
    accountType: string;
    debitDay: number;
    reference: string;
    authorized: boolean;
  };
}

// Underwriting details interface
export interface UnderwritingDetails {
  status: 'pending' | 'in_progress' | 'completed' | 'additional_info';
  assignedTo?: string;
  startDate?: string;
  completionDate?: string;
  notes?: string[];
  decision?: 'approved' | 'approved_with_terms' | 'rejected';
  decisionReason?: string;
  modifiedTerms?: string[];
}

// Required action interface
export interface RequiredAction {
  id: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  dueDate?: string;
  completed: boolean;
}

// Application interface
export interface Application {
  id: string;
  quoteId: string;
  type: string;
  status: ApplicationStatus;
  reference: string;
  date: string;
  premium: number;
  currency: string;
  coverAmount: number;
  clientInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address?: string;
    idNumber?: string;
  };
  documents: Document[];
  timeline: TimelineStep[];
  payment: PaymentDetails;
  underwriting?: UnderwritingDetails;
  policyNumber?: string;
  policyIssueDate?: string;
  policyDocumentId?: string;
  notes?: string[];
  requiredActions?: RequiredAction[];
}
