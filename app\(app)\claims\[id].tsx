import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Claim, ClaimDocument } from '@/types/claim.types';
import useClaimStore from '@/store/claimStore';
import ClaimDetailView from '@/components/claims/ClaimDetailView';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';

export default function ClaimDetailScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const claimId = params.id as string;
  
  // Get claim store methods
  const {
    getClaimById,
    updateClaim,
    submitClaim,
    uploadClaimDocument,
    isLoading,
    error,
    currentClaim
  } = useClaimStore();
  
  // State
  const [claim, setClaim] = useState<Claim | null>(null);
  
  // Fetch claim on mount
  useEffect(() => {
    const fetchClaim = async () => {
      const fetchedClaim = await getClaimById(claimId);
      if (fetchedClaim) {
        setClaim(fetchedClaim);
      } else {
        // Handle claim not found
        showToast(
          'error',
          'Claim Not Found',
          'The requested claim could not be found',
          { visibilityTime: 3000 }
        );
        router.back();
      }
    };
    
    fetchClaim();
  }, [claimId]);
  
  // Update claim when currentClaim changes
  useEffect(() => {
    if (currentClaim && currentClaim.id === claimId) {
      setClaim(currentClaim);
    }
  }, [currentClaim]);
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.sm,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    content: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    errorText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.error[500],
      textAlign: 'center',
      marginTop: spacing.md,
    },
  });
  
  // Handle update claim
  const handleUpdateClaim = async (id: string, updates: Partial<Claim>) => {
    try {
      await updateClaim(id, updates);
    } catch (error) {
      console.error('Error updating claim:', error);
      showToast(
        'error',
        'Update Error',
        'Failed to update claim. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };
  
  // Handle submit claim
  const handleSubmitClaim = async (id: string) => {
    try {
      await submitClaim(id);
    } catch (error) {
      console.error('Error submitting claim:', error);
      showToast(
        'error',
        'Submission Error',
        'Failed to submit claim. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };
  
  // Handle upload document
  const handleUploadDocument = async (claimId: string, document: any) => {
    try {
      await uploadClaimDocument(claimId, document);
    } catch (error) {
      console.error('Error uploading document:', error);
      showToast(
        'error',
        'Upload Error',
        'Failed to upload document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };
  
  // Handle view document
  const handleViewDocument = async (document: ClaimDocument) => {
    // In a real app, this would fetch the document from the server
    // For now, we'll just show a toast
    showToast(
      'info',
      'View Document',
      `Viewing document: ${document.name}`,
      { visibilityTime: 3000 }
    );
    
    // Simulate opening a document
    try {
      // In a real app, you would download the document from the server
      // and then open it with the appropriate viewer
      
      // For now, let's just pick a document to simulate viewing
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });
      
      if (result.canceled) {
        return;
      }
      
      // Open the document
      const fileUri = result.assets[0].uri;
      await Sharing.shareAsync(fileUri);
    } catch (error) {
      console.error('Error viewing document:', error);
      showToast(
        'error',
        'View Error',
        'Failed to view document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };
  
  // Handle share claim
  const handleShareClaim = async (claim: Claim) => {
    try {
      // Create a text summary of the claim
      const claimSummary = `
Claim Reference: ${claim.reference}
Type: ${claim.type}
Status: ${claim.status}
Date: ${claim.date}
Incident Date: ${claim.incidentDate}
Claim Amount: ${claim.claimAmount} ${claim.currency}
${claim.approvedAmount ? `Approved Amount: ${claim.approvedAmount} ${claim.currency}` : ''}

Description:
${claim.description}
      `;
      
      // Share the claim summary
      await Share.share({
        message: claimSummary,
        title: `Claim ${claim.reference}`,
      });
    } catch (error) {
      console.error('Error sharing claim:', error);
      showToast(
        'error',
        'Share Error',
        'Failed to share claim. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };
  
  // Render content based on loading/error state
  const renderContent = () => {
    if (isLoading || !claim) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      );
    }
    
    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      );
    }
    
    return (
      <ClaimDetailView
        claim={claim}
        onUpdateClaim={handleUpdateClaim}
        onSubmitClaim={handleSubmitClaim}
        onUploadDocument={handleUploadDocument}
        onViewDocument={handleViewDocument}
        onShareClaim={handleShareClaim}
      />
    );
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          Claim Details
        </Text>
      </View>
      
      <View style={styles.content}>
        {renderContent()}
      </View>
      
      <BottomNavBar currentRoute="claims" />
    </SafeAreaView>
  );
}
