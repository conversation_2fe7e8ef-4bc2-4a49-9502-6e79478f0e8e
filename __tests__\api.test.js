import axios from 'axios';
import Mock<PERSON><PERSON>pter from 'axios-mock-adapter';
import apiService from '../services/api';

// Create a mock for axios
const mockAxios = new MockAdapter(axios);

describe('API Service', () => {
  // Reset mocks before each test
  beforeEach(() => {
    mockAxios.reset();
  });

  // Test health check endpoint
  test('health check returns status ok', async () => {
    // Mock the health check endpoint
    mockAxios.onGet('/api/v1/health').reply(200, { status: 'ok' });

    // Call the health check function
    const response = await apiService.health.check();

    // Verify the response
    expect(response).toEqual({ status: 'ok' });
  });

  // Test authentication endpoints
  describe('Authentication', () => {
    test('login should return user data and tokens', async () => {
      // Mock the login endpoint
      mockAxios.onPost('/api/v1/login').reply(200, {
        access_token: 'test-token',
        refresh_token: 'test-refresh-token',
        user: {
          id: '123',
          email: '<EMAIL>',
          userType: 'individual',
        },
      });

      // Call the login function
      const response = await apiService.auth.login('<EMAIL>', 'password');

      // Verify the response
      expect(response).toHaveProperty('access_token');
      expect(response).toHaveProperty('refresh_token');
      expect(response).toHaveProperty('user');
      expect(response.user).toHaveProperty('id');
      expect(response.user).toHaveProperty('email');
    });

    test('getCurrentUser should return user data', async () => {
      // Mock the get current user endpoint
      mockAxios.onGet('/api/v1/user/me').reply(200, {
        id: '123',
        email: '<EMAIL>',
        userType: 'individual',
        firstName: 'Test',
        lastName: 'User',
      });

      // Call the getCurrentUser function
      const response = await apiService.auth.getCurrentUser();

      // Verify the response
      expect(response).toHaveProperty('id');
      expect(response).toHaveProperty('email');
      expect(response).toHaveProperty('userType');
      expect(response).toHaveProperty('firstName');
      expect(response).toHaveProperty('lastName');
    });
  });

  // Test claims endpoints
  describe('Claims', () => {
    test('getClaims should return an array of claims', async () => {
      // Mock the get claims endpoint
      mockAxios.onGet('/api/v1/claims').reply(200, [
        {
          id: 'claim1',
          policyId: 'policy1',
          type: 'motor',
          status: 'pending',
          reference: 'CLM-123456',
        },
        {
          id: 'claim2',
          policyId: 'policy2',
          type: 'property',
          status: 'approved',
          reference: 'CLM-789012',
        },
      ]);

      // Call the getClaims function
      const response = await apiService.claims.getClaims();

      // Verify the response
      expect(Array.isArray(response)).toBe(true);
      expect(response.length).toBe(2);
      expect(response[0]).toHaveProperty('id');
      expect(response[0]).toHaveProperty('policyId');
      expect(response[0]).toHaveProperty('type');
      expect(response[0]).toHaveProperty('status');
      expect(response[0]).toHaveProperty('reference');
    });

    test('getClaimById should return a single claim', async () => {
      // Mock the get claim by id endpoint
      mockAxios.onGet('/api/v1/claims/claim1').reply(200, {
        id: 'claim1',
        policyId: 'policy1',
        type: 'motor',
        status: 'pending',
        reference: 'CLM-123456',
        description: 'Car accident',
        incidentDate: '2023-01-01',
      });

      // Call the getClaimById function
      const response = await apiService.claims.getClaimById('claim1');

      // Verify the response
      expect(response).toHaveProperty('id', 'claim1');
      expect(response).toHaveProperty('policyId', 'policy1');
      expect(response).toHaveProperty('type', 'motor');
      expect(response).toHaveProperty('status', 'pending');
      expect(response).toHaveProperty('reference', 'CLM-123456');
      expect(response).toHaveProperty('description', 'Car accident');
      expect(response).toHaveProperty('incidentDate', '2023-01-01');
    });

    test('createClaim should create a new claim', async () => {
      // Mock the create claim endpoint
      mockAxios.onPost('/api/v1/claims').reply(201, {
        id: 'new-claim',
        policyId: 'policy1',
        type: 'motor',
        status: 'draft',
        reference: 'CLM-654321',
      });

      // Create claim data
      const claimData = {
        policyId: 'policy1',
        type: 'motor',
        description: 'Car accident',
        incidentDate: '2023-01-01',
      };

      // Call the createClaim function
      const response = await apiService.claims.createClaim(claimData);

      // Verify the response
      expect(response).toHaveProperty('id', 'new-claim');
      expect(response).toHaveProperty('policyId', 'policy1');
      expect(response).toHaveProperty('type', 'motor');
      expect(response).toHaveProperty('status', 'draft');
      expect(response).toHaveProperty('reference', 'CLM-654321');
    });
  });

  // Test document endpoints
  describe('Documents', () => {
    test('getAllDocuments should return an array of documents', async () => {
      // Mock the get all documents endpoint
      mockAxios.onGet('/api/v1/documents').reply(200, [
        {
          id: 'doc1',
          name: 'ID Document',
          type: 'id_document',
          status: 'verified',
        },
        {
          id: 'doc2',
          name: 'Proof of Address',
          type: 'proof_of_address',
          status: 'pending',
        },
      ]);

      // Call the getAllDocuments function
      const response = await apiService.documents.getAllDocuments();

      // Verify the response
      expect(Array.isArray(response)).toBe(true);
      expect(response.length).toBe(2);
      expect(response[0]).toHaveProperty('id');
      expect(response[0]).toHaveProperty('name');
      expect(response[0]).toHaveProperty('type');
      expect(response[0]).toHaveProperty('status');
    });
  });
});
