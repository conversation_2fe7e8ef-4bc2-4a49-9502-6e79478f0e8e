import apiService from '@/services/api';
import { showToast } from './toast';

/**
 * Performs a health check on the API to ensure connectivity
 * @returns {Promise<boolean>} True if the API is healthy, false otherwise
 */
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    console.log('Performing API health check...');
    const response = await apiService.health.check();
    
    if (response && response.status === 'ok') {
      console.log('API health check successful:', response);
      return true;
    } else {
      console.warn('API health check returned unexpected response:', response);
      return false;
    }
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
};

/**
 * Performs a health check and shows a toast notification with the result
 * @param {boolean} showSuccessToast Whether to show a success toast if the check passes
 * @returns {Promise<boolean>} True if the API is healthy, false otherwise
 */
export const checkApiHealthWithToast = async (showSuccessToast = false): Promise<boolean> => {
  try {
    const isHealthy = await checkApiHealth();
    
    if (isHealthy) {
      if (showSuccessToast) {
        showToast(
          'success',
          'Connection Successful',
          'Connected to the Inerca backend server',
          { visibilityTime: 3000 }
        );
      }
      return true;
    } else {
      showToast(
        'error',
        'Connection Failed',
        'Could not connect to the Inerca backend server. Some features may not work properly.',
        { visibilityTime: 4000 }
      );
      return false;
    }
  } catch (error) {
    showToast(
      'error',
      'Connection Error',
      'An error occurred while checking the connection to the backend server',
      { visibilityTime: 4000 }
    );
    return false;
  }
};

/**
 * Performs a health check and returns a detailed status object
 * @returns {Promise<{isHealthy: boolean, message: string, details?: any}>} Health check status
 */
export const getApiHealthStatus = async (): Promise<{
  isHealthy: boolean;
  message: string;
  details?: any;
}> => {
  try {
    const response = await apiService.health.check();
    
    if (response && response.status === 'ok') {
      return {
        isHealthy: true,
        message: 'API is healthy',
        details: response
      };
    } else {
      return {
        isHealthy: false,
        message: 'API returned unexpected response',
        details: response
      };
    }
  } catch (error) {
    return {
      isHealthy: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      details: error
    };
  }
};

export default {
  checkApiHealth,
  checkApiHealthWithToast,
  getApiHealthStatus
};
