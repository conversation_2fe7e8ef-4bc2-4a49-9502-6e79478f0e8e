import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSelector, useDispatch } from 'react-redux';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  Bell,
  CheckCircle,
  Info,
  AlertTriangle,
  AlertCircle,
  Trash2,
  CheckSquare,
  FileText,
  Settings
} from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { RootState, AppDispatch } from '@/store/store';
import {
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  Notification,
} from '@/store/notificationSlice';
import { formatDistanceToNow } from 'date-fns';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';

export default function NotificationsScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const dispatch = useDispatch<AppDispatch>();
  const { notifications, unreadCount, isLoading } = useSelector((state: RootState) => state.notification);
  const [refreshing, setRefreshing] = useState(false);

  // Load notifications on mount
  useEffect(() => {
    dispatch(fetchNotifications());
  }, [dispatch]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchNotifications());
    setRefreshing(false);
  };

  // Handle notification press
  const handleNotificationPress = async (notification: Notification) => {
    if (!notification.read) {
      dispatch(markAsRead(notification.id));
    }

    // Handle different notification types
    switch (notification.type) {
      case 'policy':
        if (notification.actionData?.policyId) {
          router.push({
            pathname: '/policies/[id]',
            params: { id: notification.actionData.policyId }
          });
        }
        break;
      case 'claim':
        if (notification.actionData?.claimId) {
          router.push({
            pathname: '/claims/[id]',
            params: { id: notification.actionData.claimId }
          });
        }
        break;
      case 'application':
        if (notification.actionData?.applicationId) {
          router.push({
            pathname: '/applications/[id]',
            params: { id: notification.actionData.applicationId }
          });
        }
        break;
      case 'document':
        router.push('/documents');
        break;
      default:
        // If notification has an action route, navigate to it
        if (notification.actionRoute) {
          router.push(notification.actionRoute);
        }
        break;
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) {
      showToast(
        'info',
        'No Unread Notifications',
        'All notifications are already read',
        { visibilityTime: 2000 }
      );
      return;
    }

    dispatch(markAllAsRead());
  };

  // Handle delete notification
  const handleDeleteNotification = async (id: string) => {
    dispatch(deleteNotification(id));
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info size={24} color={colors.info[500]} />;
      case 'success':
        return <CheckCircle size={24} color={colors.success[500]} />;
      case 'warning':
        return <AlertTriangle size={24} color={colors.warning[500]} />;
      case 'error':
        return <AlertCircle size={24} color={colors.error[500]} />;
      case 'policy':
        return <FileText size={24} color={colors.primary[500]} />;
      case 'claim':
        return <FileText size={24} color={colors.secondary[500]} />;
      case 'application':
        return <FileText size={24} color={colors.info[500]} />;
      case 'document':
        return <FileText size={24} color={colors.neutral[500]} />;
      default:
        return <Bell size={24} color={colors.primary[500]} />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Render notification item
  const renderNotificationItem = ({ item, index }: { item: Notification; index: number }) => (
    <Animated.View
      entering={FadeInDown.delay(index * 100).springify()}
      style={[
        styles.notificationItem,
        { backgroundColor: item.read ? colors.card : `${colors.primary[500]}10` }
      ]}
    >
      <TouchableOpacity
        style={styles.notificationContent}
        onPress={() => handleNotificationPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.notificationIcon}>
          {getNotificationIcon(item.type)}
        </View>
        <View style={styles.notificationTextContainer}>
          <Text style={[styles.notificationTitle, { color: colors.text }]}>
            {item.title}
          </Text>
          <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>
            {item.message}
          </Text>
          <Text style={[styles.notificationTime, { color: colors.textSecondary }]}>
            {formatDate(item.createdAt)}
          </Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteNotification(item.id)}
      >
        <Trash2 size={20} color={colors.error[500]} />
      </TouchableOpacity>
    </Animated.View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Bell size={48} color={colors.textSecondary} />
      <Text style={[styles.emptyText, { color: colors.text }]}>
        No Notifications
      </Text>
      <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
        You don't have any notifications yet
      </Text>
    </View>
  );

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    settingsButton: {
      marginRight: spacing.md,
      padding: spacing.xs,
    },
    markAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
    },
    markAllButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
      marginLeft: spacing.xs,
    },
    content: {
      flex: 1,
    },
    notificationItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    notificationContent: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    notificationIcon: {
      marginRight: spacing.md,
    },
    notificationTextContainer: {
      flex: 1,
    },
    notificationTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginBottom: spacing.xs,
    },
    notificationMessage: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      marginBottom: spacing.xs,
    },
    notificationTime: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
    },
    deleteButton: {
      padding: spacing.sm,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      marginTop: spacing.md,
    },
    emptySubtext: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      marginTop: spacing.sm,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => router.push('/notifications/preferences')}
          >
            <Settings size={20} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.markAllButton} onPress={handleMarkAllAsRead}>
            <CheckSquare size={16} color={colors.white} />
            <Text style={styles.markAllButtonText}>Mark All Read</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.content}>
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
          </View>
        ) : (
          <FlatList
            data={notifications}
            renderItem={renderNotificationItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ flexGrow: 1 }}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[colors.primary[500]]}
                tintColor={colors.primary[500]}
              />
            }
          />
        )}
      </View>

      <BottomNavBar />
    </SafeAreaView>
  );
}
