import React, { useEffect } from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchProfile } from '@/store/profileSlice';
import ProfileForm from '@/components/profile/ProfileForm';

export default function EditProfileScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  // Get profile state
  const profile = useAppSelector(state => state.profile.profile);
  const isLoading = useAppSelector(state => state.profile.isLoading);
  const error = useAppSelector(state => state.profile.error);

  // Only fetch profile if it doesn't exist and there's no error
  useEffect(() => {
    if (!profile && !isLoading && !error) {
      console.log('[EditProfile] Fetching profile data');
      dispatch(fetchProfile());
    }
  }, [profile, isLoading, error, dispatch]);

  // Handle successful profile update
  const handleSuccess = () => {
    console.log('[EditProfile] Profile updated successfully');
    router.back();
  };

  // Handle cancel
  const handleCancel = () => {
    console.log('[EditProfile] Profile edit cancelled');
    router.back();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: 8,
      marginRight: 8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    content: {
      flex: 1,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleCancel}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
      </View>
      <View style={styles.content}>
        <ProfileForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </View>
    </SafeAreaView>
  );
}
