import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ChevronRight } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Claim, CLAIM_TYPE_DISPLAY_NAMES } from '@/types/claim.types';
import { formatCurrency } from '@/utils/quoteCalculations';
import ClaimStatusBadge from './ClaimStatusBadge';

interface ClaimCardProps {
  claim: Claim;
  onPress: (claim: Claim) => void;
  index?: number;
}

const ClaimCard: React.FC<ClaimCardProps> = ({
  claim,
  onPress,
  index = 0,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders, shadows } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...shadows.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    infoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    progressContainer: {
      marginVertical: spacing.sm,
    },
    progressBackground: {
      height: 8,
      backgroundColor: colors.neutral[200],
      borderRadius: 4,
    },
    progressBar: {
      height: 8,
      borderRadius: 4,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacing.sm,
    },
    pendingActions: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.warning[500],
    },
    viewDetailsButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    viewDetailsText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginRight: spacing.xs,
    },
  });

  // Calculate progress percentage based on claim status
  const getProgressPercentage = () => {
    switch (claim.status) {
      case 'draft':
        return 10;
      case 'submitted':
        return 25;
      case 'under_review':
        return 50;
      case 'additional_info':
        return 40;
      case 'approved':
      case 'partially_approved':
        return 75;
      case 'paid':
        return 90;
      case 'closed':
      case 'rejected':
        return 100;
      default:
        return 0;
    }
  };

  // Get progress bar color based on claim status
  const getProgressColor = () => {
    switch (claim.status) {
      case 'approved':
      case 'paid':
      case 'closed':
        return colors.success[500];
      case 'partially_approved':
        return colors.info[500];
      case 'rejected':
        return colors.error[500];
      case 'additional_info':
        return colors.warning[500];
      default:
        return colors.primary[500];
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Count pending actions
  const pendingActions = claim.requiredActions?.filter(action => !action.completed).length || 0;

  // Get claim type display name
  const claimTypeDisplay = CLAIM_TYPE_DISPLAY_NAMES[claim.type] || claim.type.replace(/_/g, ' ');

  return (
    <Animated.View
      entering={FadeInDown.delay(100 + index * 100).springify()}
    >
      <TouchableOpacity
        style={styles.container}
        onPress={() => onPress(claim)}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <Text style={styles.title}>{claimTypeDisplay}</Text>
          <ClaimStatusBadge status={claim.status} size="small" />
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Reference</Text>
          <Text style={styles.infoValue}>{claim.reference}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Incident Date</Text>
          <Text style={styles.infoValue}>{formatDate(claim.incidentDate)}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Claim Amount</Text>
          <Text style={styles.infoValue}>{formatCurrency(claim.claimAmount, claim.currency)}</Text>
        </View>

        {claim.approvedAmount !== undefined && (
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Approved Amount</Text>
            <Text style={styles.infoValue}>{formatCurrency(claim.approvedAmount, claim.currency)}</Text>
          </View>
        )}

        <View style={styles.progressContainer}>
          <View style={styles.progressBackground}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${getProgressPercentage()}%`,
                  backgroundColor: getProgressColor(),
                },
              ]}
            />
          </View>
        </View>

        <View style={styles.footer}>
          {pendingActions > 0 ? (
            <Text style={styles.pendingActions}>
              {pendingActions} action{pendingActions > 1 ? 's' : ''} required
            </Text>
          ) : (
            <View />
          )}
          <View style={styles.viewDetailsButton}>
            <Text style={styles.viewDetailsText}>View Details</Text>
            <ChevronRight size={16} color={colors.primary[500]} />
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default ClaimCard;
