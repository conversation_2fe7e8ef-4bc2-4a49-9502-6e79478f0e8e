import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  UserProfileCreate,
  UserProfileUpdate,
  UserProfilePublic,
  IDType,
  ProfileStatus
} from '@/types/backend';

// Profile state interface
interface ProfileState {
  profile: UserProfilePublic | null;
  isLoading: boolean;
  error: string | null;
  isProfileComplete: boolean;
}

// Initial state
const initialState: ProfileState = {
  profile: null,
  isLoading: false,
  error: null,
  isProfileComplete: false,
};

// Async thunks
export const fetchProfile = createAsyncThunk(
  'profile/fetch',
  async (_, { rejectWithValue }) => {
    try {
      console.log('[Profile Slice] Fetching user profile');
      const { apiService } = await import('@/services/api');
      const profile = await apiService.userProfile.getProfile();
      console.log('[Profile Slice] Profile fetched successfully:', profile);
      return profile;
    } catch (error: any) {
      console.error('[Profile Slice] Fetch profile error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to fetch profile';
      console.error('[Profile Slice] Error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const createProfile = createAsyncThunk(
  'profile/create',
  async (profileData: UserProfileCreate, { rejectWithValue }) => {
    try {
      console.log('[Profile Slice] Creating profile with data:', profileData);
      const { apiService } = await import('@/services/api');
      const profile = await apiService.userProfile.createProfile(profileData);
      console.log('[Profile Slice] Profile created successfully:', profile);
      return profile;
    } catch (error: any) {
      console.error('[Profile Slice] Create profile error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to create profile';
      console.error('[Profile Slice] Error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateProfile = createAsyncThunk(
  'profile/update',
  async (profileData: UserProfileUpdate, { rejectWithValue }) => {
    try {
      console.log('[Profile Slice] Updating profile with data:', profileData);
      const { apiService } = await import('@/services/api');
      const profile = await apiService.userProfile.updateProfile(profileData);
      console.log('[Profile Slice] Profile updated successfully:', profile);
      return profile;
    } catch (error: any) {
      console.error('[Profile Slice] Update profile error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to update profile';
      console.error('[Profile Slice] Error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Helper function to check if profile is complete
const checkProfileComplete = (profile: UserProfilePublic | null): boolean => {
  if (!profile) return false;

  // Required fields for a complete profile (using correct field names)
  const requiredFields = [
    'other_names',
    'surname_name',
    'date_of_birth',
    'postal_address',
    'verifiable_id',
    'id_type',
    'occupation'
  ];

  return requiredFields.every(field => {
    const value = profile[field as keyof UserProfilePublic];
    return value !== null && value !== undefined && value !== '';
  });
};

// Profile slice
const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearProfile: (state) => {
      state.profile = null;
      state.isProfileComplete = false;
    },
  },
  extraReducers: (builder) => {
    // Fetch profile
    builder.addCase(fetchProfile.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchProfile.fulfilled, (state, action) => {
      state.isLoading = false;
      state.profile = action.payload;
      state.isProfileComplete = checkProfileComplete(action.payload);
    });
    builder.addCase(fetchProfile.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Create profile
    builder.addCase(createProfile.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(createProfile.fulfilled, (state, action) => {
      state.isLoading = false;
      state.profile = action.payload;
      state.isProfileComplete = checkProfileComplete(action.payload);
    });
    builder.addCase(createProfile.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Update profile
    builder.addCase(updateProfile.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(updateProfile.fulfilled, (state, action) => {
      state.isLoading = false;
      state.profile = action.payload;
      state.isProfileComplete = checkProfileComplete(action.payload);
    });
    builder.addCase(updateProfile.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
  },
});

export const { clearError, clearProfile } = profileSlice.actions;
export default profileSlice.reducer;

// Selectors
export const selectProfile = (state: { profile: ProfileState }) => state.profile.profile;
export const selectIsProfileLoading = (state: { profile: ProfileState }) => state.profile.isLoading;
export const selectProfileError = (state: { profile: ProfileState }) => state.profile.error;
export const selectIsProfileComplete = (state: { profile: ProfileState }) => state.profile.isProfileComplete;
