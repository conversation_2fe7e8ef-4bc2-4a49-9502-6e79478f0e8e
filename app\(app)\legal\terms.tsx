import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';

export default function TermsAndConditionsScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Terms & Conditions</Text>
      </View>

      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View entering={FadeInDown.delay(100).springify()}>
          <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
            Last Updated: April 16, 2025
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            1. Acceptance of Terms
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            By accessing or using the Inerca Holdings Mobile Application ("App"), you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use the App.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            2. Description of Services
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            The App provides insurance quotation services, document submission, payment confirmation, claims processing, and policy management for Inerca Holdings clients in Botswana.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            3. User Accounts
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            3.1. You must register for an account to access certain features of the App.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            3.2. You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            3.3. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            4. User Conduct
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            4.1. You agree not to use the App for any illegal or unauthorized purpose.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            4.2. You agree not to modify, adapt, or hack the App or modify another website so as to falsely imply that it is associated with the App.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            5. Insurance Quotes
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            5.1. Insurance quotes provided through the App are estimates based on the information you provide.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            5.2. Final insurance premiums may vary based on underwriting and verification of submitted information.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            5.3. Quotes are valid for the period specified in the quotation.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            6. Document Submission
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            6.1. You are responsible for the accuracy and authenticity of all documents submitted through the App.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            6.2. Inerca Holdings reserves the right to reject documents that do not meet verification requirements.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            7. Payment Confirmation
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            7.1. Payment confirmations submitted through the App must be accurate and verifiable.
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            7.2. Inerca Holdings is not responsible for any delays in policy issuance due to incorrect payment information.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            8. Intellectual Property
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            The App and its original content, features, and functionality are owned by Inerca Holdings and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            9. Limitation of Liability
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            In no event shall Inerca Holdings, its officers, directors, employees, or agents be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the App.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            10. Governing Law
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            These Terms shall be governed by the laws of Botswana, without respect to its conflict of law principles.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            11. Changes to Terms
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Inerca Holdings reserves the right to modify or replace these Terms at any time. It is your responsibility to check these Terms periodically for changes.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            12. Contact Us
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            If you have any questions about these Terms, please contact <NAME_EMAIL>.
          </Text>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  lastUpdated: {
    fontSize: 14,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
});
