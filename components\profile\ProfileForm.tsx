import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  UserProfileCreate,
  UserProfileUpdate,
  IDType,
  UserProfilePublic
} from '@/types/backend';
import { createProfile, updateProfile, selectProfile } from '@/store/profileSlice';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import Button from '@/components/ui/Button';
import { User, MapPin, Calendar, CreditCard, Briefcase } from 'lucide-react-native';

interface ProfileFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ onSuccess, onCancel }) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  const user = useAppSelector(state => state.auth.user);
  const existingProfile = useAppSelector(selectProfile);
  const isLoading = useAppSelector(state => state.profile.isLoading);

  const isEditing = !!existingProfile;

  // Form state
  const [formData, setFormData] = useState<UserProfileCreate>({
    other_names: existingProfile?.other_names || user?.firstName || '',
    surname_name: existingProfile?.surname_name || user?.lastName || '',
    date_of_birth: existingProfile?.date_of_birth || user?.dateOfBirth || '',
    postal_address: existingProfile?.postal_address || user?.postalAddress || user?.address || '',
    verifiable_id: existingProfile?.verifiable_id || user?.idNumber || '',
    id_type: existingProfile?.id_type || IDType.NATIONAL,
    occupation: existingProfile?.occupation || user?.occupation || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Options for select fields
  const idTypeOptions: SelectOption[] = [
    { value: IDType.NATIONAL, label: 'National ID' },
    { value: IDType.PASSPORT, label: 'Passport' },
  ];



  const updateFormField = (field: keyof UserProfileCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.other_names.trim()) newErrors.other_names = 'First name is required';
    if (!formData.surname_name.trim()) newErrors.surname_name = 'Last name is required';
    if (!formData.verifiable_id.trim()) newErrors.verifiable_id = 'ID number is required';
    if (!formData.postal_address.trim()) newErrors.postal_address = 'Postal address is required';
    if (!formData.date_of_birth) newErrors.date_of_birth = 'Date of birth is required';
    if (!formData.occupation.trim()) newErrors.occupation = 'Occupation is required';

    // Validate ID number format (basic validation)
    if (formData.verifiable_id && formData.verifiable_id.length < 5) {
      newErrors.verifiable_id = 'ID number must be at least 5 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    console.log('[ProfileForm] Submit button pressed');
    console.log('[ProfileForm] Form data:', formData);

    if (!validateForm()) {
      console.log('[ProfileForm] Form validation failed');
      Alert.alert('Validation Error', 'Please fix the errors in the form');
      return;
    }

    console.log('[ProfileForm] Form validation passed');

    try {
      let result;
      if (isEditing) {
        console.log('[ProfileForm] Updating existing profile');
        // Update existing profile
        const updateData: UserProfileUpdate = { ...formData };
        console.log('[ProfileForm] Update data:', updateData);
        result = await dispatch(updateProfile(updateData));
      } else {
        console.log('[ProfileForm] Creating new profile');
        // Create new profile
        console.log('[ProfileForm] Create data:', formData);
        result = await dispatch(createProfile(formData));
      }

      console.log('[ProfileForm] Dispatch result:', result);

      if (result.meta.requestStatus === 'fulfilled') {
        console.log('[ProfileForm] Profile save successful');
        Alert.alert(
          'Success',
          `Profile ${isEditing ? 'updated' : 'created'} successfully`,
          [{ text: 'OK', onPress: onSuccess }]
        );
      } else {
        console.error('[ProfileForm] Profile save failed:', result.payload);
        Alert.alert('Error', result.payload as string || 'Failed to save profile');
      }
    } catch (error) {
      console.error('[ProfileForm] Profile save error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      padding: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: spacing.md,
      marginTop: spacing.xl,
      marginBottom: spacing.xl,
    },
    button: {
      flex: 1,
    },
  });

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Personal Information */}
        <Text style={styles.sectionTitle}>Personal Information</Text>

        <DynamicFormField
          label="First Name"
          type="text"
          value={formData.other_names}
          onChange={(value) => updateFormField('other_names', value)}
          placeholder="Enter your first name"
          error={errors.other_names}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Last Name"
          type="text"
          value={formData.surname_name}
          onChange={(value) => updateFormField('surname_name', value)}
          placeholder="Enter your last name"
          error={errors.surname_name}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Date of Birth"
          type="date"
          value={formData.date_of_birth}
          onChange={(value) => updateFormField('date_of_birth', value)}
          placeholder="Select your date of birth"
          error={errors.date_of_birth}
          required
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Postal Address"
          type="textarea"
          value={formData.postal_address}
          onChange={(value) => updateFormField('postal_address', value)}
          placeholder="Enter your postal address"
          error={errors.postal_address}
          required
          icon={<MapPin size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="ID Type"
          type="select"
          value={formData.id_type}
          onChange={(value) => updateFormField('id_type', value)}
          options={idTypeOptions}
          icon={<CreditCard size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="ID Number"
          type="text"
          value={formData.verifiable_id}
          onChange={(value) => updateFormField('verifiable_id', value)}
          placeholder="Enter your ID number"
          error={errors.verifiable_id}
          required
          icon={<CreditCard size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Occupation"
          type="text"
          value={formData.occupation}
          onChange={(value) => updateFormField('occupation', value)}
          placeholder="Enter your occupation"
          error={errors.occupation}
          required
          icon={<Briefcase size={20} color={colors.textSecondary} />}
        />

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          {onCancel && (
            <Button
              title="Cancel"
              variant="outline"
              size="medium"
              onPress={onCancel}
              style={styles.button}
            />
          )}
          <Button
            title={isLoading ? 'Saving...' : (isEditing ? 'Update Profile' : 'Create Profile')}
            variant="primary"
            size="medium"
            onPress={handleSubmit}
            disabled={isLoading}
            style={styles.button}
            icon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ProfileForm;
