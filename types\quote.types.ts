// Quote types and interfaces

// Insurance product types
export type InsuranceProductType =
  | 'motor'
  | 'houseowners'
  | 'householdContents'
  | 'allRisks'
  | 'travel'
  | 'health'
  | 'life'
  | 'funeral'
  | 'scheme'
  | 'business';

// Quote status
export type QuoteStatus =
  | 'draft'
  | 'submitted'
  | 'quoted'
  | 'expired'
  | 'converted';

// Base Quote interface
export interface Quote {
  id: string;
  type: InsuranceProductType;
  status: QuoteStatus;
  createdAt: string;
  updatedAt: string;
  expiresAt: string;
  premium?: number;
  coverAmount?: number;
  currency?: string;
  clientInfo: ClientInfo;
  additionalInfo?: Record<string, any>;
  documents?: QuoteDocument[];
  pdfUrl?: string;
}

// Client information
export interface ClientInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  dateOfBirth?: string;
  occupation?: string;
  idNumber?: string;
}

// Quote document
export interface QuoteDocument {
  id: string;
  name: string;
  type: string;
  category?: string;
  required: boolean;
  uploaded: boolean;
  documentId?: string; // Reference to the actual document in the document system
  status?: 'pending' | 'verified' | 'rejected';
  uploadDate?: string;
  verificationDate?: string;
  reason?: string; // Reason for rejection if status is 'rejected'
}

// Motor quote specific information
export interface MotorQuoteInfo {
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: number;
  vehicleValue: number;
  registrationNumber: string;
  engineNumber?: string;
  chassisNumber?: string;
  usage: 'personal' | 'commercial';
  coverType: 'comprehensive' | 'thirdParty' | 'thirdPartyFireAndTheft';
  additionalDrivers?: AdditionalDriver[];
  previousClaims?: PreviousClaim[];
  extras?: InsuranceExtra[];
}

// Home quote specific information
export interface HomeQuoteInfo {
  propertyType: 'house' | 'apartment' | 'condo' | 'other';
  constructionType: string;
  buildingValue: number;
  contentsValue: number;
  address: string;
  yearBuilt?: number;
  securityFeatures?: string[];
  coverType: 'building' | 'contents' | 'both';
  extras?: InsuranceExtra[];
}

// Travel quote specific information
export interface TravelQuoteInfo {
  destination: string;
  departureDate: string;
  returnDate: string;
  travelers: number;
  coverType: 'basic' | 'standard' | 'premium';
  activities?: string[];
  extras?: InsuranceExtra[];
}

// Health quote specific information
export interface HealthQuoteInfo {
  coverType: 'individual' | 'family';
  familyMembers?: FamilyMember[];
  preExistingConditions?: string[];
  coverageLevel: 'basic' | 'standard' | 'premium';
  extras?: InsuranceExtra[];
}

// Houseowners insurance specific information
export interface HouseownersQuoteInfo {
  propertyAddress: string;
  constructionType: string;
  occupancyStatus: 'owner' | 'tenant' | 'vacant';
  buildingValue: number;
  yearBuilt?: number;
  securityFeatures?: string[];
  coverType: 'standard' | 'comprehensive';
  extras?: InsuranceExtra[];
}

// Household contents insurance specific information
export interface HouseholdContentsQuoteInfo {
  propertyAddress: string;
  securityFeatures?: string[];
  contentsValue: number;
  highValueItems?: HighValueItem[];
  powerSurgeProtection: boolean;
  accidentalDamage: boolean;
  extras?: InsuranceExtra[];
}

// All risks insurance specific information
export interface AllRisksQuoteInfo {
  unspecifiedItemsValue: number;
  specifiedItems: SpecifiedItem[];
  extras?: InsuranceExtra[];
}

// Life assurance specific information
export interface LifeQuoteInfo {
  insuredPerson: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: 'male' | 'female' | 'other';
    smoker: boolean;
    occupation: string;
    healthConditions?: string[];
  };
  coverageAmount: number;
  term: number; // in years
  beneficiaries: Beneficiary[];
  extras?: InsuranceExtra[];
}

// Funeral cover specific information
export interface FuneralQuoteInfo {
  insuredPerson: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: 'male' | 'female' | 'other';
  };
  coverageAmount: number;
  familyMembers?: FamilyMember[];
  beneficiaries: Beneficiary[];
  extras?: InsuranceExtra[];
}

// Scheme insurance specific information
export interface SchemeQuoteInfo {
  memberDetails: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    employeeNumber: string;
  };
  employerName: string;
  employerAddress: string;
  schemeType: string;
  supportingForms?: string[];
  extras?: InsuranceExtra[];
}

// Additional driver for motor insurance
export interface AdditionalDriver {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  licenseNumber: string;
  relationship: string;
  drivingExperience: number;
}

// Previous claim for motor insurance
export interface PreviousClaim {
  id: string;
  date: string;
  description: string;
  amount: number;
  faultStatus: 'atFault' | 'notAtFault' | 'pending';
}

// Family member for health insurance
export interface FamilyMember {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  relationship: string;
  preExistingConditions?: string[];
}

// Beneficiary for life/funeral insurance
export interface Beneficiary {
  id: string;
  firstName: string;
  lastName: string;
  relationship: string;
  percentage: number;
  contactNumber?: string;
}

// High value item for household contents
export interface HighValueItem {
  id: string;
  name: string;
  description: string;
  value: number;
  proofOfPurchase?: boolean;
  serialNumber?: string;
}

// Specified item for all risks
export interface SpecifiedItem {
  id: string;
  name: string;
  description: string;
  value: number;
  category: 'electronics' | 'jewelry' | 'sports' | 'musical' | 'other';
  proofOfPurchase?: boolean;
  serialNumber?: string;
}

// Insurance extra/add-on
export interface InsuranceExtra {
  id: string;
  name: string;
  description: string;
  price: number;
  selected: boolean;
}

// Insurance products configuration - will be fetched from API
export const insuranceProducts = [
  {
    id: '1',
    type: 'motor' as InsuranceProductType,
    name: 'Motor Insurance',
    description: 'Comprehensive coverage for your vehicle',
    icon: 'car',
    color: '#4C6FFF',
  },
  {
    id: '2',
    type: 'houseowners' as InsuranceProductType,
    name: 'Houseowners Insurance',
    description: 'Protect your property against damage',
    icon: 'home',
    color: '#FF6B6B',
  },
  {
    id: '3',
    type: 'householdContents' as InsuranceProductType,
    name: 'Household Contents',
    description: 'Coverage for your personal belongings',
    icon: 'home',
    color: '#FF8C42',
  },
  {
    id: '4',
    type: 'allRisks' as InsuranceProductType,
    name: 'All Risks Insurance',
    description: 'Protection for your valuable items',
    icon: 'shield',
    color: '#E84855',
  },
  {
    id: '5',
    type: 'travel' as InsuranceProductType,
    name: 'Travel Insurance',
    description: 'Stay protected during your travels',
    icon: 'plane',
    color: '#46CDCF',
  },
  {
    id: '6',
    type: 'health' as InsuranceProductType,
    name: 'Health Insurance',
    description: 'Medical coverage for you and your family',
    icon: 'heart',
    color: '#3FD2C7',
  },
  {
    id: '7',
    type: 'life' as InsuranceProductType,
    name: 'Life Assurance',
    description: 'Financial security for your loved ones',
    icon: 'shield',
    color: '#6C63FF',
  },
  {
    id: '8',
    type: 'funeral' as InsuranceProductType,
    name: 'Funeral Cover',
    description: 'Funeral expenses coverage for you and family',
    icon: 'shield',
    color: '#7D5BA6',
  },
  {
    id: '9',
    type: 'scheme' as InsuranceProductType,
    name: 'Scheme Insurance',
    description: 'Group insurance through your employer',
    icon: 'briefcase',
    color: '#3A86FF',
  },
  {
    id: '10',
    type: 'business' as InsuranceProductType,
    name: 'Business Insurance',
    description: 'Comprehensive coverage for your business',
    icon: 'briefcase',
    color: '#FFA500',
  },
];


