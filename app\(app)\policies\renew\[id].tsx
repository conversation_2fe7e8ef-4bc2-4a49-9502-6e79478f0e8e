import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Platform, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft, Check, AlertCircle, FileText, CreditCard,
  Shield, Calendar, RefreshCw, DollarSign, Info, ChevronRight
} from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import usePolicyStore, { Policy } from '@/store/policyStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import { showToast } from '@/utils/toast';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function PolicyRenewalScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getPolicyById, initiateRenewal } = usePolicyStore();
  const [policy, setPolicy] = useState<Policy | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRenewing, setIsRenewing] = useState(false);
  const [newPremium, setNewPremium] = useState<number | null>(null);
  const [premiumChangePercentage, setPremiumChangePercentage] = useState<number>(0);
  const [renewalDate, setRenewalDate] = useState<string>('');
  const [newEndDate, setNewEndDate] = useState<string>('');

  // Load policy details
  useEffect(() => {
    const loadPolicy = async () => {
      setIsLoading(true);
      try {
        if (id) {
          const policyData = getPolicyById(id);
          if (policyData) {
            setPolicy(policyData);
            
            // Calculate new premium (with a slight increase)
            const premiumIncrease = Math.random() * 0.1 + 0.02; // 2% to 12% increase
            const calculatedNewPremium = policyData.premium * (1 + premiumIncrease);
            setNewPremium(Math.round(calculatedNewPremium * 100) / 100);
            setPremiumChangePercentage(premiumIncrease * 100);
            
            // Calculate renewal dates
            const currentEndDate = new Date(policyData.endDate);
            const newStartDate = new Date(currentEndDate);
            newStartDate.setDate(newStartDate.getDate() + 1);
            
            const calculatedNewEndDate = new Date(newStartDate);
            calculatedNewEndDate.setFullYear(calculatedNewEndDate.getFullYear() + 1);
            
            setRenewalDate(newStartDate.toISOString().split('T')[0]);
            setNewEndDate(calculatedNewEndDate.toISOString().split('T')[0]);
          }
        }
      } catch (error) {
        console.error('Error loading policy:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPolicy();
  }, [id, getPolicyById]);

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Handle renewal
  const handleRenewal = async () => {
    if (isRenewing || !policy) return;

    setIsRenewing(true);
    try {
      await initiateRenewal(policy.id);
      showToast(
        'success',
        'Renewal Initiated',
        'Your policy renewal has been initiated successfully',
        { visibilityTime: 3000 }
      );
      
      // Navigate back to policy details
      setTimeout(() => {
        router.replace(`/policies/${policy.id}`);
      }, 1500);
    } catch (error) {
      console.error('Error renewing policy:', error);
      showToast(
        'error',
        'Renewal Failed',
        'Failed to initiate policy renewal. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsRenewing(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
      marginBottom: Platform.OS === 'ios' ? 88 : 60, // Add margin for bottom nav bar
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    infoLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    highlightCard: {
      backgroundColor: colors.primary[50],
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderLeftWidth: 4,
      borderLeftColor: colors.primary[500],
    },
    highlightTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.primary[700],
      marginBottom: spacing.xs,
    },
    highlightText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    premiumChangeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.sm,
    },
    premiumChangeText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: premiumChangePercentage > 0 ? colors.error[500] : colors.success[500],
      marginLeft: spacing.xs,
    },
    renewButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      alignItems: 'center',
      marginTop: spacing.lg,
    },
    renewButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
    noteCard: {
      backgroundColor: colors.info[50],
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    noteIcon: {
      marginRight: spacing.sm,
      marginTop: 2,
    },
    noteText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.info[700],
      flex: 1,
    },
    benefitItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    benefitText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Policy Renewal</Text>
        </View>
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={[styles.infoValue, { marginTop: spacing.md }]}>Loading policy details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!policy) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Policy Renewal</Text>
        </View>
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.infoValue}>Policy not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Policy Renewal</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View 
          style={styles.highlightCard}
          entering={FadeInDown.delay(100).springify()}
        >
          <Text style={styles.highlightTitle}>Renew Your {policy.type.charAt(0).toUpperCase() + policy.type.slice(1)} Insurance Policy</Text>
          <Text style={styles.highlightText}>
            Your policy is due for renewal. Review the details below and renew to maintain continuous coverage.
          </Text>
        </Animated.View>

        <Animated.View 
          style={styles.card}
          entering={FadeInDown.delay(200).springify()}
        >
          <Text style={styles.sectionTitle}>Current Policy Details</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Policy Number:</Text>
            <Text style={styles.infoValue}>{policy.policyNumber}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Type:</Text>
            <Text style={styles.infoValue}>{policy.type.charAt(0).toUpperCase() + policy.type.slice(1)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Start Date:</Text>
            <Text style={styles.infoValue}>{formatDate(policy.startDate)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>End Date:</Text>
            <Text style={styles.infoValue}>{formatDate(policy.endDate)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Current Premium:</Text>
            <Text style={styles.infoValue}>{formatCurrency(policy.premium, policy.currency)}</Text>
          </View>
        </Animated.View>

        <Animated.View 
          style={styles.card}
          entering={FadeInDown.delay(300).springify()}
        >
          <Text style={styles.sectionTitle}>Renewal Details</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>New Start Date:</Text>
            <Text style={styles.infoValue}>{formatDate(renewalDate)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>New End Date:</Text>
            <Text style={styles.infoValue}>{formatDate(newEndDate)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>New Premium:</Text>
            <Text style={styles.infoValue}>{newPremium ? formatCurrency(newPremium, policy.currency) : '-'}</Text>
          </View>
          
          {newPremium && (
            <View style={styles.premiumChangeContainer}>
              <Text style={styles.infoLabel}>Premium Change:</Text>
              <Text style={styles.premiumChangeText}>
                {premiumChangePercentage > 0 ? '+' : ''}
                {premiumChangePercentage.toFixed(1)}%
              </Text>
            </View>
          )}
        </Animated.View>

        <Animated.View 
          style={styles.card}
          entering={FadeInDown.delay(400).springify()}
        >
          <Text style={styles.sectionTitle}>Benefits of Renewal</Text>
          <View style={styles.benefitItem}>
            <Check size={20} color={colors.success[500]} />
            <Text style={styles.benefitText}>Continuous coverage without interruption</Text>
          </View>
          <View style={styles.benefitItem}>
            <Check size={20} color={colors.success[500]} />
            <Text style={styles.benefitText}>No need to resubmit documentation</Text>
          </View>
          <View style={styles.benefitItem}>
            <Check size={20} color={colors.success[500]} />
            <Text style={styles.benefitText}>Simplified renewal process</Text>
          </View>
          <View style={styles.benefitItem}>
            <Check size={20} color={colors.success[500]} />
            <Text style={styles.benefitText}>Maintain your policy benefits and history</Text>
          </View>
        </Animated.View>

        <Animated.View 
          style={styles.noteCard}
          entering={FadeInDown.delay(500).springify()}
        >
          <Info size={20} color={colors.info[700]} style={styles.noteIcon} />
          <Text style={styles.noteText}>
            By renewing your policy, you agree to the updated terms and conditions. 
            The new premium will be charged according to your selected payment method.
          </Text>
        </Animated.View>

        <TouchableOpacity
          style={styles.renewButton}
          onPress={handleRenewal}
          disabled={isRenewing}
        >
          <Text style={styles.renewButtonText}>
            {isRenewing ? 'Processing...' : 'Renew Policy'}
          </Text>
        </TouchableOpacity>
      </ScrollView>

      <BottomNavBar currentRoute="policies" />
    </SafeAreaView>
  );
}
