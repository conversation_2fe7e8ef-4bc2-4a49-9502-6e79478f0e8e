import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import Animated, { FadeIn } from 'react-native-reanimated';

interface TabItem {
  id: string;
  title: string;
  icon?: React.ReactNode;
}

interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  scrollable?: boolean;
  style?: any;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  scrollable = false,
  style,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.md,
    },
    tabsContainer: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    scrollableContainer: {
      flexDirection: 'row',
    },
    tabButton: {
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    tabButtonActive: {
      borderBottomWidth: 2,
      borderBottomColor: colors.primary[500],
    },
    tabText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    tabTextActive: {
      color: colors.primary[500],
      fontWeight: '600',
    },
    tabIcon: {
      marginRight: spacing.xs,
    },
  });

  const renderTabs = () => {
    return tabs.map((tab) => {
      const isActive = tab.id === activeTab;
      
      return (
        <TouchableOpacity
          key={tab.id}
          style={[
            styles.tabButton,
            isActive && styles.tabButtonActive,
          ]}
          onPress={() => onTabChange(tab.id)}
          activeOpacity={0.7}
        >
          {tab.icon && (
            <View style={styles.tabIcon}>
              {tab.icon}
            </View>
          )}
          <Text
            style={[
              styles.tabText,
              isActive && styles.tabTextActive,
            ]}
          >
            {tab.title}
          </Text>
        </TouchableOpacity>
      );
    });
  };

  return (
    <Animated.View
      style={[styles.container, style]}
      entering={FadeIn.duration(300)}
    >
      {scrollable ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollableContainer}
        >
          {renderTabs()}
        </ScrollView>
      ) : (
        <View style={styles.tabsContainer}>
          {renderTabs()}
        </View>
      )}
    </Animated.View>
  );
};

export default TabNavigation;
