import React from 'react';
import { View, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import Button from '@/components/ui/Button';
import { ArrowLeft, ArrowRight, Save, Send } from 'lucide-react-native';

interface QuoteActionButtonsProps {
  onBack: () => void;
  onNext?: () => void;
  onSave?: () => void;
  onSubmit?: () => void;
  isLastStep?: boolean;
  isLoading?: boolean;
  nextDisabled?: boolean;
  submitDisabled?: boolean;
  submitButtonText?: string;
}

const QuoteActionButtons: React.FC<QuoteActionButtonsProps> = ({
  onBack,
  onNext,
  onSave,
  onSubmit,
  isLastStep = false,
  isLoading = false,
  nextDisabled = false,
  submitDisabled = false,
  submitButtonText = "Submit",
}) => {
  // Add error handling for theme context
  const themeContext = useTheme();
  const isDarkMode = themeContext?.isDarkMode ?? false;

  // Create theme with fallback
  const theme = createTheme(isDarkMode);
  const { colors, spacing } = theme;

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    leftButtons: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rightButtons: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    saveButton: {
      marginRight: spacing.md,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.leftButtons}>
        <Button
          title="Back"
          onPress={onBack}
          variant="outline"
          icon={<ArrowLeft size={16} color={colors.primary[500]} />}
          iconPosition="left"
          disabled={isLoading}
        />
      </View>

      <View style={styles.rightButtons}>
        {onSave && (
          <View style={styles.saveButton}>
            <Button
              title="Save"
              onPress={onSave}
              variant="outline"
              icon={<Save size={16} color={colors.primary[500]} />}
              iconPosition="left"
              disabled={isLoading}
            />
          </View>
        )}

        {isLastStep && onSubmit ? (
          <Button
            title={submitButtonText}
            onPress={onSubmit}
            variant="primary"
            icon={<Send size={16} color={colors.white} />}
            iconPosition="right"
            loading={isLoading}
            disabled={nextDisabled || submitDisabled}
          />
        ) : onNext ? (
          <Button
            title="Next"
            onPress={onNext}
            variant="primary"
            icon={<ArrowRight size={16} color={colors.white} />}
            iconPosition="right"
            loading={isLoading}
            disabled={nextDisabled}
          />
        ) : null}
      </View>
    </View>
  );
};

export default QuoteActionButtons;
