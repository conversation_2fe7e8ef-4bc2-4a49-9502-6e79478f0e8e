import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { Car, Calendar, DollarSign, Hash, Info, FileText } from 'lucide-react-native';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import QuoteDocumentsSection from '@/components/quotes/QuoteDocumentsSection';
import useQuoteStore from '@/store/quoteStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { QuoteDocument } from '@/types/quote.types';
import { calculateMotorPremium } from '@/utils/quoteCalculations';

export default function MotorQuoteForm() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  // Get params
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    setCurrentQuote,
    currentQuote,
    isLoading: isQuoteLoading
  } = useQuoteStore();

  // Form state
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);
  const [isStorageLoading, setIsStorageLoading] = useState(true);
  const [storedQuoteId, setStoredQuoteId] = useState<string | null>(null);
  const [documents, setDocuments] = useState<QuoteDocument[]>([]);
  const [activeTab, setActiveTab] = useState<'details' | 'documents'>('details');

  // First useEffect to check AsyncStorage for stored quote data
  useEffect(() => {
    const checkAsyncStorage = async () => {
      try {
        // Try to get the quoteId from AsyncStorage
        const storedId = await AsyncStorage.getItem('currentQuoteId');

        console.log('MotorQuoteForm - Retrieved from AsyncStorage:', storedId);

        if (storedId) setStoredQuoteId(storedId);

        setIsStorageLoading(false);
      } catch (error) {
        console.error('MotorQuoteForm - AsyncStorage error:', error);
        setIsStorageLoading(false);
      }
    };

    checkAsyncStorage();
  }, []);

  // Load quote data
  useEffect(() => {
    // Skip if still loading AsyncStorage data
    if (isStorageLoading) return;

    console.log('MotorQuoteForm - Params received:', { quoteId });
    console.log('MotorQuoteForm - Stored data:', { storedQuoteId });

    // Use quoteId from params or AsyncStorage
    const quoteIdToUse = quoteId || storedQuoteId;

    if (!quoteIdToUse) {
      console.error('MotorQuoteForm - No quoteId found');
      showToast(
        'error',
        'Error',
        'Missing quote ID information',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
      return;
    }

    console.log('MotorQuoteForm - Looking up quote with ID:', quoteIdToUse);
    const quote = getQuoteById(quoteIdToUse);

    if (quote) {
      console.log('MotorQuoteForm - Quote found:', quote.id, 'Type:', quote.type);
      setCurrentQuote(quote);

      // Initialize form data based on existing quote data
      if (quote.additionalInfo) {
        setFormData(quote.additionalInfo);
      } else {
        // Set default form data
        setFormData({
          vehicleMake: '',
          vehicleModel: '',
          vehicleYear: new Date().getFullYear(),
          vehicleValue: 0,
          registrationNumber: '',
          engineNumber: '',
          chassisNumber: '',
          currentMileage: 0,
          usage: 'personal',
          coverType: 'comprehensive',
          isGreyImport: false,
          paymentPreference: 'annual', // Default to annual payment
          additionalExtensions: {
            towingAndStorage: true,
            lossOfKeys: true,
            carHire: true,
            freeValuation: true
          }
        });
      }

      // Initialize documents if they exist
      if (quote.documents && quote.documents.length > 0) {
        setDocuments(quote.documents);
      } else {
        // Initialize with required documents based on requirements
        setDocuments([
          {
            id: Date.now().toString() + '1',
            name: 'Copy of Driver\'s Licence',
            type: 'drivers_license',
            required: true,
            uploaded: false,
          },
          {
            id: Date.now().toString() + '2',
            name: 'Copy of Vehicle Registration Book',
            type: 'vehicle_registration',
            required: true,
            uploaded: false,
          },
          {
            id: Date.now().toString() + '3',
            name: 'Vehicle Valuation Report',
            type: 'valuation_report',
            required: true,
            uploaded: false,
          },
          {
            id: Date.now().toString() + '4',
            name: 'Claim-Free Group Letter / Previous Insurance History',
            type: 'insurance_history',
            required: false,
            uploaded: false,
          },
        ]);
      }
    } else {
      showToast(
        'error',
        'Error',
        'Quote not found',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
    }
  }, [quoteId, storedQuoteId, isStorageLoading, getQuoteById, setCurrentQuote]);

  // Validate form when data or documents change
  useEffect(() => {
    validateForm();
  }, [formData, documents]);

  // Update form data
  const updateFormField = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form - memoize to prevent unnecessary re-renders
  const validateForm = React.useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.vehicleMake) {
      newErrors.vehicleMake = 'Vehicle make is required';
    }
    if (!formData.vehicleModel) {
      newErrors.vehicleModel = 'Vehicle model is required';
    }
    if (!formData.registrationNumber) {
      newErrors.registrationNumber = 'Registration number is required';
    }
    if (!formData.vehicleValue || formData.vehicleValue <= 0) {
      newErrors.vehicleValue = 'Vehicle value must be greater than 0';
    }
    if (!formData.engineNumber) {
      newErrors.engineNumber = 'Engine number is required';
    }
    if (!formData.chassisNumber) {
      newErrors.chassisNumber = 'Chassis number is required';
    }
    if (!formData.currentMileage) {
      newErrors.currentMileage = 'Current mileage is required';
    }

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      newErrors.documents = 'All required documents must be uploaded';
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  }, [formData, documents]);


  // Handle back button
  const handleBack = () => {
    // Save the current form data before navigating back
    if (currentQuote && formData) {
      const saveBeforeBack = async () => {
        try {
          console.log('Saving draft before navigating back:', {
            quoteId: currentQuote.id,
            formData
          });

          // Update the quote with the current form data and documents
          await updateQuote({
            additionalInfo: formData,
            documents: documents,
            // Make sure the status is still draft
            status: 'draft',
            // Update the timestamp to ensure it's at the top of the list
            updatedAt: new Date().toISOString().split('T')[0]
          });

          // Get the fetchQuotes function from the store to refresh the quotes list
          // Use getState() to access store methods without hooks
          const storeState = useQuoteStore.getState();
          const fetchQuotes = storeState.fetchQuotes;

          // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
          await fetchQuotes();

          // Show toast notification
          showToast(
            'success',
            'Draft Saved',
            'Your quote draft has been saved',
            { visibilityTime: 2000 }
          );

          // Clear AsyncStorage to prevent issues with future navigation
          try {
            await AsyncStorage.removeItem('currentQuoteId');
            await AsyncStorage.removeItem('currentQuoteType');
            console.log('AsyncStorage cleared');
          } catch (storageError) {
            console.error('Error clearing AsyncStorage:', storageError);
          }

          // Use setTimeout to ensure navigation happens after component is mounted
          setTimeout(() => {
            try {
              // Navigate back to the quotes list
              // Use router.push instead of replace to ensure a fresh load
              router.push('/(app)/quotes' as any);
            } catch (navError) {
              console.error('Navigation error:', navError);
              // Show toast notification
              showToast(
                'error',
                'Navigation Error',
                'Failed to navigate back. Please try again.',
                { visibilityTime: 4000 }
              );
            }
          }, 100);
        } catch (error) {
          console.error('Error saving draft before navigating back:', error);

          // Still navigate back even if save fails
          setTimeout(() => {
            try {
              router.push('/(app)/quotes' as any);
            } catch (navError) {
              console.error('Navigation error:', navError);
            }
          }, 100);
        }
      };

      saveBeforeBack();
    } else {
      // If no current quote, just navigate back
      setTimeout(() => {
        try {
          router.push('/(app)/quotes' as any);
        } catch (navError) {
          console.error('Navigation error:', navError);
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate back. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!currentQuote) return;

    try {
      // Get the current state of the store
      const storeState = useQuoteStore.getState();
      console.log('Before update - Current quotes:', storeState.quotes);

      // Update the quote with the current form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        // Make sure the status is still draft
        status: 'draft',
        // Update the timestamp to ensure it's at the top of the list
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the updated state
      const updatedState = useQuoteStore.getState();
      console.log('After update - Current quotes:', updatedState.quotes);

      // Get the fetchQuotes function from the store to refresh the quotes list
      const fetchQuotes = updatedState.fetchQuotes;

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      console.log('After fetchQuotes - Current quotes:', useQuoteStore.getState().quotes);

      // Show a toast notification for better user feedback
      showToast(
        'success',
        'Draft Saved',
        'Your quote draft has been saved',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle next
  const handleNext = async () => {
    if (!currentQuote) return;

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      showToast(
        'error',
        'Required Documents',
        'Please upload all required documents before proceeding',
        { visibilityTime: 4000 }
      );
      setActiveTab('documents');
      return;
    }

    try {
      // Calculate premium based on vehicle make and value
      const vehicleValue = formData.vehicleValue || 50000;
      const vehicleMake = formData.vehicleMake || 'default';
      const isGreyImport = formData.isGreyImport || false;

      // Count number of vehicles (for fleet discount)
      const numberOfVehicles = 1; // In a real app, this would be determined by the user's policy

      // Calculate premium using our utility function
      const premium = calculateMotorPremium(
        vehicleValue,
        vehicleMake,
        isGreyImport,
        numberOfVehicles
      );

      // Create premium breakdown for display on summary page
      const premiumBreakdown = {
        baseRate: isGreyImport
          ? (vehicleMake === 'Toyota' ? '5%' : '6.5%')
          : (vehicleMake === 'Toyota' ? '3%' :
             vehicleMake === 'Mercedes-Benz' || vehicleMake === 'BMW' || vehicleMake === 'Volkswagen' || vehicleMake === 'Audi' ? '3.35%' : '3.5%'),
        vehicleValue: vehicleValue,
        basePremium: premium,
        extensions: {
          towingAndStorage: formData.additionalExtensions?.towingAndStorage ? 'Included' : 'Not included',
          lossOfKeys: formData.additionalExtensions?.lossOfKeys ? 'Included' : 'Not included',
          carHire: formData.additionalExtensions?.carHire ? 'Included' : 'Not included',
          freeValuation: formData.additionalExtensions?.freeValuation ? 'Included' : 'Not included'
        },
        fleetDiscount: numberOfVehicles >= 4 ? '5%' : 'Not applicable',
        totalPremium: premium
      };

      // Save form data, documents, and premium breakdown
      await updateQuote({
        additionalInfo: {
          ...formData,
          premiumBreakdown: premiumBreakdown
        },
        documents: documents,
        premium: premium,
        currency: 'P', // Set Botswana currency
        coverAmount: vehicleValue,
        // Update the timestamp
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const storeState = useQuoteStore.getState();
      const fetchQuotes = storeState.fetchQuotes;

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Clear AsyncStorage to prevent issues with future navigation
      try {
        await AsyncStorage.removeItem('currentQuoteId');
        await AsyncStorage.removeItem('currentQuoteType');
        console.log('AsyncStorage cleared before navigating to summary');
      } catch (storageError) {
        console.error('Error clearing AsyncStorage:', storageError);
      }

      // Use setTimeout to ensure navigation happens after component is mounted
      setTimeout(() => {
        try {
          // Navigate to the summary page
          router.push({
            pathname: '/(app)/quotes/[type]/summary',
            params: {
              type: 'motor',
              quoteId: currentQuote.id
            }
          });
        } catch (navError) {
          console.error('Navigation error:', navError);
          // Show toast notification
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate to summary page. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  // Render motor insurance form fields
  const renderFormFields = () => {
    const usageOptions: SelectOption[] = [
      { value: 'personal', label: 'Personal Use' },
      { value: 'commercial', label: 'Commercial Use' },
    ];

    const coverTypeOptions: SelectOption[] = [
      { value: 'comprehensive', label: 'Comprehensive' },
      { value: 'thirdParty', label: 'Third Party Only' },
      { value: 'thirdPartyFireAndTheft', label: 'Third Party, Fire & Theft' },
    ];

    const paymentPreferenceOptions: SelectOption[] = [
      { value: 'annual', label: 'Annual (EFT only)' },
      { value: 'monthly', label: 'Monthly (Direct Debit Order)' },
    ];

    return (
      <View>
        <DynamicFormField
          label="Vehicle Make"
          type="text"
          value={formData.vehicleMake}
          onChange={(value) => updateFormField('vehicleMake', value)}
          placeholder="e.g., Toyota, Honda, Ford"
          error={errors.vehicleMake}
          required
          icon={<Car size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Model"
          type="text"
          value={formData.vehicleModel}
          onChange={(value) => updateFormField('vehicleModel', value)}
          placeholder="e.g., Corolla, Civic, Focus"
          error={errors.vehicleModel}
          required
          icon={<Car size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Year"
          type="number"
          value={formData.vehicleYear?.toString()}
          onChange={(value) => updateFormField('vehicleYear', parseInt(value) || new Date().getFullYear())}
          placeholder="e.g., 2020"
          keyboardType="numeric"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Value"
          type="number"
          value={formData.vehicleValue?.toString()}
          onChange={(value) => updateFormField('vehicleValue', parseFloat(value) || 0)}
          placeholder="e.g., 25000"
          error={errors.vehicleValue}
          required
          keyboardType="numeric"
          icon={<DollarSign size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Registration Number"
          type="text"
          value={formData.registrationNumber}
          onChange={(value) => updateFormField('registrationNumber', value)}
          placeholder="e.g., ABC123"
          error={errors.registrationNumber}
          required
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Engine Number"
          type="text"
          value={formData.engineNumber}
          onChange={(value) => updateFormField('engineNumber', value)}
          placeholder="e.g., EN12345678"
          error={errors.engineNumber}
          required
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Chassis Number"
          type="text"
          value={formData.chassisNumber}
          onChange={(value) => updateFormField('chassisNumber', value)}
          placeholder="e.g., CH12345678"
          error={errors.chassisNumber}
          required
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Current Mileage"
          type="number"
          value={formData.currentMileage?.toString()}
          onChange={(value) => updateFormField('currentMileage', parseFloat(value) || 0)}
          placeholder="e.g., 50000"
          error={errors.currentMileage}
          required
          keyboardType="numeric"
          icon={<Hash size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Vehicle Usage"
          type="select"
          value={formData.usage}
          onChange={(value) => updateFormField('usage', value)}
          options={usageOptions}
          icon={<Car size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Cover Type"
          type="select"
          value={formData.coverType}
          onChange={(value) => updateFormField('coverType', value)}
          options={coverTypeOptions}
          icon={<Info size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Grey Import Vehicle (higher premium applies)"
          type="switch"
          value={formData.isGreyImport || false}
          onChange={(value) => updateFormField('isGreyImport', value)}
        />

        <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 20 }]}>
          Payment Preference
        </Text>

        <DynamicFormField
          label="Payment Method"
          type="select"
          value={formData.paymentPreference || 'annual'}
          onChange={(value) => updateFormField('paymentPreference', value)}
          options={paymentPreferenceOptions}
          required
        />

        <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 20 }]}>
          Additional Extensions
        </Text>

        <DynamicFormField
          label="Towing and storage limit: P5,000"
          type="switch"
          value={formData.additionalExtensions?.towingAndStorage || false}
          onChange={(value) => updateFormField('additionalExtensions', {
            ...formData.additionalExtensions,
            towingAndStorage: value
          })}
        />

        <DynamicFormField
          label="Loss of Keys: P5,000"
          type="switch"
          value={formData.additionalExtensions?.lossOfKeys || false}
          onChange={(value) => updateFormField('additionalExtensions', {
            ...formData.additionalExtensions,
            lossOfKeys: value
          })}
        />

        <DynamicFormField
          label="Car Hire: P100 per day (max P500)"
          type="switch"
          value={formData.additionalExtensions?.carHire || false}
          onChange={(value) => updateFormField('additionalExtensions', {
            ...formData.additionalExtensions,
            carHire: value
          })}
        />

        <DynamicFormField
          label="Free Valuation at every Subsequent renewal"
          type="switch"
          value={formData.additionalExtensions?.freeValuation || false}
          onChange={(value) => updateFormField('additionalExtensions', {
            ...formData.additionalExtensions,
            freeValuation: value
          })}
        />
      </View>
    );
  };

  // Handle document updates
  const handleDocumentsUpdated = (updatedDocuments: QuoteDocument[]) => {
    setDocuments(updatedDocuments);
  };

  // Define styles
  const styles = StyleSheet.create({
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    loadingText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginTop: spacing.md,
      textAlign: 'center',
    },
    tabsContainer: {
      flexDirection: 'row',
      marginBottom: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    tab: {
      flex: 1,
      paddingVertical: spacing.sm,
      alignItems: 'center',
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: colors.primary[500],
    },
    tabText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    activeTabText: {
      color: colors.primary[500],
    },
    contentContainer: {
      flex: 1,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      marginBottom: spacing.sm,
    },
  });

  // Show loading indicator while checking AsyncStorage
  if (isStorageLoading || isQuoteLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading quote data...</Text>
      </View>
    );
  }

  // We'll render both components and show/hide them based on the active tab
  // This avoids the invalid hook call error by ensuring components with hooks
  // are always rendered, not conditionally

  return (
    <QuoteFormContainer
      title="Motor Insurance Details"
      subtitle="Please provide details for your motor insurance quote"
      steps={steps}
      currentStep={1}
      completedSteps={[0]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isQuoteLoading}
      nextDisabled={!formValid}
    >
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'details' && styles.activeTab
          ]}
          onPress={() => setActiveTab('details')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'details' && styles.activeTabText
          ]}>
            Vehicle Details
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'documents' && styles.activeTab
          ]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'documents' && styles.activeTabText
          ]}>
            Required Documents
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        {/* Render both components but only show the active one */}
        <View style={{ display: activeTab === 'details' ? 'flex' : 'none' }}>
          {renderFormFields()}
        </View>

        <View style={{ display: activeTab === 'documents' ? 'flex' : 'none' }}>
          <QuoteDocumentsSection
            quoteType="motor"
            documents={documents}
            onDocumentsUpdated={handleDocumentsUpdated}
          />
        </View>
      </View>
    </QuoteFormContainer>
  );
}
