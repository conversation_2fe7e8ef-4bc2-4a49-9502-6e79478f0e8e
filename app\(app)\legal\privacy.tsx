import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, Clock, ChevronDown } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import useLegalStore, { LegalDocumentVersion } from '@/store/legalStore';
import { useAppSelector } from '@/store/hooks';

export default function PrivacyPolicyScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const versionId = params.versionId as string;

  // Get user ID from auth state
  const userId = useAppSelector(state => state.auth.user?.id);

  // State
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<LegalDocumentVersion | null>(null);

  // Get legal store methods
  const {
    fetchLegalDocuments,
    getCurrentVersion,
    getDocumentVersions,
    getVersionById,
    hasUserAccepted,
    acceptLegalDocument,
    isLoading
  } = useLegalStore();

  // Load legal documents on mount
  useEffect(() => {
    const loadDocuments = async () => {
      await fetchLegalDocuments();

      if (versionId) {
        // If a specific version is requested, load that version
        const version = getVersionById('privacy', versionId);
        if (version) {
          setSelectedVersion(version);
        } else {
          // If version not found, load current version
          setSelectedVersion(getCurrentVersion('privacy'));
        }
      } else {
        // Otherwise load current version
        setSelectedVersion(getCurrentVersion('privacy'));
      }
    };

    loadDocuments();
  }, [versionId]);

  // Handle accepting the privacy policy
  const handleAcceptPrivacyPolicy = async () => {
    if (!userId) {
      console.error('User ID not found');
      return;
    }

    try {
      await acceptLegalDocument('privacy', userId);
    } catch (error) {
      console.error('Error accepting privacy policy:', error);
    }
  };

  // Toggle version history
  const toggleVersionHistory = () => {
    setShowVersionHistory(!showVersionHistory);
  };

  // Select a specific version
  const selectVersion = (version: LegalDocumentVersion) => {
    setSelectedVersion(version);
    setShowVersionHistory(false);
  };

  // Get all versions for history
  const allVersions = getDocumentVersions('privacy');

  // Check if user has accepted current version
  const hasAccepted = userId ? hasUserAccepted('privacy', userId) : false;

  // Define styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    backButton: {
      padding: 8,
      marginRight: 8,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
    },
    scrollView: {
      flex: 1,
    },
    contentContainer: {
      padding: 16,
      paddingBottom: 40,
    },
    lastUpdated: {
      fontSize: 14,
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginTop: 24,
      marginBottom: 8,
    },
    paragraph: {
      fontSize: 16,
      lineHeight: 24,
      marginBottom: 16,
    },
    bulletPoint: {
      fontSize: 16,
      lineHeight: 24,
      marginBottom: 8,
      paddingLeft: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    versionBar: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderBottomWidth: 1,
    },
    versionSelector: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    versionText: {
      fontSize: 14,
    },
    acceptButton: {
      paddingHorizontal: 16,
      paddingVertical: 6,
      borderRadius: 4,
    },
    acceptButtonText: {
      color: 'white',
      fontWeight: '500',
    },
    versionHistoryContainer: {
      position: 'absolute',
      top: 100,
      left: 16,
      right: 16,
      zIndex: 10,
      borderRadius: 8,
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      padding: 8,
    },
    versionItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 8,
      borderRadius: 4,
    },
    versionItemText: {
      fontSize: 14,
      fontWeight: '500',
    },
    versionItemDate: {
      fontSize: 12,
    },
    activeVersionBadge: {
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 4,
    },
    activeVersionText: {
      color: 'white',
      fontSize: 10,
      fontWeight: '500',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Privacy Policy</Text>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      ) : (
        <>
          <View style={[styles.versionBar, { borderBottomColor: colors.border }]}>
            <TouchableOpacity
              style={styles.versionSelector}
              onPress={toggleVersionHistory}
            >
              <Clock size={16} color={colors.textSecondary} />
              <Text style={[styles.versionText, { color: colors.textSecondary }]}>
                Version {selectedVersion?.version || '1.0'}
              </Text>
              <ChevronDown size={16} color={colors.textSecondary} />
            </TouchableOpacity>

            {!hasAccepted && userId && selectedVersion?.isActive && (
              <TouchableOpacity
                style={[styles.acceptButton, { backgroundColor: colors.primary[500] }]}
                onPress={handleAcceptPrivacyPolicy}
              >
                <Text style={styles.acceptButtonText}>Accept</Text>
              </TouchableOpacity>
            )}
          </View>

          {showVersionHistory && (
            <View style={[styles.versionHistoryContainer, { backgroundColor: colors.card }]}>
              {allVersions.map(version => (
                <TouchableOpacity
                  key={version.id}
                  style={[
                    styles.versionItem,
                    selectedVersion?.id === version.id && { backgroundColor: `${colors.primary[500]}20` }
                  ]}
                  onPress={() => selectVersion(version)}
                >
                  <Text style={[styles.versionItemText, { color: colors.text }]}>
                    Version {version.version}
                  </Text>
                  <Text style={[styles.versionItemDate, { color: colors.textSecondary }]}>
                    {version.publishDate}
                  </Text>
                  {version.isActive && (
                    <View style={[styles.activeVersionBadge, { backgroundColor: colors.success[500] }]}>
                      <Text style={styles.activeVersionText}>Current</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}

          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            <Animated.View entering={FadeInDown.delay(100).springify()}>
              <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
                Last Updated: {selectedVersion?.publishDate || 'April 16, 2025'}
              </Text>

          <Text style={[styles.paragraph, { color: colors.text }]}>
            Inerca Holdings ("we", "our", or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application (the "App").
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            1. Information We Collect
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            We may collect the following types of information:
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Personal Identification Information: Name, email address, phone number, national ID/passport number, residential/business address, occupation.
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Financial Information: Payment preferences, bank details for direct debit orders.
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Documents: ID/passport copies, proof of address, driver's license, vehicle registration, property documents, and other insurance-related documentation.
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Insurance Information: Vehicle details, property information, contents inventory, and other information related to insurance quotes and policies.
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Device Information: Device type, operating system, unique device identifiers, and mobile network information.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            2. How We Use Your Information
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            We use the information we collect to:
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Provide, maintain, and improve our services
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Process and manage insurance quotes, applications, and policies
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Verify your identity and the documents you submit
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Communicate with you about your account, policies, and our services
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Comply with legal obligations and insurance regulations
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            3. Disclosure of Your Information
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            We may share your information with:
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Insurance providers and underwriters to process your applications and policies
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Service providers who perform services on our behalf
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Legal and regulatory authorities when required by law
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            4. Data Security
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            We implement appropriate technical and organizational measures to protect your personal information against unauthorized or unlawful processing, accidental loss, destruction, or damage. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            5. Data Retention
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            We will retain your personal information for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required or permitted by law.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            6. Your Rights
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Depending on your location, you may have the right to:
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Access the personal information we hold about you
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Request correction of inaccurate information
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Request deletion of your information
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Object to processing of your information
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Request restriction of processing
          </Text>
          <Text style={[styles.bulletPoint, { color: colors.text }]}>
            • Request transfer of your information
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            7. Children's Privacy
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            Our App is not intended for children under 18 years of age. We do not knowingly collect personal information from children under 18.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            8. Changes to This Privacy Policy
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.
          </Text>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            9. Contact Us
          </Text>
          <Text style={[styles.paragraph, { color: colors.text }]}>
            If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
          </Text>
        </Animated.View>
          </ScrollView>
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  lastUpdated: {
    fontSize: 14,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 8,
    paddingLeft: 16,
  },
});
