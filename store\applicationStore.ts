import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { Quote } from '@/types/quote.types';

// Define application types
export type ApplicationStatus =
  | 'quote_accepted'      // Initial status when quote is accepted
  | 'submitted'           // Application submitted
  | 'payment_pending'     // Waiting for payment
  | 'payment_verified'    // Payment has been verified
  | 'underwriting'        // In underwriting process
  | 'additional_info'     // Additional information requested
  | 'approved'            // Application approved
  | 'approved_with_terms' // Approved with modified terms
  | 'policy_issued'       // Policy has been issued
  | 'rejected';           // Application rejected

export type DocumentStatus = 'pending' | 'verified' | 'rejected';

export type PaymentMethod = 'eft' | 'direct_debit' | 'none';

export type PaymentStatus =
  | 'not_started'    // No payment initiated
  | 'pending'        // Payment initiated but not verified
  | 'verified'       // Payment verified
  | 'failed';        // Payment failed

export type Document = {
  id: string;
  name: string;
  type: string;
  status: DocumentStatus;
  date: string;
  verificationDate?: string;
  rejectionReason?: string;
  required: boolean;
  documentId?: string; // Reference to document in document system
};

export type TimelineEvent = {
  id: string;
  date: string;
  title: string;
  description: string;
  status: 'completed' | 'current' | 'upcoming';
  icon?: string; // Icon name for visual representation
  actions?: Array<{
    label: string;
    action: string;
  }>;
};

export type PaymentDetails = {
  method: PaymentMethod;
  status: PaymentStatus;
  amount: number;
  currency: string;
  dueDate?: string;
  paidDate?: string;
  reference?: string;
  proofOfPaymentId?: string; // Reference to uploaded proof of payment
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    branchCode: string;
    reference: string;
  };
  debitOrderDetails?: {
    accountHolder: string;
    accountNumber: string;
    bankName: string;
    accountType: string;
    debitDay: number;
    reference: string;
    authorized: boolean;
  };
};

export type UnderwritingDetails = {
  status: 'pending' | 'in_progress' | 'completed' | 'additional_info';
  assignedTo?: string;
  startDate?: string;
  completionDate?: string;
  notes?: string[];
  decision?: 'approved' | 'approved_with_terms' | 'rejected';
  decisionReason?: string;
  modifiedTerms?: string[];
};

export type Application = {
  id: string;
  quoteId: string;
  type: string;
  status: ApplicationStatus;
  reference: string;
  date: string;
  premium: number;
  currency: string;
  coverAmount: number;
  clientInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address?: string;
    idNumber?: string;
  };
  documents: Document[];
  timeline: TimelineEvent[];
  payment: PaymentDetails;
  underwriting?: UnderwritingDetails;
  policyNumber?: string;
  policyIssueDate?: string;
  policyDocumentId?: string;
  notes?: string[];
  requiredActions?: Array<{
    id: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    dueDate?: string;
    completed: boolean;
  }>;
};

// Define application state
interface ApplicationState {
  applications: Application[];
  currentApplication: Application | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchApplications: () => Promise<void>;
  getApplicationById: (id: string) => Promise<Application | undefined>;
  createApplicationFromQuote: (quote: Quote) => Promise<Application>;
  updateApplication: (applicationUpdate: Partial<Application>) => Promise<void>;
  updateApplicationStatus: (id: string, status: ApplicationStatus) => Promise<void>;
  updatePaymentMethod: (id: string, method: PaymentMethod, details?: any) => Promise<void>;
  updatePaymentStatus: (id: string, status: PaymentStatus, details?: any) => Promise<void>;
  uploadPaymentProof: (id: string, documentId: string) => Promise<void>;
  updateDocumentStatus: (id: string, documentId: string, status: DocumentStatus, details?: any) => Promise<void>;
  addTimelineEvent: (id: string, event: Omit<TimelineEvent, 'id'>) => Promise<void>;
  updateUnderwritingDetails: (id: string, details: Partial<UnderwritingDetails>) => Promise<void>;
  issuePolicy: (id: string, policyNumber: string, documentId?: string) => Promise<void>;
  addRequiredAction: (id: string, action: Omit<Application['requiredActions'][0], 'id' | 'completed'>) => Promise<void>;
  completeRequiredAction: (id: string, actionId: string) => Promise<void>;
}

// Helper function to load applications from storage
const loadApplicationsFromStorage = async (): Promise<Application[] | null> => {
  try {
    const applicationsJson = await AsyncStorage.getItem('applications');
    if (applicationsJson) {
      return JSON.parse(applicationsJson);
    }
    return null;
  } catch (error) {
    console.error('Error loading applications from storage:', error);
    return null;
  }
};

// Helper function to save applications to storage
const saveApplicationsToStorage = async (applications: Application[]): Promise<void> => {
  try {
    await AsyncStorage.setItem('applications', JSON.stringify(applications));
  } catch (error) {
    console.error('Error saving applications to storage:', error);
  }
};

// Create the store
const useApplicationStore = create<ApplicationState>((set, get) => ({
  applications: [],
  currentApplication: null,
  isLoading: false,
  error: null,

  // Fetch all applications - frontend only, no API calls
  fetchApplications: async () => {
    set({ isLoading: true, error: null });
    try {
      // Load from AsyncStorage only (no API calls)
      const storedApplications = await loadApplicationsFromStorage();

      if (storedApplications && storedApplications.length > 0) {
        console.log('Using applications from AsyncStorage:', storedApplications.length);
        set({ applications: storedApplications, isLoading: false });
        return;
      }

      // If no stored applications, start with empty array
      console.log('No applications found in storage, starting with empty array');
      set({ applications: [], isLoading: false });
    } catch (error) {
      console.error('Error loading applications from storage:', error);
      set({
        applications: [],
        error: 'Failed to load applications. Please try again.',
        isLoading: false
      });
    }
  },

  // Get an application by ID - frontend only, no API calls
  getApplicationById: async (id: string) => {
    try {
      set({ isLoading: true, error: null });

      // First check if we have it in state
      const cachedApplication = get().applications.find(application => application.id === id);

      if (cachedApplication) {
        set({ currentApplication: cachedApplication, isLoading: false });
        return cachedApplication;
      }

      // If not in state, try to load all applications from storage
      const storedApplications = await loadApplicationsFromStorage();

      if (storedApplications && storedApplications.length > 0) {
        // Update state with stored applications
        set({ applications: storedApplications, isLoading: false });

        // Find the requested application
        const application = storedApplications.find(app => app.id === id);

        if (application) {
          set({ currentApplication: application });
          return application;
        }
      }

      // Application not found
      console.log(`Application with ID ${id} not found in storage`);
      set({ isLoading: false });
      return undefined;
    } catch (error) {
      console.error(`Error getting application with ID ${id}:`, error);
      set({
        error: `Failed to get application details. Please try again.`,
        isLoading: false
      });
      return undefined;
    }
  },

  // Create a new application from a quote
  createApplicationFromQuote: async (quote: Quote) => {
    set({ isLoading: true, error: null });
    try {
      // Generate a reference number
      const timestamp = Date.now();
      const reference = `APP-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;
      const currentDate = new Date().toISOString().split('T')[0];

      // Create timeline events
      const timeline: TimelineEvent[] = [
        {
          id: '1',
          date: currentDate,
          title: 'Quote Accepted',
          description: 'You have accepted the quote and started the application process.',
          status: 'completed',
          icon: 'check-circle'
        },
        {
          id: '2',
          date: currentDate,
          title: 'Application Submitted',
          description: 'Your application has been submitted successfully.',
          status: 'completed',
          icon: 'file-text'
        },
        {
          id: '3',
          date: '',
          title: 'Payment',
          description: 'Payment is required to proceed with your application.',
          status: 'current',
          icon: 'credit-card',
          actions: [
            {
              label: 'Make Payment',
              action: 'navigate_to_payment'
            }
          ]
        },
        {
          id: '4',
          date: '',
          title: 'Document Verification',
          description: 'We are verifying your documents.',
          status: 'upcoming',
          icon: 'file-check'
        },
        {
          id: '5',
          date: '',
          title: 'Underwriting',
          description: 'Our underwriters will review your application.',
          status: 'upcoming',
          icon: 'clipboard'
        },
        {
          id: '6',
          date: '',
          title: 'Policy Issuance',
          description: 'Your policy will be issued upon approval.',
          status: 'upcoming',
          icon: 'shield'
        }
      ];

      // Map quote documents to application documents with enhanced properties
      const documents: Document[] = quote.documents.map(doc => ({
        id: doc.id || `doc-${Math.random().toString(36).substring(2, 9)}`,
        name: doc.name || doc.type,
        type: doc.type,
        status: doc.status || 'pending',
        date: currentDate,
        required: doc.required || true,
        documentId: doc.documentId
      }));

      // Set due date for payment (7 days from now)
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 7);
      const dueDateStr = dueDate.toISOString().split('T')[0];

      // Create required actions
      const requiredActions = [
        {
          id: `action-${Math.random().toString(36).substring(2, 9)}`,
          description: 'Select a payment method and complete payment',
          priority: 'high' as const,
          dueDate: dueDateStr,
          completed: false
        }
      ];

      // Create the new application with enhanced fields
      const newApplication: Application = {
        id: timestamp.toString(),
        quoteId: quote.id,
        type: quote.type.charAt(0).toUpperCase() + quote.type.slice(1),
        status: 'quote_accepted',
        reference,
        date: currentDate,
        premium: quote.premium || 0,
        currency: quote.currency || 'P',
        coverAmount: quote.coverAmount || 0,
        clientInfo: {
          ...quote.clientInfo,
          idNumber: quote.clientInfo.idNumber || ''
        },
        documents,
        timeline,
        payment: {
          method: 'none',
          status: 'not_started',
          amount: quote.premium || 0,
          currency: quote.currency || 'P',
          dueDate: dueDateStr,
          bankDetails: {
            accountName: 'Inerca Insurance',
            accountNumber: '***********',
            bankName: 'First National Bank',
            branchCode: '250655',
            reference: reference
          }
        },
        underwriting: {
          status: 'pending'
        },
        notes: [],
        requiredActions
      };

      // Update state with new application
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = [newApplication, ...state.applications];
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: newApplication,
          isLoading: false
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Show success toast
      showToast(
        'success',
        'Application Created',
        'Your application has been created successfully',
        { visibilityTime: 3000 }
      );

      return newApplication;
    } catch (error) {
      console.error('Error creating application:', error);
      set({
        error: 'Failed to create application. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update an application
  updateApplication: async (applicationUpdate: Partial<Application>) => {
    set({ isLoading: true, error: null });
    try {
      const { currentApplication } = get();

      if (!currentApplication) {
        throw new Error('No current application to update');
      }

      const updatedApplication = {
        ...currentApplication,
        ...applicationUpdate,
      };

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === updatedApplication.id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: updatedApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      return;
    } catch (error) {
      console.error('Error updating application:', error);
      set({
        error: 'Failed to update application. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update application status
  updateApplicationStatus: async (id: string, status: ApplicationStatus) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Update timeline based on status change
      const currentDate = new Date().toISOString().split('T')[0];
      let updatedTimeline = [...application.timeline];

      // Update timeline events based on new status
      switch (status) {
        case 'payment_pending':
          updatedTimeline = updatedTimeline.map(event =>
            event.title === 'Payment'
              ? { ...event, status: 'current', date: currentDate }
              : event
          );
          break;
        case 'payment_verified':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Payment') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Document Verification') {
              return { ...event, status: 'current', date: currentDate };
            }
            return event;
          });
          break;
        case 'underwriting':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Document Verification') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Underwriting') {
              return { ...event, status: 'current', date: currentDate };
            }
            return event;
          });
          break;
        case 'approved':
        case 'approved_with_terms':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Underwriting') {
              return { ...event, status: 'completed', date: currentDate };
            } else if (event.title === 'Policy Issuance') {
              return { ...event, status: 'current', date: currentDate };
            }
            return event;
          });
          break;
        case 'policy_issued':
          updatedTimeline = updatedTimeline.map(event => {
            if (event.title === 'Policy Issuance') {
              return { ...event, status: 'completed', date: currentDate };
            }
            return event;
          });
          break;
      }

      // Update the application
      const updatedApplication = {
        ...application,
        status,
        timeline: updatedTimeline,
      };

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Show success toast
      showToast(
        'success',
        'Status Updated',
        `Application status updated to ${status.replace('_', ' ')}`,
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error updating application status:', error);
      set({
        error: 'Failed to update application status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update payment method
  updatePaymentMethod: async (id: string, method: PaymentMethod, details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Update payment details based on method
      let updatedPayment = { ...application.payment, method };

      if (method === 'eft') {
        // EFT payment details remain the same as they were initialized
      } else if (method === 'direct_debit') {
        // Update with debit order details
        updatedPayment = {
          ...updatedPayment,
          debitOrderDetails: details || {
            accountHolder: '',
            accountNumber: '',
            bankName: '',
            accountType: '',
            debitDay: 1,
            reference: application.reference,
            authorized: false
          }
        };
      }

      // Update the application
      const updatedApplication = {
        ...application,
        payment: updatedPayment,
        status: 'payment_pending'
      };

      // Update timeline
      const currentDate = new Date().toISOString().split('T')[0];
      const updatedTimeline = application.timeline.map(event =>
        event.title === 'Payment'
          ? { ...event, status: 'current', date: currentDate }
          : event
      );

      updatedApplication.timeline = updatedTimeline;

      // Update required actions
      const updatedRequiredActions = application.requiredActions?.map(action =>
        action.description.includes('payment method')
          ? { ...action, completed: true }
          : action
      ) || [];

      // Add new action for payment proof if EFT
      if (method === 'eft') {
        updatedRequiredActions.push({
          id: `action-${Math.random().toString(36).substring(2, 9)}`,
          description: 'Upload proof of payment',
          priority: 'high',
          dueDate: application.payment.dueDate,
          completed: false
        });
      }

      updatedApplication.requiredActions = updatedRequiredActions;

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Show success toast
      showToast(
        'success',
        'Payment Method Selected',
        `You've selected ${method === 'eft' ? 'EFT' : 'Direct Debit'} as your payment method`,
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error updating payment method:', error);
      set({
        error: 'Failed to update payment method. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update payment status
  updatePaymentStatus: async (id: string, status: PaymentStatus, details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      const currentDate = new Date().toISOString().split('T')[0];

      // Update payment details
      const updatedPayment = {
        ...application.payment,
        status,
        ...(status === 'verified' ? { paidDate: currentDate } : {})
      };

      // Update application status if payment is verified
      let updatedStatus = application.status;
      if (status === 'verified') {
        updatedStatus = 'payment_verified';
      }

      // Update the application
      const updatedApplication = {
        ...application,
        payment: updatedPayment,
        status: updatedStatus
      };

      // Update timeline if payment is verified
      if (status === 'verified') {
        const updatedTimeline = application.timeline.map(event => {
          if (event.title === 'Payment') {
            return { ...event, status: 'completed', date: currentDate };
          } else if (event.title === 'Document Verification') {
            return { ...event, status: 'current', date: currentDate };
          }
          return event;
        });
        updatedApplication.timeline = updatedTimeline;
      }

      // Update required actions if payment is verified
      if (status === 'verified') {
        const updatedRequiredActions = application.requiredActions?.map(action =>
          action.description.includes('proof of payment')
            ? { ...action, completed: true }
            : action
        ) || [];
        updatedApplication.requiredActions = updatedRequiredActions;
      }

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Show success toast
      if (status === 'verified') {
        showToast(
          'success',
          'Payment Verified',
          'Your payment has been verified successfully',
          { visibilityTime: 3000 }
        );
      } else if (status === 'pending') {
        showToast(
          'info',
          'Payment Pending',
          'Your payment is being processed',
          { visibilityTime: 3000 }
        );
      }

      return;
    } catch (error) {
      console.error('Error updating payment status:', error);
      set({
        error: 'Failed to update payment status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Upload payment proof
  uploadPaymentProof: async (id: string, documentId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Update payment details
      const updatedPayment = {
        ...application.payment,
        status: 'pending',
        proofOfPaymentId: documentId
      };

      // Update the application
      const updatedApplication = {
        ...application,
        payment: updatedPayment
      };

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Show success toast
      showToast(
        'success',
        'Proof Uploaded',
        'Your payment proof has been uploaded successfully',
        { visibilityTime: 3000 }
      );

      // Simulate payment verification after 15 seconds
      setTimeout(() => {
        get().updatePaymentStatus(id, 'verified');
      }, 15000);

      return;
    } catch (error) {
      console.error('Error uploading payment proof:', error);
      set({
        error: 'Failed to upload payment proof. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update document status
  updateDocumentStatus: async (id: string, documentId: string, status: DocumentStatus, details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      const currentDate = new Date().toISOString().split('T')[0];

      // Update document
      const updatedDocuments = application.documents.map(doc =>
        doc.id === documentId
          ? {
              ...doc,
              status,
              ...(status === 'verified' ? { verificationDate: currentDate } : {}),
              ...(status === 'rejected' && details?.reason ? { rejectionReason: details.reason } : {})
            }
          : doc
      );

      // Check if all required documents are verified
      const allRequiredVerified = updatedDocuments
        .filter(doc => doc.required)
        .every(doc => doc.status === 'verified');

      // Update application status if all documents are verified
      let updatedStatus = application.status;
      if (allRequiredVerified && application.status === 'payment_verified') {
        updatedStatus = 'underwriting';
      }

      // Update the application
      const updatedApplication = {
        ...application,
        documents: updatedDocuments,
        status: updatedStatus
      };

      // Update timeline if all documents are verified
      if (allRequiredVerified && application.status === 'payment_verified') {
        const updatedTimeline = application.timeline.map(event => {
          if (event.title === 'Document Verification') {
            return { ...event, status: 'completed', date: currentDate };
          } else if (event.title === 'Underwriting') {
            return { ...event, status: 'current', date: currentDate };
          }
          return event;
        });
        updatedApplication.timeline = updatedTimeline;

        // Update underwriting details
        updatedApplication.underwriting = {
          ...updatedApplication.underwriting,
          status: 'in_progress',
          startDate: currentDate,
          assignedTo: 'John Smith'
        };

        // Simulate a more detailed underwriting process with multiple stages

        // First, add a timeline event for underwriting assignment
        get().addTimelineEvent(id, {
          date: currentDate,
          title: 'Underwriting Started',
          description: 'Your application has been assigned to an underwriter.',
          status: 'completed',
          icon: 'clipboard'
        });

        // Initial review after 10 seconds
        setTimeout(() => {
          // Add a timeline event for initial review
          get().addTimelineEvent(id, {
            date: new Date().toISOString().split('T')[0],
            title: 'Initial Review',
            description: 'Initial review of your application has been completed.',
            status: 'completed',
            icon: 'file-check',
            details: 'Our underwriters have completed the initial review of your application and are now performing a detailed risk assessment.'
          });

          // Update underwriting details
          get().updateUnderwritingDetails(id, {
            status: 'in_progress',
            notes: [
              ...(updatedApplication.underwriting?.notes || []),
              'Initial review completed. Proceeding with risk assessment.'
            ]
          });

          // Risk assessment after another 10 seconds
          setTimeout(() => {
            // Add a timeline event for risk assessment
            get().addTimelineEvent(id, {
              date: new Date().toISOString().split('T')[0],
              title: 'Risk Assessment',
              description: 'Risk assessment has been completed.',
              status: 'completed',
              icon: 'clipboard',
              details: 'Our underwriters have completed the risk assessment for your application.'
            });

            // Update underwriting details
            get().updateUnderwritingDetails(id, {
              status: 'in_progress',
              notes: [
                ...(updatedApplication.underwriting?.notes || []),
                'Risk assessment completed. Finalizing underwriting decision.'
              ]
            });

            // Final decision after another 10 seconds
            setTimeout(() => {
              // Randomly decide if application is approved, approved with terms, or rejected
              const decision = Math.random();
              let underwritingDecision: 'approved' | 'approved_with_terms' | 'rejected';
              let decisionReason = '';
              let modifiedTerms: string[] = [];

              if (decision < 0.7) {
                // 70% chance of approval
                underwritingDecision = 'approved';
                decisionReason = 'All requirements met. Application approved.';
              } else if (decision < 0.9) {
                // 20% chance of approval with terms
                underwritingDecision = 'approved_with_terms';
                decisionReason = 'Application approved with modified terms.';
                modifiedTerms = [
                  'Increased excess amount',
                  'Limited coverage for specific items',
                  'Additional security requirements'
                ];
              } else {
                // 10% chance of rejection
                underwritingDecision = 'rejected';
                decisionReason = 'Application does not meet underwriting criteria.';
              }

              // Add a timeline event for final decision
              get().addTimelineEvent(id, {
                date: new Date().toISOString().split('T')[0],
                title: 'Underwriting Decision',
                description: `Your application has been ${underwritingDecision.replace('_', ' ')}.`,
                status: underwritingDecision === 'rejected' ? 'rejected' : 'completed',
                icon: underwritingDecision === 'rejected' ? 'alert-circle' : 'check-circle',
                details: decisionReason
              });

              // Update underwriting details with decision
              get().updateUnderwritingDetails(id, {
                status: 'completed',
                completionDate: new Date().toISOString().split('T')[0],
                decision: underwritingDecision,
                decisionReason,
                modifiedTerms,
                notes: [
                  ...(updatedApplication.underwriting?.notes || []),
                  `Final decision: ${underwritingDecision.replace('_', ' ')}. ${decisionReason}`
                ]
              });

              // If approved or approved with terms, simulate policy issuance after 15 more seconds
              if (underwritingDecision === 'approved' || underwritingDecision === 'approved_with_terms') {
                setTimeout(() => {
                  const policyNumber = `POL-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`;

                  // Add a timeline event for policy issuance preparation
                  get().addTimelineEvent(id, {
                    date: new Date().toISOString().split('T')[0],
                    title: 'Policy Preparation',
                    description: 'Your policy is being prepared.',
                    status: 'completed',
                    icon: 'file-text'
                  });

                  // Issue the policy
                  get().issuePolicy(id, policyNumber);
                }, 15000);
              }
            }, 10000);
          }, 10000);
        }, 10000);
      }

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Show toast notification if document status changed
      if (status === 'verified') {
        showToast(
          'success',
          'Document Verified',
          'Your document has been verified successfully',
          { visibilityTime: 3000 }
        );
      } else if (status === 'rejected') {
        showToast(
          'error',
          'Document Rejected',
          details?.reason || 'Your document has been rejected',
          { visibilityTime: 3000 }
        );
      }

      return;
    } catch (error) {
      console.error('Error updating document status:', error);
      set({
        error: 'Failed to update document status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Add timeline event
  addTimelineEvent: async (id: string, event: Omit<TimelineEvent, 'id'>) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Create new event with ID
      const newEvent: TimelineEvent = {
        ...event,
        id: `event-${Math.random().toString(36).substring(2, 9)}`
      };

      // Update timeline
      const updatedTimeline = [...application.timeline, newEvent];

      // Update the application
      const updatedApplication = {
        ...application,
        timeline: updatedTimeline
      };

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      return;
    } catch (error) {
      console.error('Error adding timeline event:', error);
      set({
        error: 'Failed to add timeline event. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update underwriting details
  updateUnderwritingDetails: async (id: string, details: Partial<UnderwritingDetails>) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Update underwriting details
      const updatedUnderwriting = {
        ...application.underwriting,
        ...details
      };

      // Update application status based on underwriting decision
      let updatedStatus = application.status;
      if (details.decision === 'approved') {
        updatedStatus = 'approved';
      } else if (details.decision === 'approved_with_terms') {
        updatedStatus = 'approved_with_terms';
      } else if (details.decision === 'rejected') {
        updatedStatus = 'rejected';
      }

      // Update the application
      const updatedApplication = {
        ...application,
        underwriting: updatedUnderwriting,
        status: updatedStatus
      };

      // Update timeline based on underwriting decision
      if (details.decision) {
        const currentDate = new Date().toISOString().split('T')[0];
        const updatedTimeline = application.timeline.map(event => {
          if (event.title === 'Underwriting') {
            return { ...event, status: 'completed', date: currentDate };
          } else if (event.title === 'Policy Issuance' && (details.decision === 'approved' || details.decision === 'approved_with_terms')) {
            return { ...event, status: 'current', date: currentDate };
          }
          return event;
        });
        updatedApplication.timeline = updatedTimeline;
      }

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      return;
    } catch (error) {
      console.error('Error updating underwriting details:', error);
      set({
        error: 'Failed to update underwriting details. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Issue policy
  issuePolicy: async (id: string, policyNumber: string, documentId?: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      const currentDate = new Date().toISOString().split('T')[0];

      // Generate a policy document ID if not provided
      const policyDocId = documentId || `doc-policy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      // Update the application
      const updatedApplication = {
        ...application,
        status: 'policy_issued',
        policyNumber,
        policyIssueDate: currentDate,
        policyDocumentId: policyDocId
      };

      // Update timeline
      const updatedTimeline = application.timeline.map(event => {
        if (event.title === 'Policy Issuance') {
          return {
            ...event,
            status: 'completed',
            date: currentDate,
            details: `Your policy (${policyNumber}) has been issued successfully. You can view and download your policy documents from the Documents section.`
          };
        }
        return event;
      });

      // Add a detailed policy issuance event
      const policyIssuanceEvent = {
        id: `event-${Math.random().toString(36).substring(2, 9)}`,
        date: currentDate,
        title: 'Policy Issued',
        description: `Policy ${policyNumber} has been issued.`,
        status: 'completed' as const,
        icon: 'shield',
        details: `Your insurance policy has been issued with policy number ${policyNumber}. The policy is effective from ${currentDate}.`,
        actions: [
          {
            label: 'View Policy Documents',
            action: 'navigate_to_documents'
          }
        ]
      };

      updatedApplication.timeline = [...updatedTimeline, policyIssuanceEvent];

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      // Create a policy from this application
      try {
        // Import dynamically to avoid circular dependency
        const { default: usePolicyStore } = await import('./policyStore');

        // Calculate policy dates
        const startDate = currentDate;
        const endDate = new Date(currentDate);
        endDate.setFullYear(endDate.getFullYear() + 1);
        const endDateStr = endDate.toISOString().split('T')[0];

        // Calculate renewal date (30 days before end date)
        const renewalDate = new Date(endDate);
        renewalDate.setDate(renewalDate.getDate() - 30);
        const renewalDateStr = renewalDate.toISOString().split('T')[0];

        // Create policy details from application
        const policyDetails = {
          applicationId: id,
          quoteId: application.quoteId,
          policyNumber,
          type: application.type.toLowerCase() as any,
          status: 'active',
          startDate,
          endDate: endDateStr,
          renewalDate: renewalDateStr,
          premium: application.premium,
          coverAmount: application.coverAmount,
          currency: application.currency,
          paymentFrequency: 'annual',
          underwriter: 'Inerca Insurance',
          clientInfo: application.clientInfo,
          documents: [
            {
              policyId: '', // Will be set by the policy store
              type: 'policy',
              name: `${application.type} Insurance Policy`,
              description: 'Official policy document',
              documentId: policyDocId,
              issueDate: currentDate
            },
            {
              policyId: '', // Will be set by the policy store
              type: 'schedule',
              name: `${application.type} Policy Schedule`,
              description: 'Policy schedule and coverage details',
              documentId: `doc-schedule-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              issueDate: currentDate
            }
          ],
          // Add initial payment schedule
          payments: [
            {
              policyId: '', // Will be set by the policy store
              amount: application.premium,
              currency: application.currency,
              dueDate: currentDate,
              status: 'due',
              reference: `INV-${policyNumber}`
            }
          ],
          // Add coverage items based on application type
          coverage: [
            {
              name: 'Standard Coverage',
              description: `Standard ${application.type} insurance coverage`,
              coverAmount: application.coverAmount * 0.8,
              premium: application.premium * 0.8,
              isAddOn: false
            },
            {
              name: 'Extended Coverage',
              description: `Extended protection for your ${application.type} insurance`,
              coverAmount: application.coverAmount * 0.2,
              premium: application.premium * 0.2,
              isAddOn: true
            }
          ]
        };

        // Create the policy
        const policyStore = usePolicyStore.getState();
        await policyStore.createPolicyFromApplication(id, policyDetails);
      } catch (policyError) {
        console.error('Error creating policy:', policyError);
        // Continue with application update even if policy creation fails
      }

      // Show success toast
      showToast(
        'success',
        'Policy Issued',
        `Your policy (${policyNumber}) has been issued successfully`,
        { visibilityTime: 3000 }
      );

      return;
    } catch (error) {
      console.error('Error issuing policy:', error);
      set({
        error: 'Failed to issue policy. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Add required action
  addRequiredAction: async (id: string, action: Omit<Application['requiredActions'][0], 'id' | 'completed'>) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Create new action with ID
      const newAction = {
        ...action,
        id: `action-${Math.random().toString(36).substring(2, 9)}`,
        completed: false
      };

      // Update required actions
      const updatedRequiredActions = [...(application.requiredActions || []), newAction];

      // Update the application
      const updatedApplication = {
        ...application,
        requiredActions: updatedRequiredActions
      };

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      return;
    } catch (error) {
      console.error('Error adding required action:', error);
      set({
        error: 'Failed to add required action. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Complete required action
  completeRequiredAction: async (id: string, actionId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the application
      const application = get().applications.find(a => a.id === id);

      if (!application) {
        throw new Error(`Application with ID ${id} not found`);
      }

      // Update required actions
      const updatedRequiredActions = application.requiredActions?.map(action =>
        action.id === actionId
          ? { ...action, completed: true }
          : action
      ) || [];

      // Update the application
      const updatedApplication = {
        ...application,
        requiredActions: updatedRequiredActions
      };

      // Update the state
      let updatedApplications: Application[] = [];
      set(state => {
        const newApplications = state.applications.map(a =>
          a.id === id ? updatedApplication : a
        );
        updatedApplications = newApplications;
        return {
          applications: newApplications,
          currentApplication: state.currentApplication?.id === id ? updatedApplication : state.currentApplication,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveApplicationsToStorage(updatedApplications);

      return;
    } catch (error) {
      console.error('Error completing required action:', error);
      set({
        error: 'Failed to complete required action. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
}));

export default useApplicationStore;
