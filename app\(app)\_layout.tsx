import { Stack } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { StatusBar } from 'expo-status-bar';
import NotificationManager from '@/components/notifications/NotificationManager';

export default function AppLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  return (
    <>
      <StatusBar style={isDarkMode ? "light" : "dark"} />
      <NotificationManager />
      <Stack screenOptions={{ headerShown: false }} />
    </>
  );
}
