/**
 * Claim Store
 * 
 * This store manages the state for claims management using Zustand.
 */

import { create } from 'zustand';
import { Claim, ClaimInput, ClaimStatus, DocumentStatus } from '@/types/claim.types';
import { showToast } from '@/utils/toast';
import claimService from '@/services/claimService';

// Define claim store state
interface ClaimState {
  claims: Claim[];
  currentClaim: Claim | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchClaims: () => Promise<void>;
  getClaimById: (id: string) => Promise<Claim | undefined>;
  createClaim: (claimInput: ClaimInput) => Promise<Claim>;
  updateClaim: (id: string, updates: Partial<Claim>) => Promise<void>;
  submitClaim: (id: string) => Promise<void>;
  updateClaimStatus: (id: string, status: ClaimStatus) => Promise<void>;
  uploadClaimDocument: (claimId: string, document: any) => Promise<void>;
  updateClaimDocumentStatus: (claimId: string, documentId: string, status: DocumentStatus, details?: any) => Promise<void>;
  addClaimNote: (claimId: string, note: string) => Promise<void>;
}

// Create the store
const useClaimStore = create<ClaimState>((set, get) => ({
  claims: [],
  currentClaim: null,
  isLoading: false,
  error: null,
  
  // Fetch all claims
  fetchClaims: async () => {
    set({ isLoading: true, error: null });
    try {
      const claims = await claimService.getAllClaims();
      set({ claims, isLoading: false });
    } catch (error) {
      console.error('Error fetching claims:', error);
      set({
        error: 'Failed to fetch claims. Please try again.',
        isLoading: false
      });
    }
  },
  
  // Get claim by ID
  getClaimById: async (id: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // First check if we have it in state
      const cachedClaim = get().claims.find(claim => claim.id === id);
      
      if (cachedClaim) {
        set({ currentClaim: cachedClaim, isLoading: false });
        return cachedClaim;
      }
      
      // If not in state, fetch from service
      const claim = await claimService.getClaimById(id);
      
      if (claim) {
        // Update state with the fetched claim
        set(state => {
          // Check if claim already exists in state
          const exists = state.claims.some(c => c.id === claim.id);
          
          // If it doesn't exist, add it to the claims array
          const updatedClaims = exists
            ? state.claims.map(c => c.id === claim.id ? claim : c)
            : [claim, ...state.claims];
          
          return {
            claims: updatedClaims,
            currentClaim: claim,
            isLoading: false
          };
        });
        
        return claim;
      }
      
      set({ isLoading: false });
      return undefined;
    } catch (error) {
      console.error(`Error getting claim with ID ${id}:`, error);
      set({
        error: `Failed to get claim details. Please try again.`,
        isLoading: false
      });
      return undefined;
    }
  },
  
  // Create a new claim
  createClaim: async (claimInput: ClaimInput) => {
    set({ isLoading: true, error: null });
    try {
      const newClaim = await claimService.createClaim(claimInput);
      
      // Update state with new claim
      set(state => ({
        claims: [newClaim, ...state.claims],
        currentClaim: newClaim,
        isLoading: false
      }));
      
      // Show success toast
      showToast(
        'success',
        'Claim Created',
        'Your claim has been created successfully',
        { visibilityTime: 3000 }
      );
      
      return newClaim;
    } catch (error) {
      console.error('Error creating claim:', error);
      set({
        error: 'Failed to create claim. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  
  // Update claim
  updateClaim: async (id: string, updates: Partial<Claim>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedClaim = await claimService.updateClaim(id, updates);
      
      // Update state
      set(state => ({
        claims: state.claims.map(c => c.id === id ? updatedClaim : c),
        currentClaim: state.currentClaim?.id === id ? updatedClaim : state.currentClaim,
        isLoading: false
      }));
      
      // Show success toast
      showToast(
        'success',
        'Claim Updated',
        'Your claim has been updated successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error updating claim:', error);
      set({
        error: 'Failed to update claim. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  
  // Submit claim
  submitClaim: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const submittedClaim = await claimService.submitClaim(id);
      
      // Update state
      set(state => ({
        claims: state.claims.map(c => c.id === id ? submittedClaim : c),
        currentClaim: state.currentClaim?.id === id ? submittedClaim : state.currentClaim,
        isLoading: false
      }));
      
      // Show success toast
      showToast(
        'success',
        'Claim Submitted',
        'Your claim has been submitted successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error submitting claim:', error);
      set({
        error: 'Failed to submit claim. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  
  // Update claim status
  updateClaimStatus: async (id: string, status: ClaimStatus) => {
    set({ isLoading: true, error: null });
    try {
      const updatedClaim = await claimService.updateClaimStatus(id, status);
      
      // Update state
      set(state => ({
        claims: state.claims.map(c => c.id === id ? updatedClaim : c),
        currentClaim: state.currentClaim?.id === id ? updatedClaim : state.currentClaim,
        isLoading: false
      }));
      
      // Show success toast
      showToast(
        'success',
        'Status Updated',
        `Claim status updated to ${status.replace('_', ' ')}`,
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error updating claim status:', error);
      set({
        error: 'Failed to update claim status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  
  // Upload claim document
  uploadClaimDocument: async (claimId: string, document: any) => {
    set({ isLoading: true, error: null });
    try {
      // Get the claim
      const claim = get().claims.find(c => c.id === claimId);
      
      if (!claim) {
        throw new Error(`Claim with ID ${claimId} not found`);
      }
      
      const currentDate = new Date().toISOString().split('T')[0];
      
      // Create new document
      const newDocument = {
        id: `doc-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        name: document.name,
        type: document.type,
        status: 'pending' as DocumentStatus,
        date: currentDate,
        required: false,
        documentId: document.id
      };
      
      // Update claim with new document
      const updatedClaim = await claimService.updateClaim(claimId, {
        documents: [...claim.documents, newDocument]
      });
      
      // Update state
      set(state => ({
        claims: state.claims.map(c => c.id === claimId ? updatedClaim : c),
        currentClaim: state.currentClaim?.id === claimId ? updatedClaim : state.currentClaim,
        isLoading: false
      }));
      
      // Show success toast
      showToast(
        'success',
        'Document Uploaded',
        'Your document has been uploaded successfully',
        { visibilityTime: 3000 }
      );
      
      // Simulate document verification after 15 seconds
      setTimeout(() => {
        get().updateClaimDocumentStatus(claimId, newDocument.id, 'verified');
      }, 15000);
    } catch (error) {
      console.error('Error uploading claim document:', error);
      set({
        error: 'Failed to upload document. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  
  // Update claim document status
  updateClaimDocumentStatus: async (claimId: string, documentId: string, status: DocumentStatus, details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Get the claim
      const claim = get().claims.find(c => c.id === claimId);
      
      if (!claim) {
        throw new Error(`Claim with ID ${claimId} not found`);
      }
      
      const currentDate = new Date().toISOString().split('T')[0];
      
      // Update document
      const updatedDocuments = claim.documents.map(doc =>
        doc.id === documentId
          ? {
              ...doc,
              status,
              ...(status === 'verified' ? { verificationDate: currentDate } : {}),
              ...(status === 'rejected' && details?.reason ? { rejectionReason: details.reason } : {})
            }
          : doc
      );
      
      // Update claim with updated documents
      const updatedClaim = await claimService.updateClaim(claimId, {
        documents: updatedDocuments
      });
      
      // Update state
      set(state => ({
        claims: state.claims.map(c => c.id === claimId ? updatedClaim : c),
        currentClaim: state.currentClaim?.id === claimId ? updatedClaim : state.currentClaim,
        isLoading: false
      }));
      
      // Show toast notification if document status changed
      if (status === 'verified') {
        showToast(
          'success',
          'Document Verified',
          'Your document has been verified successfully',
          { visibilityTime: 3000 }
        );
      } else if (status === 'rejected') {
        showToast(
          'error',
          'Document Rejected',
          details?.reason || 'Your document has been rejected',
          { visibilityTime: 3000 }
        );
      }
    } catch (error) {
      console.error('Error updating claim document status:', error);
      set({
        error: 'Failed to update document status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },
  
  // Add claim note
  addClaimNote: async (claimId: string, note: string) => {
    set({ isLoading: true, error: null });
    try {
      // Get the claim
      const claim = get().claims.find(c => c.id === claimId);
      
      if (!claim) {
        throw new Error(`Claim with ID ${claimId} not found`);
      }
      
      // Update claim with new note
      const updatedClaim = await claimService.updateClaim(claimId, {
        notes: [...(claim.notes || []), note]
      });
      
      // Update state
      set(state => ({
        claims: state.claims.map(c => c.id === claimId ? updatedClaim : c),
        currentClaim: state.currentClaim?.id === claimId ? updatedClaim : state.currentClaim,
        isLoading: false
      }));
      
      // Show success toast
      showToast(
        'success',
        'Note Added',
        'Your note has been added successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error adding claim note:', error);
      set({
        error: 'Failed to add note. Please try again.',
        isLoading: false
      });
      throw error;
    }
  }
}));

export default useClaimStore;
