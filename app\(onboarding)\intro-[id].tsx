import { View, StyleSheet, Text, Image, Pressable } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { ArrowLeft, ArrowRight } from 'lucide-react-native';
import Animated, { FadeInRight, FadeOutLeft } from 'react-native-reanimated';
import { useTheme } from '@/context/ThemeContext';
import { StatusBar } from 'expo-status-bar';

// Detailed onboarding content for each step
const detailedContent = [
  {
    id: '1',
    title: 'Get Insurance Quotes',
    description: 'Access our range of insurance products and get instant quotes tailored to your specific needs.',
    features: [
      'Compare multiple policy options',
      'Personalized recommendations',
      'Transparent pricing with no hidden fees'
    ],
    imageUrl: 'https://images.pexels.com/photos/7654589/pexels-photo-7654589.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: '2',
    title: 'Manage Your Policies',
    description: 'Keep track of all your policies in one place with our intuitive dashboard.',
    features: [
      'View policy details and documents',
      'Receive renewal reminders',
      'Update coverage as your needs change'
    ],
    imageUrl: 'https://images.pexels.com/photos/8867433/pexels-photo-8867433.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: '3',
    title: 'Submit & Track Claims',
    description: 'File claims directly through the app and monitor progress in real-time.',
    features: [
      'Simple step-by-step claim filing',
      'Upload supporting documents',
      'Direct communication with claim handlers'
    ],
    imageUrl: 'https://images.pexels.com/photos/7844602/pexels-photo-7844602.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  }
];

export default function DetailedIntroScreen() {
  const { id } = useLocalSearchParams();
  const currentId = typeof id === 'string' ? id : '1';
  const content = detailedContent.find(item => item.id === currentId) || detailedContent[0];

  const currentIndex = parseInt(currentId) - 1;
  const isLastSlide = currentIndex === detailedContent.length - 1;

  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(colors, spacing, typography, borders);

  const handleBack = () => {
    if (currentIndex === 0) {
      router.back();
    } else {
      const prevId = (currentIndex).toString();
      router.replace(`/intro-${prevId}`);
    }
  };

  const handleNext = () => {
    if (isLastSlide) {
      // Complete onboarding
      router.replace('/(app)/(tabs)');
    } else {
      const nextId = (currentIndex + 2).toString();
      router.replace(`/intro-${nextId}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />
      <Animated.View
        style={styles.content}
        entering={FadeInRight}
        exiting={FadeOutLeft}
      >
        <View style={styles.header}>
          <Pressable style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color={colors.primary[500]} />
          </Pressable>
          <Text style={[styles.stepText, { color: colors.textSecondary }]}>Step {currentId} of 3</Text>
        </View>

        <Image
          source={{ uri: content.imageUrl }}
          style={styles.image}
          resizeMode="cover"
        />

        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: colors.text }]}>{content.title}</Text>
          <Text style={[styles.description, { color: colors.textSecondary }]}>{content.description}</Text>

          <View style={styles.featureList}>
            {content.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <View style={[styles.featureDot, { backgroundColor: colors.primary[500] }]} />
                <Text style={[styles.featureText, { color: colors.text }]}>{feature}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.footer}>
          <Pressable
            style={styles.nextButton}
            onPress={handleNext}
          >
            <Text style={styles.nextButtonText}>
              {isLastSlide ? 'Get Started' : 'Next'}
            </Text>
            <ArrowRight size={20} color={colors.white} />
          </Pressable>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
}

const getStyles = (colors, spacing, typography, borders) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  backButton: {
    padding: spacing.xs,
  },
  stepText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
  },
  image: {
    width: '100%',
    height: '35%',
  },
  textContainer: {
    padding: spacing.lg,
  },
  title: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes['2xl'],
    marginBottom: spacing.sm,
  },
  description: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.md,
    lineHeight: typography.lineHeights.normal * typography.sizes.md,
    marginBottom: spacing.lg,
  },
  featureList: {
    marginTop: spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  featureDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.sm,
  },
  featureText: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.md,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.lg,
  },
  nextButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borders.radius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    color: colors.white,
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.md,
    marginRight: spacing.xs,
  },
});