import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, ToastAndroid, Clipboard } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Building, Copy, Check, AlertCircle } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { showToast } from '@/utils/toast';

interface BankDetailsDisplayProps {
  bankDetails: {
    accountName: string;
    accountNumber: string;
    bankName: string;
    branchCode: string;
    reference: string;
  };
  amount: number;
  currency?: string;
  dueDate?: string;
}

const BankDetailsDisplay: React.FC<BankDetailsDisplayProps> = ({
  bankDetails,
  amount,
  currency = 'P',
  dueDate,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  // Format currency
  const formatCurrency = (value: number) => {
    return `${currency} ${value.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Copy text to clipboard
  const copyToClipboard = (text: string, fieldName: string) => {
    Clipboard.setString(text);
    setCopiedField(fieldName);
    
    // Show toast notification
    showToast(
      'success',
      'Copied to Clipboard',
      `${fieldName} has been copied to clipboard`,
      { visibilityTime: 2000 }
    );
    
    // Reset copied field after 2 seconds
    setTimeout(() => {
      setCopiedField(null);
    }, 2000);
  };

  const styles = StyleSheet.create({
    container: {
      marginVertical: spacing.md,
    },
    title: {
      ...typography.h3,
      color: colors.text,
      marginBottom: spacing.md,
    },
    subtitle: {
      ...typography.body,
      color: colors.textSecondary,
      marginBottom: spacing.lg,
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    bankHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
      paddingBottom: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    bankIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.primary[50],
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    bankName: {
      ...typography.h4,
      color: colors.text,
    },
    fieldContainer: {
      marginBottom: spacing.md,
    },
    fieldRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: spacing.sm,
    },
    fieldLabel: {
      ...typography.body,
      color: colors.textSecondary,
      flex: 1,
    },
    fieldValue: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
      flex: 2,
      textAlign: 'right',
      marginRight: spacing.sm,
    },
    copyButton: {
      padding: spacing.xs,
    },
    referenceContainer: {
      backgroundColor: colors.primary[50],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
    },
    referenceLabel: {
      ...typography.caption,
      color: colors.primary[700],
      marginBottom: spacing.xs,
    },
    referenceValue: {
      ...typography.h4,
      color: colors.primary[700],
      fontWeight: 'bold',
    },
    amountContainer: {
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    amountLabel: {
      ...typography.caption,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    amount: {
      ...typography.h2,
      color: colors.text,
      fontWeight: 'bold',
    },
    dueDateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.lg,
    },
    dueDateText: {
      ...typography.body,
      color: colors.error[500],
      marginLeft: spacing.xs,
    },
    instructionsContainer: {
      backgroundColor: colors.info[50],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
    },
    instructionsTitle: {
      ...typography.h4,
      color: colors.info[700],
      marginBottom: spacing.sm,
    },
    instructionsList: {
      marginLeft: spacing.md,
    },
    instructionItem: {
      ...typography.body,
      color: colors.info[700],
      marginBottom: spacing.xs,
    },
  });

  return (
    <Animated.View style={styles.container} entering={FadeIn.duration(300)}>
      <Text style={styles.title}>Bank Payment Details</Text>
      <Text style={styles.subtitle}>
        Please use the following details to make your payment via EFT.
      </Text>

      <View style={styles.amountContainer}>
        <Text style={styles.amountLabel}>Amount Due</Text>
        <Text style={styles.amount}>{formatCurrency(amount)}</Text>
      </View>

      {dueDate && (
        <View style={styles.dueDateContainer}>
          <AlertCircle size={16} color={colors.error[500]} />
          <Text style={styles.dueDateText}>
            Payment due by {formatDate(dueDate)}
          </Text>
        </View>
      )}

      <View style={styles.card}>
        <View style={styles.bankHeader}>
          <View style={styles.bankIconContainer}>
            <Building size={24} color={colors.primary[500]} />
          </View>
          <Text style={styles.bankName}>{bankDetails.bankName}</Text>
        </View>

        <View style={styles.fieldContainer}>
          <View style={styles.fieldRow}>
            <Text style={styles.fieldLabel}>Account Name</Text>
            <Text style={styles.fieldValue}>{bankDetails.accountName}</Text>
            <TouchableOpacity
              style={styles.copyButton}
              onPress={() => copyToClipboard(bankDetails.accountName, 'Account Name')}
            >
              {copiedField === 'Account Name' ? (
                <Check size={20} color={colors.success[500]} />
              ) : (
                <Copy size={20} color={colors.primary[500]} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.fieldRow}>
            <Text style={styles.fieldLabel}>Account Number</Text>
            <Text style={styles.fieldValue}>{bankDetails.accountNumber}</Text>
            <TouchableOpacity
              style={styles.copyButton}
              onPress={() => copyToClipboard(bankDetails.accountNumber, 'Account Number')}
            >
              {copiedField === 'Account Number' ? (
                <Check size={20} color={colors.success[500]} />
              ) : (
                <Copy size={20} color={colors.primary[500]} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.fieldRow}>
            <Text style={styles.fieldLabel}>Branch Code</Text>
            <Text style={styles.fieldValue}>{bankDetails.branchCode}</Text>
            <TouchableOpacity
              style={styles.copyButton}
              onPress={() => copyToClipboard(bankDetails.branchCode, 'Branch Code')}
            >
              {copiedField === 'Branch Code' ? (
                <Check size={20} color={colors.success[500]} />
              ) : (
                <Copy size={20} color={colors.primary[500]} />
              )}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.referenceContainer}>
          <Text style={styles.referenceLabel}>Payment Reference (Important)</Text>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text style={styles.referenceValue}>{bankDetails.reference}</Text>
            <TouchableOpacity
              style={styles.copyButton}
              onPress={() => copyToClipboard(bankDetails.reference, 'Reference')}
            >
              {copiedField === 'Reference' ? (
                <Check size={20} color={colors.success[500]} />
              ) : (
                <Copy size={20} color={colors.primary[500]} />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsTitle}>Payment Instructions</Text>
        <View style={styles.instructionsList}>
          <Text style={styles.instructionItem}>
            • Use your bank's online banking or mobile app to make the payment
          </Text>
          <Text style={styles.instructionItem}>
            • Enter the exact amount shown above
          </Text>
          <Text style={styles.instructionItem}>
            • Use the reference number provided to ensure your payment is correctly allocated
          </Text>
          <Text style={styles.instructionItem}>
            • After making the payment, upload proof of payment in the next step
          </Text>
          <Text style={styles.instructionItem}>
            • Payment verification may take up to 24 hours
          </Text>
        </View>
      </View>
    </Animated.View>
  );
};

export default BankDetailsDisplay;
