import apiService from './api';
import { Notification } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';





// Configure Expo Notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Register for push notifications
const registerForPushNotifications = async () => {
  if (!Device.isDevice) {
    console.log('Push notifications are not available in the simulator');
    return null;
  }

  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    const token = (await Notifications.getExpoPushTokenAsync()).data;
    console.log('Expo push token:', token);

    // Save token to AsyncStorage
    await AsyncStorage.setItem('pushToken', token);

    // Configure for Android
    if (Platform.OS === 'android') {
      Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    return token;
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return null;
  }
};

// Notification service methods
export const notificationService = {
  // Register for push notifications
  registerForPushNotifications,

  // Get all notifications
  getNotifications: async (): Promise<Notification[]> => {
    try {
      console.log('[NotificationService] Getting notifications from API');
      const response = await apiService.notifications.getNotifications();
      console.log('[NotificationService] Notifications retrieved successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[NotificationService] Error getting notifications:', error);
      throw error;
    }
  },

  // Mark notification as read
  markAsRead: async (id: string): Promise<void> => {
    try {
      console.log('[NotificationService] Marking notification as read:', id);
      await apiService.notifications.markAsRead(id);
      console.log('[NotificationService] Notification marked as read successfully');
    } catch (error) {
      console.error('[NotificationService] Error marking notification as read:', id, error);
      throw error;
    }
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    try {
      console.log('[NotificationService] Marking all notifications as read');
      await apiService.notifications.markAllAsRead();
      console.log('[NotificationService] All notifications marked as read successfully');
    } catch (error) {
      console.error('[NotificationService] Error marking all notifications as read:', error);
      throw error;
    }
  },

  // Delete notification
  deleteNotification: async (id: string): Promise<void> => {
    try {
      console.log('[NotificationService] Deleting notification:', id);
      await apiService.notifications.deleteNotification(id);
      console.log('[NotificationService] Notification deleted successfully');
    } catch (error) {
      console.error('[NotificationService] Error deleting notification:', id, error);
      throw error;
    }
  },

  // Send local notification
  sendLocalNotification: async (title: string, body: string, data?: any): Promise<void> => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending local notification:', error);
      throw error;
    }
  },

  // Clear all notifications
  clearAllNotifications: async (): Promise<void> => {
    try {
      console.log('[NotificationService] Clearing all notifications');
      // Note: This endpoint may not exist in the API, implement if needed
      // await apiService.notifications.clearAllNotifications();
      console.log('[NotificationService] All notifications cleared successfully');
    } catch (error) {
      console.error('[NotificationService] Error clearing all notifications:', error);
      throw error;
    }
  },

  // Send local push notification
  sendLocalNotification: async (title: string, message: string, data?: any): Promise<void> => {
    try {
      console.log('[NotificationService] Sending local notification:', title);

      // Import Notifications dynamically to avoid issues if expo-notifications is not available
      const { Notifications } = await import('expo-notifications');

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body: message,
          data: data || {},
          sound: true,
        },
        trigger: null, // Send immediately
      });

      console.log('[NotificationService] Local notification sent successfully');
    } catch (error) {
      console.error('[NotificationService] Error sending local notification:', error);
      // Don't throw error for local notifications as it's not critical
    }
  },

  // Request notification permissions
  requestPermissions: async (): Promise<boolean> => {
    try {
      console.log('[NotificationService] Requesting notification permissions');

      const { Notifications } = await import('expo-notifications');

      const { status } = await Notifications.requestPermissionsAsync();
      const granted = status === 'granted';

      console.log('[NotificationService] Notification permissions:', granted ? 'granted' : 'denied');
      return granted;
    } catch (error) {
      console.error('[NotificationService] Error requesting notification permissions:', error);
      return false;
    }
  },

  // Configure notification behavior
  configureNotifications: async (): Promise<void> => {
    try {
      console.log('[NotificationService] Configuring notifications');

      const { Notifications } = await import('expo-notifications');

      // Set notification handler
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });

      console.log('[NotificationService] Notifications configured successfully');
    } catch (error) {
      console.error('[NotificationService] Error configuring notifications:', error);
    }
  },

  // Enhanced notification triggers for new workflow
  triggerDocumentVerification: async (documentName: string, flowId: string): Promise<void> => {
    try {
      console.log('[NotificationService] Triggering document verification for:', documentName);

      // Simulate verification delay (10-20 seconds)
      const delay = Math.random() * 10000 + 10000; // 10-20 seconds

      setTimeout(async () => {
        try {
          const { Notifications } = await import('expo-notifications');

          await Notifications.scheduleNotificationAsync({
            content: {
              title: 'Document Verified ✅',
              body: `Your ${documentName} has been verified and approved.`,
              data: { flowId, documentName, type: 'document_verified' },
              sound: true,
            },
            trigger: null,
          });

          console.log('[NotificationService] Document verification notification sent');
        } catch (error) {
          console.error('[NotificationService] Error sending verification notification:', error);
        }
      }, delay);

    } catch (error) {
      console.error('[NotificationService] Error triggering document verification:', error);
    }
  },

  triggerPaymentVerification: async (amount: number, reference: string, flowId: string): Promise<void> => {
    try {
      console.log('[NotificationService] Triggering payment verification for:', reference);

      // Simulate verification delay (10-20 seconds)
      const delay = Math.random() * 10000 + 10000; // 10-20 seconds

      setTimeout(async () => {
        try {
          const { Notifications } = await import('expo-notifications');

          await Notifications.scheduleNotificationAsync({
            content: {
              title: 'Payment Verified ✅',
              body: `Your payment of BWP ${amount.toFixed(2)} has been verified and processed.`,
              data: { flowId, reference, amount, type: 'payment_verified' },
              sound: true,
            },
            trigger: null,
          });

          console.log('[NotificationService] Payment verification notification sent');
        } catch (error) {
          console.error('[NotificationService] Error sending payment verification notification:', error);
        }
      }, delay);

    } catch (error) {
      console.error('[NotificationService] Error triggering payment verification:', error);
    }
  },

  triggerPolicyReview: async (policyNumber: string, flowId: string): Promise<void> => {
    try {
      console.log('[NotificationService] Triggering policy review for:', policyNumber);

      // Simulate review delay (10-20 seconds)
      const delay = Math.random() * 10000 + 10000; // 10-20 seconds

      setTimeout(async () => {
        try {
          const { Notifications } = await import('expo-notifications');

          await Notifications.scheduleNotificationAsync({
            content: {
              title: 'Policy Ready 🎉',
              body: `Your policy ${policyNumber} has been reviewed and is ready for dispatch.`,
              data: { flowId, policyNumber, type: 'policy_dispatched' },
              sound: true,
            },
            trigger: null,
          });

          console.log('[NotificationService] Policy review notification sent');
        } catch (error) {
          console.error('[NotificationService] Error sending policy review notification:', error);
        }
      }, delay);

    } catch (error) {
      console.error('[NotificationService] Error triggering policy review:', error);
    }
  },

  // Workflow-specific notification helpers
  notifyQuotationSent: async (quoteId: string, type: string): Promise<void> => {
    try {
      await notificationService.sendLocalNotification(
        'Quotation Sent 📋',
        `Your ${type} quotation has been generated and sent successfully.`,
        { quoteId, type: 'quotation_sent' }
      );
    } catch (error) {
      console.error('[NotificationService] Error sending quotation notification:', error);
    }
  },

  notifyDocumentReminder: async (documentType: string): Promise<void> => {
    try {
      await notificationService.sendLocalNotification(
        'Document Upload Reminder 📄',
        `Please upload your ${documentType} to continue with your application.`,
        { documentType, type: 'document_reminder' }
      );
    } catch (error) {
      console.error('[NotificationService] Error sending document reminder:', error);
    }
  },

  notifyAdminNote: async (message: string, isApproval: boolean): Promise<void> => {
    try {
      const title = isApproval ? 'Application Approved ✅' : 'Action Required ⚠️';
      await notificationService.sendLocalNotification(
        title,
        message,
        { isApproval, type: 'admin_notes' }
      );
    } catch (error) {
      console.error('[NotificationService] Error sending admin note:', error);
    }
  },

  notifyRenewalDue: async (policyNumber: string, dueDate: string): Promise<void> => {
    try {
      await notificationService.sendLocalNotification(
        'Policy Renewal Due 🔄',
        `Your policy ${policyNumber} is due for renewal on ${dueDate}.`,
        { policyNumber, dueDate, type: 'renewal_reminder' }
      );
    } catch (error) {
      console.error('[NotificationService] Error sending renewal notification:', error);
    }
  },
};

export default notificationService;
