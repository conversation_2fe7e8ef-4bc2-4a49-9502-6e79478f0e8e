import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  Search,
  ChevronDown,
  ChevronRight,
  HelpCircle,
  Shield,
  CreditCard,
  FileText,
  Phone,
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface FAQCategory {
  id: string;
  title: string;
  icon: any;
  color: string;
}

export default function FAQScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories: FAQCategory[] = [
    { id: 'general', title: 'General', icon: HelpCircle, color: colors.primary[500] },
    { id: 'insurance', title: 'Insurance', icon: Shield, color: colors.secondary[500] },
    { id: 'payments', title: 'Payments', icon: CreditCard, color: colors.success[500] },
    { id: 'claims', title: 'Claims', icon: FileText, color: colors.warning[500] },
    { id: 'support', title: 'Support', icon: Phone, color: colors.error[500] },
  ];

  const faqData: FAQItem[] = [
    {
      id: '1',
      category: 'general',
      question: 'What is Inerca Insurance?',
      answer: 'Inerca Insurance is a comprehensive insurance platform that provides various insurance products including motor, home, life, and business insurance. We aim to make insurance accessible and affordable for everyone in Botswana.',
    },
    {
      id: '2',
      category: 'general',
      question: 'How do I create an account?',
      answer: 'You can create an account by downloading our mobile app and following the registration process. You\'ll need to provide your email, phone number, and complete identity verification.',
    },
    {
      id: '3',
      category: 'insurance',
      question: 'What types of insurance do you offer?',
      answer: 'We offer Motor Insurance, Houseowners Insurance, Household Contents Insurance, All Risks Insurance, Life Assurance, and Business Insurance through our partners Holland Insurance and Botswana Life.',
    },
    {
      id: '4',
      category: 'insurance',
      question: 'How do I get a quote?',
      answer: 'You can get a quote by selecting the type of insurance you need in our app, filling out the required information, and our system will generate a personalized quote for you within minutes.',
    },
    {
      id: '5',
      category: 'payments',
      question: 'What payment methods do you accept?',
      answer: 'We accept bank transfers, debit orders, and cash deposits. You can upload proof of payment through the app for verification.',
    },
    {
      id: '6',
      category: 'payments',
      question: 'When do I need to pay my premium?',
      answer: 'Premium payments are due according to your chosen payment frequency (monthly or annually). You\'ll receive notifications before your payment is due.',
    },
    {
      id: '7',
      category: 'claims',
      question: 'How do I file a claim?',
      answer: 'You can file a claim through the app by going to the Claims section, selecting your policy, and providing details about the incident. You\'ll also need to upload supporting documents.',
    },
    {
      id: '8',
      category: 'claims',
      question: 'How long does claim processing take?',
      answer: 'Claim processing typically takes 5-10 business days, depending on the complexity of the claim and the completeness of submitted documents.',
    },
    {
      id: '9',
      category: 'support',
      question: 'How can I contact customer support?',
      answer: 'You can contact our support team through the in-app chat, email <NAME_EMAIL>, or call our customer service line during business hours.',
    },
    {
      id: '10',
      category: 'support',
      question: 'What are your business hours?',
      answer: 'Our customer service is available Monday to Friday, 8:00 AM to 5:00 PM (CAT). Emergency claims support is available 24/7.',
    },
  ];

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const filteredFAQs = faqData.filter((item) => {
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === null || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const renderCategoryButton = (category: FAQCategory) => {
    const isSelected = selectedCategory === category.id;
    const Icon = category.icon;

    return (
      <TouchableOpacity
        key={category.id}
        style={[
          styles.categoryButton,
          {
            backgroundColor: isSelected ? category.color : colors.card,
            borderColor: isSelected ? category.color : colors.border,
          },
        ]}
        onPress={() => setSelectedCategory(isSelected ? null : category.id)}
      >
        <Icon 
          size={20} 
          color={isSelected ? colors.white : category.color} 
        />
        <Text
          style={[
            styles.categoryText,
            {
              color: isSelected ? colors.white : colors.text,
            },
          ]}
        >
          {category.title}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderFAQItem = (item: FAQItem, index: number) => {
    const isExpanded = expandedItems.has(item.id);

    return (
      <Animated.View
        key={item.id}
        entering={FadeInDown.delay(index * 50).springify()}
        style={[styles.faqItem, { backgroundColor: colors.card, borderColor: colors.border }]}
      >
        <TouchableOpacity
          style={styles.faqHeader}
          onPress={() => toggleExpanded(item.id)}
        >
          <Text style={[styles.faqQuestion, { color: colors.text }]}>
            {item.question}
          </Text>
          {isExpanded ? (
            <ChevronDown size={20} color={colors.textSecondary} />
          ) : (
            <ChevronRight size={20} color={colors.textSecondary} />
          )}
        </TouchableOpacity>
        {isExpanded && (
          <View style={styles.faqAnswer}>
            <Text style={[styles.faqAnswerText, { color: colors.textSecondary }]}>
              {item.answer}
            </Text>
          </View>
        )}
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: spacing.sm,
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
    },
    searchContainer: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
    },
    searchInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      borderColor: colors.border,
      paddingHorizontal: spacing.md,
    },
    searchIcon: {
      marginRight: spacing.sm,
    },
    searchInput: {
      flex: 1,
      paddingVertical: spacing.sm,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    categoriesContainer: {
      paddingHorizontal: spacing.lg,
      paddingBottom: spacing.md,
    },
    categoriesScrollView: {
      flexDirection: 'row',
    },
    categoryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      marginRight: spacing.sm,
    },
    categoryText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      marginLeft: spacing.xs,
    },
    faqList: {
      paddingHorizontal: spacing.lg,
    },
    faqItem: {
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      marginBottom: spacing.md,
      overflow: 'hidden',
    },
    faqHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
    },
    faqQuestion: {
      flex: 1,
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginRight: spacing.sm,
    },
    faqAnswer: {
      paddingHorizontal: spacing.md,
      paddingBottom: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    faqAnswerText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      lineHeight: 20,
      marginTop: spacing.sm,
    },
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: spacing.xl,
    },
    emptyText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Frequently Asked Questions</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Search size={20} color={colors.textSecondary} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search FAQs..."
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        <View style={styles.categoriesContainer}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScrollView}
          >
            {categories.map(renderCategoryButton)}
          </ScrollView>
        </View>

        <View style={styles.faqList}>
          {filteredFAQs.length > 0 ? (
            filteredFAQs.map((item, index) => renderFAQItem(item, index))
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                No FAQs found matching your search criteria.
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
