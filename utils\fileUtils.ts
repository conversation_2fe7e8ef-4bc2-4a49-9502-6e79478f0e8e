import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import { Platform } from 'react-native';
import Toast from 'react-native-toast-message';

/**
 * Get MIME type based on file extension
 */
export const getMimeType = (fileUri: string): string => {
  const extension = fileUri.split('.').pop()?.toLowerCase() || '';

  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'doc':
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'xls':
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'ppt':
    case 'pptx':
      return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    case 'txt':
      return 'text/plain';
    case 'rtf':
      return 'text/rtf';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    default:
      return 'application/octet-stream';
  }
};

/**
 * Open a file in an external viewer
 */
export const openFileInExternalViewer = async (fileUri: string, mimeType?: string): Promise<void> => {
  try {
    console.log(`[FileUtils] Opening file in external viewer: ${fileUri}`);

    // Determine MIME type if not provided
    const fileMimeType = mimeType || getMimeType(fileUri);

    // For Android, we need to use content:// URIs instead of file:// URIs
    if (Platform.OS === 'android') {
      try {
        // Create a directory for shared files if it doesn't exist
        const sharedDir = `${FileSystem.cacheDirectory}shared/`;
        const dirInfo = await FileSystem.getInfoAsync(sharedDir);
        if (!dirInfo.exists) {
          await FileSystem.makeDirectoryAsync(sharedDir, { intermediates: true });
        }

        // Generate a unique filename to avoid conflicts
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 8);
        const originalFilename = fileUri.split('/').pop() || 'file';
        const filename = `${timestamp}_${randomString}_${originalFilename}`;
        const destinationUri = `${sharedDir}${filename}`;

        console.log(`[FileUtils] Copying file to: ${destinationUri}`);

        // Copy the file to the shared directory
        await FileSystem.copyAsync({
          from: fileUri,
          to: destinationUri
        });

        // Use IntentLauncher with the content URI
        console.log(`[FileUtils] Opening with mime type: ${fileMimeType}`);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: destinationUri,
          flags: 1,
          type: fileMimeType
        });

        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Opening file in external viewer',
          visibilityTime: 2000,
          topOffset: 60,
        });
      } catch (innerError) {
        console.error(`[FileUtils] Inner error opening file:`, innerError);

        // Fallback to direct sharing if the above method fails
        console.log(`[FileUtils] Falling back to direct sharing`);
        await Sharing.shareAsync(fileUri, {
          mimeType: fileMimeType,
          dialogTitle: 'View File'
        });
      }
    } else {
      // On iOS, we can use Sharing directly with proper UTI
      console.log(`[FileUtils] iOS sharing with UTI: ${getUTIForMimeType(fileMimeType)}`);
      await Sharing.shareAsync(fileUri, {
        UTI: getUTIForMimeType(fileMimeType),
        mimeType: fileMimeType,
        dialogTitle: 'View File'
      });

      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Opening file in external viewer',
        visibilityTime: 2000,
        topOffset: 60,
      });
    }
  } catch (error) {
    console.error(`[FileUtils] Error opening file in external viewer:`, error);
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: 'Failed to open file. Please try again.',
      visibilityTime: 4000,
      topOffset: 60,
    });
    throw error;
  }
};

/**
 * Get UTI for MIME type (iOS only)
 */
const getUTIForMimeType = (mimeType: string): string => {
  switch (mimeType) {
    case 'application/pdf':
      return 'com.adobe.pdf';
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return 'org.openxmlformats.wordprocessingml.document';
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return 'org.openxmlformats.spreadsheetml.sheet';
    case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      return 'org.openxmlformats.presentationml.presentation';
    case 'text/plain':
      return 'public.plain-text';
    case 'text/rtf':
      return 'public.rtf';
    case 'image/jpeg':
      return 'public.jpeg';
    case 'image/png':
      return 'public.png';
    case 'image/gif':
      return 'public.gif';
    default:
      return 'public.data';
  }
};
