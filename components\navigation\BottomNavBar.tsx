import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Home, FileText, ClipboardList, User, Settings } from 'lucide-react-native';
import { router, usePathname } from 'expo-router';
import Animated, { FadeInUp } from 'react-native-reanimated';

interface BottomNavBarProps {
  currentRoute?: string;
}

const BottomNavBar: React.FC<BottomNavBarProps> = ({ currentRoute }) => {
  const { isDarkMode } = useTheme();
  const { colors, typography } = createTheme(isDarkMode);

  // Get current path to determine active tab
  const pathname = usePathname();

  // Determine active route based on pathname or passed currentRoute
  const getActiveRoute = () => {
    if (currentRoute) return currentRoute;

    if (pathname.includes('/(app)/(tabs)/policies')) return 'policies';
    if (pathname.includes('/(app)/(tabs)/applications')) return 'applications';
    if (pathname.includes('/(app)/(tabs)/profile')) return 'profile';
    if (pathname.includes('/(app)/(tabs)/settings')) return 'settings';
    if (pathname.includes('/(app)/(tabs)/')) return 'home';

    return '';
  };

  const activeRoute = getActiveRoute();

  // Define navigation items
  const navItems = [
    {
      id: 'home',
      title: 'Home',
      icon: Home,
      route: '/(app)/(tabs)/',
      active: activeRoute === 'home',
    },
    {
      id: 'policies',
      title: 'Policies',
      icon: FileText,
      route: '/(app)/(tabs)/policies',
      active: activeRoute === 'policies',
    },
    {
      id: 'applications',
      title: 'Applications',
      icon: ClipboardList,
      route: '/(app)/(tabs)/applications',
      active: activeRoute === 'applications',
    },
    {
      id: 'profile',
      title: 'Profile',
      icon: User,
      route: '/(app)/(tabs)/profile',
      active: activeRoute === 'profile',
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: Settings,
      route: '/(app)/(tabs)/settings',
      active: activeRoute === 'settings',
    },
  ];

  // Handle navigation
  const handleNavigation = (route: string) => {
    router.push(route);
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: colors.background,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      height: Platform.OS === 'ios' ? 88 : 60,
      paddingBottom: Platform.OS === 'ios' ? 28 : 8,
      paddingTop: 8,
    },
    navButton: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    iconContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    label: {
      fontFamily: typography.fonts.medium,
      fontSize: 12,
      marginTop: 4,
      color: colors.textSecondary,
    },
    activeLabel: {
      color: colors.primary[500],
    },
  });

  return (
    <Animated.View
      style={styles.container}
      entering={FadeInUp.duration(300)}
    >
      {navItems.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={styles.navButton}
          onPress={() => handleNavigation(item.route)}
          activeOpacity={0.7}
        >
          <View style={styles.iconContainer}>
            <item.icon
              size={24}
              color={item.active ? colors.primary[500] : colors.textSecondary}
            />
          </View>
          <Text
            style={[
              styles.label,
              item.active && styles.activeLabel,
            ]}
          >
            {item.title}
          </Text>
        </TouchableOpacity>
      ))}
    </Animated.View>
  );
};

export default BottomNavBar;
