import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Platform,
  ScrollView,
  Modal,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Calendar, ChevronDown, AlertCircle } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';

// Field types
export type FieldType =
  | 'text'
  | 'email'
  | 'phone'
  | 'number'
  | 'date'
  | 'select'
  | 'switch'
  | 'textarea';

// Option for select fields
export interface SelectOption {
  value: string;
  label: string;
}

// Props for the DynamicFormField component
interface DynamicFormFieldProps {
  label: string;
  type: FieldType;
  value: any;
  onChange: (value: any) => void;
  placeholder?: string;
  options?: SelectOption[];
  error?: string;
  required?: boolean;
  icon?: React.ReactNode;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  onBlur?: () => void;
}

const DynamicFormField: React.FC<DynamicFormFieldProps> = ({
  label,
  type,
  value,
  onChange,
  placeholder,
  options = [],
  error,
  required = false,
  icon,
  keyboardType = 'default',
  autoCapitalize = 'none',
  onBlur,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // State for date picker
  const [showDatePicker, setShowDatePicker] = useState(false);

  // State for select dropdown
  const [showDropdown, setShowDropdown] = useState(false);

  // Handle date change
  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      onChange(selectedDate.toISOString().split('T')[0]);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    // Use a simple date formatter instead of toLocaleDateString to avoid locale issues
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.md,
    },
    labelContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    label: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    required: {
      color: colors.error[500],
      marginLeft: 2,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: error ? colors.error[500] : colors.border,
      borderRadius: borders.radius.md,
      backgroundColor: colors.card,
      paddingHorizontal: spacing.md,
      minHeight: 50,
    },
    input: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      paddingVertical: spacing.sm,
      marginLeft: icon ? spacing.sm : 0,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    errorText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.error[500],
      marginTop: spacing.xs,
    },
    iconContainer: {
      marginRight: spacing.xs,
    },
    datePickerButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: spacing.sm,
    },
    dateText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: value ? colors.text : colors.textSecondary,
      marginLeft: spacing.sm,
    },
    selectButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacing.sm,
    },
    selectText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: value ? colors.text : colors.textSecondary,
      marginLeft: icon ? spacing.sm : 0,
    },
    // Modal styles for dropdown
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.lg,
    },
    modalContent: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      width: '100%',
      maxWidth: 400,
      maxHeight: '80%',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 16,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    modalCloseButton: {
      padding: spacing.sm,
      borderRadius: borders.radius.full,
      backgroundColor: colors.neutral[200],
      width: 32,
      height: 32,
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalCloseText: {
      fontFamily: typography.fonts.bold,
      fontSize: 16,
      color: colors.neutral[700],
      lineHeight: 16,
    },
    modalScrollView: {
      maxHeight: 300,
    },
    modalOptionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    selectedOptionItem: {
      backgroundColor: colors.primary[50],
    },
    lastModalOptionItem: {
      borderBottomWidth: 0,
    },
    modalOptionText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      flex: 1,
    },
    selectedOptionText: {
      fontFamily: typography.fonts.medium,
      color: colors.primary[600],
    },
    checkmark: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.primary[600],
      marginLeft: spacing.sm,
    },
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacing.xs,
    },
    switchLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
  });

  // Render the appropriate input based on the field type
  const renderInput = () => {
    switch (type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'number':
        return (
          <View style={styles.inputWrapper}>
            {icon && <View style={styles.iconContainer}>{icon}</View>}
            <TextInput
              style={styles.input}
              value={value}
              onChangeText={onChange}
              placeholder={placeholder}
              placeholderTextColor={colors.textSecondary}
              keyboardType={keyboardType}
              autoCapitalize={autoCapitalize}
              onBlur={onBlur}
            />
          </View>
        );

      case 'textarea':
        return (
          <View style={styles.inputWrapper}>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={value}
              onChangeText={onChange}
              placeholder={placeholder}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              onBlur={onBlur}
            />
          </View>
        );

      case 'date':
        return (
          <>
            <View style={styles.inputWrapper}>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Calendar size={20} color={colors.textSecondary} />
                <Text style={styles.dateText}>
                  {value ? formatDate(value) : placeholder || 'Select date'}
                </Text>
              </TouchableOpacity>
            </View>
            {showDatePicker && (
              <DateTimePicker
                value={value ? new Date(value) : new Date()}
                mode="date"
                display="default"
                onChange={handleDateChange}
              />
            )}
          </>
        );

      case 'select':
        return (
          <>
            <View style={styles.inputWrapper}>
              <TouchableOpacity
                style={styles.selectButton}
                onPress={() => setShowDropdown(true)}
              >
                {icon && <View style={styles.iconContainer}>{icon}</View>}
                <Text style={styles.selectText}>
                  {value
                    ? options.find(option => option.value === value)?.label || value
                    : placeholder || 'Select an option'}
                </Text>
                <ChevronDown
                  size={20}
                  color={colors.textSecondary}
                  style={{
                    transform: [{ rotate: showDropdown ? '180deg' : '0deg' }],
                  }}
                />
              </TouchableOpacity>
            </View>

            <Modal
              visible={showDropdown}
              transparent={true}
              animationType="fade"
              onRequestClose={() => setShowDropdown(false)}
            >
              <TouchableOpacity
                style={styles.modalOverlay}
                activeOpacity={1}
                onPress={() => setShowDropdown(false)}
              >
                <View style={styles.modalContent}>
                  <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>{label}</Text>
                    <TouchableOpacity
                      style={styles.modalCloseButton}
                      onPress={() => setShowDropdown(false)}
                    >
                      <Text style={styles.modalCloseText}>✕</Text>
                    </TouchableOpacity>
                  </View>
                  <ScrollView
                    style={styles.modalScrollView}
                    showsVerticalScrollIndicator={false}
                  >
                    {options.map((option, index) => (
                      <TouchableOpacity
                        key={option.value}
                        style={[
                          styles.modalOptionItem,
                          value === option.value && styles.selectedOptionItem,
                          index === options.length - 1 && styles.lastModalOptionItem
                        ]}
                        onPress={() => {
                          onChange(option.value);
                          setShowDropdown(false);
                        }}
                      >
                        <Text style={[
                          styles.modalOptionText,
                          value === option.value && styles.selectedOptionText
                        ]}>
                          {option.label}
                        </Text>
                        {value === option.value && (
                          <Text style={styles.checkmark}>✓</Text>
                        )}
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </TouchableOpacity>
            </Modal>
          </>
        );

      case 'switch':
        return (
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>{label}</Text>
            <Switch
              value={Boolean(value)}
              onValueChange={onChange}
              trackColor={{ false: colors.neutral[300], true: colors.primary[500] }}
              thumbColor={colors.white}
            />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <Animated.View
      style={styles.container}
      entering={FadeInDown.delay(100).springify()}
    >
      {type !== 'switch' && (
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {required && <Text style={styles.required}>*</Text>}
        </View>
      )}

      {renderInput()}

      {error && (
        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: spacing.xs }}>
          <AlertCircle size={14} color={colors.error[500]} style={{ marginRight: 4 }} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </Animated.View>
  );
};

export default DynamicFormField;
