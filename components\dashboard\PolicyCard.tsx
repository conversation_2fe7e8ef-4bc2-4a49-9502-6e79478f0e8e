import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { ChevronRight } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';

type PolicyProps = {
  id: string;
  type: string;
  name: string;
  policyNumber: string;
  coverageAmount: string;
  premium: string;
  nextPayment: string;
  status: string;
  icon: React.ComponentType<any>;
};

type PolicyCardProps = {
  policy: PolicyProps;
};

export default function PolicyCard({ policy }: PolicyCardProps) {
  const { name, policyNumber, premium, status, coverageAmount, icon: Icon } = policy;
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders, shadows } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(colors, spacing, typography, borders, shadows);

  const getPolicyStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return colors.success[500];
      case 'pending':
        return colors.warning[500];
      case 'expired':
        return colors.error[500];
      default:
        return colors.neutral[500];
    }
  };

  const statusColor = getPolicyStatusColor(status);

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: colors.card }]}
      onPress={() => console.log(`Policy ${policyNumber} pressed`)}
    >
      <View style={[styles.header, { borderBottomColor: isDarkMode ? colors.border : colors.neutral[100] }]}>
        <View style={[styles.iconContainer, { backgroundColor: `${colors.primary[500]}20` }]}>
          <Icon size={20} color={colors.primary[500]} />
        </View>
        <View style={styles.headerTextContainer}>
          <Text style={[styles.policyName, { color: colors.text }]}>{name}</Text>
          <Text style={[styles.policyNumber, { color: colors.textSecondary }]}>{policyNumber}</Text>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Premium</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>{premium}/mo</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Coverage</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>{coverageAmount}</Text>
        </View>

        <View style={styles.detailItem}>
          <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Status</Text>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
            <Text style={[styles.statusText, { color: statusColor }]}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Text>
          </View>
        </View>
      </View>

      <View style={[styles.footer, { borderTopColor: isDarkMode ? colors.border : colors.neutral[100] }]}>
        <TouchableOpacity
          style={styles.viewDetailsButton}
          onPress={() => console.log(`View details for ${policyNumber}`)}
        >
          <Text style={[styles.viewDetailsText, { color: colors.primary[500] }]}>View Details</Text>
          <ChevronRight size={16} color={colors.primary[500]} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
}

// Define styles with a function to access theme values
const getStyles = (colors, spacing, typography, borders, shadows) => StyleSheet.create({
  container: {
    borderRadius: borders.radius.lg,
    marginBottom: spacing.md,
    overflow: 'hidden',
    ...shadows.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: borders.radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  headerTextContainer: {
    flex: 1,
  },
  policyName: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.md,
  },
  policyNumber: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.sm,
  },
  content: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.xs,
  },
  detailLabel: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.sm,
  },
  detailValue: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
  },
  footer: {
    borderTopWidth: 1,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewDetailsText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
    marginRight: 4,
  },
});