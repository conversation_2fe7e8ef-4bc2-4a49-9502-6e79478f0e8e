import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { CreditCard, Upload, AlertCircle, Check, Clock, ChevronDown, ChevronUp } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Application, PaymentMethod, PaymentStatus } from '@/store/applicationStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import { router } from 'expo-router';
import { showToast } from '@/utils/toast';

interface PaymentProcessingSectionProps {
  application: Application;
  onUpdatePaymentMethod: (method: PaymentMethod) => Promise<boolean | void>;
}

const PaymentProcessingSection: React.FC<PaymentProcessingSectionProps> = ({
  application,
  onUpdatePaymentMethod,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>(application.payment.method || 'eft');
  const [showBankDetails, setShowBankDetails] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Handle payment method selection
  const handleMethodSelect = async (method: PaymentMethod) => {
    setSelectedMethod(method);
    try {
      await onUpdatePaymentMethod(method);
      showToast(
        'success',
        'Payment Method Updated',
        `Payment method set to ${method === 'eft' ? 'EFT' : 'Direct Debit'}`,
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error updating payment method:', error);
      showToast(
        'error',
        'Update Failed',
        'Failed to update payment method. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle payment proof upload
  const handleUploadPaymentProof = async () => {
    setIsUploading(true);
    try {
      // Navigate to payment upload screen with application ID
      router.push({
        pathname: '/payment',
        params: { applicationId: application.id }
      });
    } catch (error) {
      console.error('Error navigating to payment upload:', error);
      showToast(
        'error',
        'Navigation Failed',
        'Failed to open payment upload screen. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsUploading(false);
    }
  };

  // Get payment status info
  const getPaymentStatusInfo = (status: PaymentStatus) => {
    switch (status) {
      case 'verified':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Payment Verified',
        };
      case 'pending':
        return {
          color: colors.warning[500],
          bgColor: colors.warning[50],
          icon: <Clock size={16} color={colors.warning[500]} />,
          text: 'Payment Pending Verification',
        };
      case 'failed':
        return {
          color: colors.error[500],
          bgColor: colors.error[50],
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          text: 'Payment Failed',
        };
      default:
        return {
          color: colors.textSecondary,
          bgColor: colors.neutral[100],
          icon: <CreditCard size={16} color={colors.textSecondary} />,
          text: 'Payment Not Started',
        };
    }
  };

  const paymentStatusInfo = getPaymentStatusInfo(application.payment.status);

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
    },
    paymentInfo: {
      marginBottom: spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    infoLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    methodsContainer: {
      marginBottom: spacing.md,
    },
    methodTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    methodOption: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      marginBottom: spacing.sm,
    },
    methodOptionSelected: {
      borderColor: colors.primary[500],
      backgroundColor: `${colors.primary[500]}10`,
    },
    methodRadio: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: colors.border,
      marginRight: spacing.sm,
      justifyContent: 'center',
      alignItems: 'center',
    },
    methodRadioSelected: {
      borderColor: colors.primary[500],
    },
    methodRadioInner: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: colors.primary[500],
    },
    methodLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    bankDetailsContainer: {
      backgroundColor: colors.background,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.sm,
    },
    bankDetailsHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    bankDetailsTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    bankDetail: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    bankDetailLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    bankDetailValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    uploadButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
    },
    uploadButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: paymentStatusInfo.bgColor,
      borderRadius: borders.radius.md,
      padding: spacing.sm,
      marginTop: spacing.md,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: paymentStatusInfo.color,
      marginLeft: spacing.sm,
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Payment Processing</Text>

      <Animated.View
        style={styles.card}
        entering={FadeInDown.delay(100).springify()}
      >
        <View style={styles.paymentInfo}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Premium Amount</Text>
            <Text style={styles.infoValue}>
              {formatCurrency(application.payment.amount, application.payment.currency)}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Due Date</Text>
            <Text style={styles.infoValue}>{formatDate(application.payment.dueDate || '')}</Text>
          </View>
        </View>

        <View style={styles.methodsContainer}>
          <Text style={styles.methodTitle}>Select Payment Method</Text>

          <TouchableOpacity
            style={[
              styles.methodOption,
              selectedMethod === 'eft' && styles.methodOptionSelected,
            ]}
            onPress={() => handleMethodSelect('eft')}
          >
            <View style={[
              styles.methodRadio,
              selectedMethod === 'eft' && styles.methodRadioSelected,
            ]}>
              {selectedMethod === 'eft' && <View style={styles.methodRadioInner} />}
            </View>
            <Text style={styles.methodLabel}>Electronic Funds Transfer (EFT)</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.methodOption,
              selectedMethod === 'direct_debit' && styles.methodOptionSelected,
            ]}
            onPress={() => handleMethodSelect('direct_debit')}
          >
            <View style={[
              styles.methodRadio,
              selectedMethod === 'direct_debit' && styles.methodRadioSelected,
            ]}>
              {selectedMethod === 'direct_debit' && <View style={styles.methodRadioInner} />}
            </View>
            <Text style={styles.methodLabel}>Direct Debit Order</Text>
          </TouchableOpacity>
        </View>

        {selectedMethod === 'eft' && (
          <View>
            <TouchableOpacity
              style={styles.bankDetailsHeader}
              onPress={() => setShowBankDetails(!showBankDetails)}
            >
              <Text style={styles.bankDetailsTitle}>Bank Details</Text>
              {showBankDetails ? (
                <ChevronUp size={20} color={colors.text} />
              ) : (
                <ChevronDown size={20} color={colors.text} />
              )}
            </TouchableOpacity>

            {showBankDetails && (
              <View style={styles.bankDetailsContainer}>
                <View style={styles.bankDetail}>
                  <Text style={styles.bankDetailLabel}>Account Name</Text>
                  <Text style={styles.bankDetailValue}>{application.payment.bankDetails?.accountName}</Text>
                </View>
                <View style={styles.bankDetail}>
                  <Text style={styles.bankDetailLabel}>Account Number</Text>
                  <Text style={styles.bankDetailValue}>{application.payment.bankDetails?.accountNumber}</Text>
                </View>
                <View style={styles.bankDetail}>
                  <Text style={styles.bankDetailLabel}>Bank Name</Text>
                  <Text style={styles.bankDetailValue}>{application.payment.bankDetails?.bankName}</Text>
                </View>
                <View style={styles.bankDetail}>
                  <Text style={styles.bankDetailLabel}>Branch Code</Text>
                  <Text style={styles.bankDetailValue}>{application.payment.bankDetails?.branchCode}</Text>
                </View>
                <View style={styles.bankDetail}>
                  <Text style={styles.bankDetailLabel}>Reference</Text>
                  <Text style={styles.bankDetailValue}>{application.payment.bankDetails?.reference}</Text>
                </View>
              </View>
            )}

            <TouchableOpacity
              style={styles.uploadButton}
              onPress={handleUploadPaymentProof}
              disabled={isUploading || application.payment.status === 'verified'}
            >
              <Upload size={20} color={colors.white} />
              <Text style={styles.uploadButtonText}>
                {application.payment.status === 'verified'
                  ? 'Payment Verified'
                  : application.payment.status === 'pending'
                    ? 'Update Payment Proof'
                    : 'Upload Payment Proof'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {application.payment.status !== 'not_started' && (
          <View style={styles.statusContainer}>
            {paymentStatusInfo.icon}
            <Text style={styles.statusText}>{paymentStatusInfo.text}</Text>
          </View>
        )}
      </Animated.View>
    </View>
  );
};

export default PaymentProcessingSection;
