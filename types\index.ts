export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
}

export interface Policy {
  id: string;
  type: 'auto' | 'home' | 'life' | 'health' | 'business' | 'other';
  name: string;
  policyNumber: string;
  coverageAmount: string;
  premium: string;
  startDate: string;
  endDate: string;
  nextPayment: string;
  status: 'active' | 'pending' | 'expired' | 'cancelled';
}

export interface Claim {
  id: string;
  policyId: string;
  claimNumber: string;
  dateSubmitted: string;
  status: 'submitted' | 'in_review' | 'approved' | 'denied' | 'pending';
  amount: string;
  description: string;
}

export interface QuoteRequest {
  id: string;
  type: 'auto' | 'home' | 'life' | 'health' | 'business' | 'other';
  status: 'draft' | 'submitted' | 'quoted' | 'expired';
  createdAt: string;
  expiresAt: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  createdAt: string;
}