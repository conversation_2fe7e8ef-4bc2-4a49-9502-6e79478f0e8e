import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  ClaimPublic,
  ClaimCreate,
  ClaimUpdate,
  Status,
  DocumentPublic
} from '@/types/backend';
import { claimsService } from '@/services/claimsService';

// State interface
export interface ClaimsState {
  claims: ClaimPublic[];
  currentClaim: ClaimPublic | null;
  isLoading: boolean;
  isSubmitting: boolean;
  error: string | null;
  filters: {
    status?: Status;
    policy_id?: string;
  };
}

// Initial state
const initialState: ClaimsState = {
  claims: [],
  currentClaim: null,
  isLoading: false,
  isSubmitting: false,
  error: null,
  filters: {},
};

// Async thunks
export const fetchClaims = createAsyncThunk(
  'claims/fetchClaims',
  async (filters?: { status?: Status; policy_id?: string }, { rejectWithValue }) => {
    try {
      console.log('[Claims Slice] Fetching claims with filters:', filters);
      const claims = await claimsService.getClaims(filters);
      console.log('[Claims Slice] Claims fetched successfully:', claims.length);
      return { claims, filters: filters || {} };
    } catch (error: any) {
      console.error('[Claims Slice] Error fetching claims:', error);
      return rejectWithValue(error.message || 'Failed to fetch claims');
    }
  }
);

export const fetchClaimById = createAsyncThunk(
  'claims/fetchClaimById',
  async (claimId: string, { rejectWithValue }) => {
    try {
      console.log('[Claims Slice] Fetching claim:', claimId);
      const claim = await claimsService.getClaimById(claimId);
      console.log('[Claims Slice] Claim fetched successfully:', claim.id);
      return claim;
    } catch (error: any) {
      console.error('[Claims Slice] Error fetching claim:', error);
      return rejectWithValue(error.message || 'Failed to fetch claim');
    }
  }
);

export const createClaim = createAsyncThunk(
  'claims/createClaim',
  async (claimData: ClaimCreate, { rejectWithValue }) => {
    try {
      console.log('[Claims Slice] Creating claim:', claimData);
      const claim = await claimsService.createClaim(claimData);
      console.log('[Claims Slice] Claim created successfully:', claim.id);
      return claim;
    } catch (error: any) {
      console.error('[Claims Slice] Error creating claim:', error);
      return rejectWithValue(error.message || 'Failed to create claim');
    }
  }
);

export const updateClaim = createAsyncThunk(
  'claims/updateClaim',
  async ({ claimId, claimData }: { claimId: string; claimData: ClaimUpdate }, { rejectWithValue }) => {
    try {
      console.log('[Claims Slice] Updating claim:', claimId, claimData);
      const claim = await claimsService.updateClaim(claimId, claimData);
      console.log('[Claims Slice] Claim updated successfully:', claim.id);
      return claim;
    } catch (error: any) {
      console.error('[Claims Slice] Error updating claim:', error);
      return rejectWithValue(error.message || 'Failed to update claim');
    }
  }
);

export const deleteClaim = createAsyncThunk(
  'claims/deleteClaim',
  async (claimId: string, { rejectWithValue }) => {
    try {
      console.log('[Claims Slice] Deleting claim:', claimId);
      await claimsService.deleteClaim(claimId);
      console.log('[Claims Slice] Claim deleted successfully');
      return claimId;
    } catch (error: any) {
      console.error('[Claims Slice] Error deleting claim:', error);
      return rejectWithValue(error.message || 'Failed to delete claim');
    }
  }
);

export const uploadClaimDocument = createAsyncThunk(
  'claims/uploadClaimDocument',
  async ({ claimId, file, documentType }: { claimId: string; file: File; documentType: any }, { rejectWithValue }) => {
    try {
      console.log('[Claims Slice] Uploading claim document:', claimId, documentType);
      const document = await claimsService.uploadClaimDocument(claimId, file, documentType);
      console.log('[Claims Slice] Claim document uploaded successfully:', document.id);
      return { claimId, document };
    } catch (error: any) {
      console.error('[Claims Slice] Error uploading claim document:', error);
      return rejectWithValue(error.message || 'Failed to upload document');
    }
  }
);

// Claims slice
const claimsSlice = createSlice({
  name: 'claims',
  initialState,
  reducers: {
    setCurrentClaim: (state, action: PayloadAction<ClaimPublic | null>) => {
      state.currentClaim = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<{ status?: Status; policy_id?: string }>) => {
      state.filters = action.payload;
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    addDocumentToClaim: (state, action: PayloadAction<{ claimId: string; document: DocumentPublic }>) => {
      const { claimId, document } = action.payload;
      
      // Update current claim if it matches
      if (state.currentClaim && state.currentClaim.id === claimId) {
        state.currentClaim.documents.push(document);
      }
      
      // Update claim in claims list
      const claimIndex = state.claims.findIndex(c => c.id === claimId);
      if (claimIndex !== -1) {
        state.claims[claimIndex].documents.push(document);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch claims
      .addCase(fetchClaims.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchClaims.fulfilled, (state, action) => {
        state.isLoading = false;
        state.claims = action.payload.claims;
        state.filters = action.payload.filters;
      })
      .addCase(fetchClaims.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch single claim
      .addCase(fetchClaimById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchClaimById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentClaim = action.payload;
        
        // Update claim in claims list
        const claimIndex = state.claims.findIndex(c => c.id === action.payload.id);
        if (claimIndex !== -1) {
          state.claims[claimIndex] = action.payload;
        } else {
          state.claims.push(action.payload);
        }
      })
      .addCase(fetchClaimById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create claim
      .addCase(createClaim.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(createClaim.fulfilled, (state, action) => {
        state.isSubmitting = false;
        state.claims.unshift(action.payload);
        state.currentClaim = action.payload;
      })
      .addCase(createClaim.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      })
      
      // Update claim
      .addCase(updateClaim.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(updateClaim.fulfilled, (state, action) => {
        state.isSubmitting = false;
        
        // Update current claim if it matches
        if (state.currentClaim && state.currentClaim.id === action.payload.id) {
          state.currentClaim = action.payload;
        }
        
        // Update claim in claims list
        const claimIndex = state.claims.findIndex(c => c.id === action.payload.id);
        if (claimIndex !== -1) {
          state.claims[claimIndex] = action.payload;
        }
      })
      .addCase(updateClaim.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      })
      
      // Delete claim
      .addCase(deleteClaim.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(deleteClaim.fulfilled, (state, action) => {
        state.isSubmitting = false;
        const claimId = action.payload;
        
        // Remove from claims list
        state.claims = state.claims.filter(c => c.id !== claimId);
        
        // Clear current claim if it was deleted
        if (state.currentClaim && state.currentClaim.id === claimId) {
          state.currentClaim = null;
        }
      })
      .addCase(deleteClaim.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      })
      
      // Upload claim document
      .addCase(uploadClaimDocument.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(uploadClaimDocument.fulfilled, (state, action) => {
        state.isSubmitting = false;
        const { claimId, document } = action.payload;
        
        // Update current claim if it matches
        if (state.currentClaim && state.currentClaim.id === claimId) {
          state.currentClaim.documents.push(document);
        }
        
        // Update claim in claims list
        const claimIndex = state.claims.findIndex(c => c.id === claimId);
        if (claimIndex !== -1) {
          state.claims[claimIndex].documents.push(document);
        }
      })
      .addCase(uploadClaimDocument.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      });
  },
});

export const { 
  setCurrentClaim, 
  clearError, 
  setFilters, 
  clearFilters, 
  addDocumentToClaim 
} = claimsSlice.actions;

export default claimsSlice.reducer;
