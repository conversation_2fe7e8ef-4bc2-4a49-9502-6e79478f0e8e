import React from 'react';
import { Stack } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function QuotesLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  console.log('[QuotesLayout] Rendering quotes layout');

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: colors.background },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="general-info" />
      <Stack.Screen name="[type]/index" />
      <Stack.Screen name="[type]/summary" />
      <Stack.Screen name="[type]/pdf" />

      {/* Type-specific routes */}
      <Stack.Screen name="motor/index" />
      <Stack.Screen name="motor/form" />
      <Stack.Screen name="houseowners/index" />
      <Stack.Screen name="houseowners/form" />
      <Stack.Screen name="householdContents/index" />
      <Stack.Screen name="householdContents/form" />
      <Stack.Screen name="allRisks/index" />
      <Stack.Screen name="allRisks/form" />
      <Stack.Screen name="travel/index" />
      <Stack.Screen name="health/index" />
      <Stack.Screen name="life/index" />
      <Stack.Screen name="life/form" />
      <Stack.Screen name="funeral/index" />
      <Stack.Screen name="scheme/index" />
      <Stack.Screen name="business/index" />
    </Stack>
  );
}
