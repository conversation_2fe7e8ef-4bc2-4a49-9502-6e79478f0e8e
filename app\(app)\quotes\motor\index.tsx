import { Redirect, useLocalSearchParams } from 'expo-router';

export default function MotorQuoteRedirect() {
  // Get the quoteId from the URL params if it exists
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;

  console.log('MotorQuoteRedirect - Received params:', params);
  console.log('MotorQuoteRedirect - Redirecting to motor form with quoteId:', quoteId);

  // Redirect directly to the motor form page with the quoteId
  return (
    <Redirect
      href="/(app)/quotes/motor/form"
      params={{
        quoteId: quoteId || ''
      }}
    />
  );
}
