import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface ApplicationDetailSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  initiallyExpanded?: boolean;
  badge?: {
    count: number;
    color: string;
  };
}

const ApplicationDetailSection: React.FC<ApplicationDetailSectionProps> = ({
  title,
  icon,
  children,
  initiallyExpanded = false,
  badge,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [expanded, setExpanded] = useState(initiallyExpanded);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.md,
      borderRadius: borders.radius.lg,
      overflow: 'hidden',
      backgroundColor: colors.card,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: expanded ? 1 : 0,
      borderBottomColor: colors.border,
    },
    iconContainer: {
      marginRight: spacing.sm,
    },
    title: {
      ...typography.h4,
      color: colors.text,
      flex: 1,
    },
    badge: {
      minWidth: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.sm,
      paddingHorizontal: spacing.xs,
    },
    badgeText: {
      ...typography.caption,
      fontWeight: 'bold',
      color: colors.white,
    },
    content: {
      padding: spacing.md,
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.header} onPress={toggleExpanded} activeOpacity={0.7}>
        <View style={styles.iconContainer}>{icon}</View>
        <Text style={styles.title}>{title}</Text>
        
        {badge && (
          <View style={[styles.badge, { backgroundColor: badge.color }]}>
            <Text style={styles.badgeText}>{badge.count}</Text>
          </View>
        )}
        
        {expanded ? (
          <ChevronUp size={20} color={colors.textSecondary} />
        ) : (
          <ChevronDown size={20} color={colors.textSecondary} />
        )}
      </TouchableOpacity>

      {expanded && (
        <Animated.View 
          style={styles.content}
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(200)}
        >
          {children}
        </Animated.View>
      )}
    </View>
  );
};

export default ApplicationDetailSection;
