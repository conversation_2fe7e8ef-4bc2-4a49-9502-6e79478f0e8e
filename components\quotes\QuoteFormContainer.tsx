import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, Info } from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import FormStepIndicator from './FormStepIndicator';
// Import QuoteActionButtons with explicit path
import QuoteActionButtons from '@/components/quotes/QuoteActionButtons';
import Animated, { FadeIn, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

interface Step {
  id: string;
  title: string;
}

interface QuoteFormContainerProps {
  title: string;
  subtitle?: string;
  steps?: Step[];
  currentStep?: number;
  completedSteps?: number[];
  children: React.ReactNode;
  onBack: () => void;
  onNext?: () => void;
  onSave?: () => void;
  onSubmit?: () => void;
  isLastStep?: boolean;
  isLoading?: boolean;
  nextDisabled?: boolean;
  submitDisabled?: boolean;
  submitButtonText?: string;
  infoText?: string;
}

const QuoteFormContainer: React.FC<QuoteFormContainerProps> = ({
  title,
  subtitle,
  steps,
  currentStep = 0,
  completedSteps = [],
  children,
  onBack,
  onNext,
  onSave,
  onSubmit,
  isLastStep = false,
  isLoading = false,
  nextDisabled = false,
  submitDisabled = false,
  submitButtonText,
  infoText,
}) => {
  // Add error handling for theme context
  const themeContext = useTheme();
  const isDarkMode = themeContext?.isDarkMode ?? false;

  // Create theme with fallback
  const theme = createTheme(isDarkMode);
  const { colors, spacing, typography, borders } = theme;

  // Animation value for footer position
  const footerTranslateY = useSharedValue(0);

  // Set up keyboard listeners
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        footerTranslateY.value = withTiming(-10, { duration: 150 });
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        footerTranslateY.value = withTiming(0, { duration: 100 });
      }
    );

    // Clean up listeners on unmount
    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  // Animated style for the footer
  const animatedFooterStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: footerTranslateY.value }],
    };
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTextContainer: {
      flex: 1,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    headerSubtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginTop: 2,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: spacing.lg,
      paddingBottom: spacing.xl * 4, // Extra padding at the bottom for keyboard and to ensure content is visible
    },
    infoContainer: {
      flexDirection: 'row',
      backgroundColor: `${colors.primary[500]}15`,
      padding: spacing.md,
      borderRadius: borders.radius.md,
      marginBottom: spacing.lg,
      alignItems: 'flex-start',
    },
    infoIcon: {
      marginRight: spacing.sm,
      marginTop: 2,
    },
    infoText: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      lineHeight: typography.lineHeights.relaxed * typography.sizes.sm,
    },
    footer: {
      padding: spacing.lg,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      backgroundColor: colors.card,
      position: 'relative',
      zIndex: 999,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 5,
    },
  });

  // Function to dismiss keyboard when tapping outside of input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <TouchableOpacity activeOpacity={1} onPress={dismissKeyboard} style={{flex: 1}}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>{title}</Text>
            {subtitle && <Text style={styles.headerSubtitle}>{subtitle}</Text>}
          </View>
        </View>

        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            onScrollBeginDrag={Keyboard.dismiss}
          >
            {steps && steps.length > 0 && (
              <FormStepIndicator
                steps={steps}
                currentStep={currentStep}
                completedSteps={completedSteps}
              />
            )}

            {infoText && (
              <Animated.View
                style={styles.infoContainer}
                entering={FadeIn.delay(200).duration(500)}
              >
                <Info size={20} color={colors.primary[500]} style={styles.infoIcon} />
                <Text style={styles.infoText}>{infoText}</Text>
              </Animated.View>
            )}

            {children}
          </ScrollView>
        </KeyboardAvoidingView>
      </TouchableOpacity>

      <Animated.View style={[styles.footer, animatedFooterStyle]}>
        <QuoteActionButtons
          onBack={onBack}
          onNext={onNext}
          onSave={onSave}
          onSubmit={onSubmit}
          isLastStep={isLastStep}
          isLoading={isLoading}
          nextDisabled={nextDisabled}
          submitDisabled={submitDisabled}
          submitButtonText={submitButtonText}
        />
      </Animated.View>
    </SafeAreaView>
  );
};

export default QuoteFormContainer;
