import {
  PolicyCreate,
  PolicyType,
  PolicyProvider,
  PolicyStatus,
  MotorPolicyDetails,
  HouseOwnerPolicyDetails,
  HouseholdContentsPolicyDetails,
  AllRisksSpecifiedItemsPolicyDetails,
  AllRisksUnspecifiedItemsPolicyDetails,
  Use,
  ConstructionType,
  SecurityType,
  InventoryItem
} from '@/types/backend';
import { Quote, InsuranceProductType } from '@/types/quote.types';

// Map frontend quote types to backend policy types
const mapQuoteTypeToPolicyType = (quoteType: InsuranceProductType): PolicyType => {
  switch (quoteType) {
    case 'motor':
      return PolicyType.MOTOR;
    case 'houseowners':
      return PolicyType.HOUSE_OWNER;
    case 'householdContents':
      return PolicyType.HOUSEHOLD_CONTENTS;
    case 'allRisks':
      // Default to specified items, can be changed based on quote data
      return PolicyType.ALL_RISKS_SPECIFIED;
    case 'life':
      return PolicyType.LIFE_ASSURANCE;
    default:
      throw new Error(`Unsupported quote type: ${quoteType}`);
  }
};

// Map quote data to motor policy details
const mapMotorPolicyDetails = (quote: Quote): MotorPolicyDetails => {
  const additionalInfo = quote.additionalInfo || {};

  return {
    make: additionalInfo.vehicleMake || '',
    model: additionalInfo.vehicleModel || '',
    year: additionalInfo.vehicleYear || new Date().getFullYear(),
    engine_number: additionalInfo.engineNumber || '',
    chassis_number: additionalInfo.chassisNumber || '',
    registration_number: additionalInfo.registrationNumber || '',
    current_mileage: additionalInfo.currentMileage || 0,
    use: additionalInfo.vehicleUse === 'commercial' ? Use.COMMERCIAL : Use.PRIVATE,
    driver_license: additionalInfo.hasDriverLicense || false,
    vehicle_registration_book: additionalInfo.hasRegistrationBook || false,
    vehicle_valuation_report: additionalInfo.hasValuationReport || false,
    previous_insurance_policy: additionalInfo.hasPreviousInsurance || false,
  };
};

// Map quote data to house owner policy details
const mapHouseOwnerPolicyDetails = (quote: Quote): HouseOwnerPolicyDetails => {
  const additionalInfo = quote.additionalInfo || {};

  return {
    property_value: additionalInfo.buildingValue || quote.coverAmount || 0,
    year_of_construction: additionalInfo.yearOfConstruction || new Date().getFullYear(),
    construction_type: additionalInfo.constructionType === 'thatched'
      ? ConstructionType.THATCHED
      : ConstructionType.BRICK,
    is_occupied: additionalInfo.isOccupied !== false, // Default to true
    title_deed: additionalInfo.hasTitleDeed || false,
    property_valuation_report: additionalInfo.hasValuationReport || false,
  };
};

// Map quote data to household contents policy details
const mapHouseholdContentsPolicyDetails = (quote: Quote): HouseholdContentsPolicyDetails => {
  const additionalInfo = quote.additionalInfo || {};

  // Convert inventory items from quote format to backend format
  const inventoryList: InventoryItem[] = (additionalInfo.inventoryItems || []).map((item: any) => ({
    item: item.name || item.description || 'Item',
    value: item.value || 0,
  }));

  return {
    type_of_security: additionalInfo.securityType === 'premium'
      ? SecurityType.PREMIUM
      : additionalInfo.securityType === 'advanced'
      ? SecurityType.ADVANCED
      : SecurityType.BASIC,
    inventory_list: inventoryList,
    power_surge_extension: additionalInfo.powerSurgeExtension || false,
    accidental_breakage_extension: additionalInfo.accidentalBreakageExtension || false,
    inventory_form: additionalInfo.hasInventoryForm || false,
  };
};

// Map quote data to all risks specified items policy details
const mapAllRisksSpecifiedPolicyDetails = (quote: Quote): AllRisksSpecifiedItemsPolicyDetails => {
  const additionalInfo = quote.additionalInfo || {};

  // Convert specified items from quote format to backend format
  const items: InventoryItem[] = (additionalInfo.specifiedItems || []).map((item: any) => ({
    item: item.name || item.description || 'Item',
    value: item.value || 0,
  }));

  return {
    items,
  };
};

// Map quote data to all risks unspecified items policy details
const mapAllRisksUnspecifiedPolicyDetails = (quote: Quote): AllRisksUnspecifiedItemsPolicyDetails => {
  const additionalInfo = quote.additionalInfo || {};

  // Convert unspecified items from quote format to backend format
  const items: InventoryItem[] = (additionalInfo.unspecifiedItems || []).map((item: any) => ({
    item: item.name || item.description || 'General Item',
    value: item.value || 0,
  }));

  return {
    total_sum_insured: additionalInfo.unspecifiedItemsValue || quote.coverAmount || 0,
    items,
  };
};

// Main conversion function
export const convertQuoteToPolicy = (quote: Quote, userId: string): PolicyCreate => {
  console.log('[Policy Conversion] Converting quote to policy:', quote);
  console.log('[Policy Conversion] User ID:', userId);

  const policyType = mapQuoteTypeToPolicyType(quote.type);
  console.log('[Policy Conversion] Mapped policy type:', policyType);

  // Determine policy provider based on quote type or other criteria
  const policyProvider = quote.type === 'life'
    ? PolicyProvider.BOTSWANA_LIFE
    : PolicyProvider.HOLLAND;
  console.log('[Policy Conversion] Policy provider:', policyProvider);

  // Calculate policy dates (start immediately, end in 1 year)
  const startDate = new Date();
  const endDate = new Date();
  endDate.setFullYear(startDate.getFullYear() + 1);
  console.log('[Policy Conversion] Policy dates - Start:', startDate, 'End:', endDate);

  // Map policy details based on type
  console.log('[Policy Conversion] Mapping policy details for type:', quote.type);
  let policyDetails;

  switch (quote.type) {
    case 'motor':
      console.log('[Policy Conversion] Mapping motor policy details');
      policyDetails = mapMotorPolicyDetails(quote);
      break;
    case 'houseowners':
      console.log('[Policy Conversion] Mapping house owner policy details');
      policyDetails = mapHouseOwnerPolicyDetails(quote);
      break;
    case 'householdContents':
      console.log('[Policy Conversion] Mapping household contents policy details');
      policyDetails = mapHouseholdContentsPolicyDetails(quote);
      break;
    case 'allRisks':
      console.log('[Policy Conversion] Mapping all risks policy details');
      // Determine if it's specified or unspecified based on quote data
      const additionalInfo = quote.additionalInfo || {};
      if (additionalInfo.specifiedItems && additionalInfo.specifiedItems.length > 0) {
        console.log('[Policy Conversion] Using specified items for all risks');
        policyDetails = mapAllRisksSpecifiedPolicyDetails(quote);
      } else {
        console.log('[Policy Conversion] Using unspecified items for all risks');
        policyDetails = mapAllRisksUnspecifiedPolicyDetails(quote);
        // Update policy type to unspecified
        const updatedPolicyType = PolicyType.ALL_RISKS_UNSPECIFIED;
        const policyCreate = {
          policy_type: updatedPolicyType,
          policy_provider: policyProvider,
          status: PolicyStatus.PENDING,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0],
          policy_details: policyDetails,
          user_id: userId,
        };
        console.log('[Policy Conversion] Created unspecified all risks policy:', policyCreate);
        return policyCreate;
      }
      break;
    default:
      console.error('[Policy Conversion] Unsupported quote type:', quote.type);
      throw new Error(`Policy details mapping not implemented for quote type: ${quote.type}`);
  }

  console.log('[Policy Conversion] Mapped policy details:', policyDetails);

  const policyCreate = {
    policy_type: policyType,
    policy_provider: policyProvider,
    status: PolicyStatus.PENDING,
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
    policy_details: policyDetails,
    user_id: userId,
  };

  console.log('[Policy Conversion] Final policy create object:', policyCreate);
  return policyCreate;
};

// Helper function to validate quote data before conversion
export const validateQuoteForPolicyCreation = (quote: Quote): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check if quote has required basic information
  if (!quote.clientInfo?.firstName) {
    errors.push('Client first name is required');
  }

  if (!quote.clientInfo?.lastName) {
    errors.push('Client last name is required');
  }

  if (!quote.premium || quote.premium <= 0) {
    errors.push('Valid premium amount is required');
  }

  if (!quote.coverAmount || quote.coverAmount <= 0) {
    errors.push('Valid cover amount is required');
  }

  // Type-specific validations
  const additionalInfo = quote.additionalInfo || {};

  switch (quote.type) {
    case 'motor':
      if (!additionalInfo.vehicleMake) errors.push('Vehicle make is required');
      if (!additionalInfo.vehicleModel) errors.push('Vehicle model is required');
      if (!additionalInfo.vehicleYear) errors.push('Vehicle year is required');
      break;
    case 'houseowners':
      if (!additionalInfo.buildingValue) errors.push('Building value is required');
      break;
    case 'householdContents':
      if (!additionalInfo.inventoryItems || additionalInfo.inventoryItems.length === 0) {
        errors.push('At least one inventory item is required');
      }
      break;
    case 'allRisks':
      if (!additionalInfo.specifiedItems && !additionalInfo.unspecifiedItemsValue) {
        errors.push('Either specified items or unspecified items value is required');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Helper function to get required documents for policy type
export const getRequiredDocumentsForPolicyType = (policyType: PolicyType): string[] => {
  switch (policyType) {
    case PolicyType.MOTOR:
      return [
        'Driver License',
        'Vehicle Registration Book',
        'Vehicle Valuation Report',
        'Previous Insurance Policy (if applicable)'
      ];
    case PolicyType.HOUSE_OWNER:
      return [
        'Title Deed',
        'Property Valuation Report'
      ];
    case PolicyType.HOUSEHOLD_CONTENTS:
      return [
        'Inventory Form',
        'Property Photos'
      ];
    case PolicyType.ALL_RISKS_SPECIFIED:
    case PolicyType.ALL_RISKS_UNSPECIFIED:
      return [
        'Item Valuation Reports',
        'Purchase Receipts'
      ];
    case PolicyType.LIFE_ASSURANCE:
      return [
        'Medical Report',
        'Identity Document'
      ];
    default:
      return [];
  }
};
