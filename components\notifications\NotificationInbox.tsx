import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import {
  CheckCircle,
  AlertCircle,
  Info,
  Bell,
  FileText,
  CreditCard,
  Shield,
  Trash2,
  MarkAsRead,
} from 'lucide-react-native';

import { useTheme } from '@/context/ThemeContext';
import { RootState, AppDispatch } from '@/store/store';
import {
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  clearAllNotifications,
  Notification,
  NotificationType,
} from '@/store/notificationSlice';

interface NotificationInboxProps {
  onNotificationPress?: (notification: Notification) => void;
}

const NotificationInbox: React.FC<NotificationInboxProps> = ({
  onNotificationPress,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { colors } = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  const { notifications, unreadCount, isLoading, error } = useSelector(
    (state: RootState) => state.notification
  );

  useEffect(() => {
    dispatch(fetchNotifications());
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchNotifications());
    setRefreshing(false);
  };

  const handleNotificationPress = (notification: Notification) => {
    // Mark as read if unread
    if (!notification.read) {
      dispatch(markAsRead(notification.id));
    }

    // Handle custom press action
    if (onNotificationPress) {
      onNotificationPress(notification);
      return;
    }

    // Navigate to action route if available
    if (notification.actionRoute) {
      router.push(notification.actionRoute);
    }
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead());
  };

  const handleDeleteNotification = (id: string) => {
    dispatch(deleteNotification(id));
  };

  const handleClearAll = () => {
    dispatch(clearAllNotifications());
  };

  const getNotificationIcon = (type: NotificationType) => {
    const iconProps = { size: 20, color: colors.primary[500] };
    
    switch (type) {
      case 'success':
      case 'document_verified':
      case 'payment_verified':
      case 'policy_dispatched':
        return <CheckCircle {...iconProps} color={colors.success[500]} />;
      
      case 'error':
        return <AlertCircle {...iconProps} color={colors.error[500]} />;
      
      case 'warning':
      case 'document_reminder':
      case 'renewal_reminder':
        return <AlertCircle {...iconProps} color={colors.warning[500]} />;
      
      case 'info':
      case 'quotation_sent':
      case 'quote_generated':
      case 'admin_notes':
        return <Info {...iconProps} color={colors.info[500]} />;
      
      case 'document':
        return <FileText {...iconProps} />;
      
      case 'payment':
      case 'payment_uploaded':
        return <CreditCard {...iconProps} />;
      
      case 'policy':
      case 'policy_issued':
      case 'policy_bound':
        return <Shield {...iconProps} />;
      
      default:
        return <Bell {...iconProps} />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderNotificationItem = ({ item, index }: { item: Notification; index: number }) => (
    <Animated.View
      entering={FadeInDown.delay(index * 50).springify()}
      style={[
        styles.notificationItem,
        { backgroundColor: colors.surface },
        !item.read && { backgroundColor: colors.primary[50] },
      ]}
    >
      <TouchableOpacity
        style={styles.notificationContent}
        onPress={() => handleNotificationPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.notificationHeader}>
          <View style={styles.iconContainer}>
            {getNotificationIcon(item.type)}
          </View>
          <View style={styles.notificationInfo}>
            <Text
              style={[
                styles.notificationTitle,
                { color: colors.text },
                !item.read && { fontWeight: '600' },
              ]}
              numberOfLines={1}
            >
              {item.title}
            </Text>
            <Text
              style={[
                styles.notificationMessage,
                { color: colors.textSecondary },
              ]}
              numberOfLines={2}
            >
              {item.message}
            </Text>
            <Text style={[styles.notificationDate, { color: colors.textSecondary }]}>
              {formatDate(item.createdAt)}
            </Text>
          </View>
          {!item.read && (
            <View style={[styles.unreadIndicator, { backgroundColor: colors.primary[500] }]} />
          )}
        </View>

        {item.actionLabel && (
          <View style={styles.actionContainer}>
            <Text style={[styles.actionLabel, { color: colors.primary[500] }]}>
              {item.actionLabel}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteNotification(item.id)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Trash2 size={16} color={colors.textSecondary} />
      </TouchableOpacity>
    </Animated.View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={[styles.headerTitle, { color: colors.text }]}>
        Notifications {unreadCount > 0 && `(${unreadCount})`}
      </Text>
      <View style={styles.headerActions}>
        {unreadCount > 0 && (
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.primary[100] }]}
            onPress={handleMarkAllAsRead}
          >
            <MarkAsRead size={16} color={colors.primary[500]} />
            <Text style={[styles.headerButtonText, { color: colors.primary[500] }]}>
              Mark All Read
            </Text>
          </TouchableOpacity>
        )}
        {notifications.length > 0 && (
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: colors.error[100] }]}
            onPress={handleClearAll}
          >
            <Trash2 size={16} color={colors.error[500]} />
            <Text style={[styles.headerButtonText, { color: colors.error[500] }]}>
              Clear All
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Bell size={48} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Notifications
      </Text>
      <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
        You're all caught up! New notifications will appear here.
      </Text>
    </View>
  );

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {renderHeader()}
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={colors.error[500]} />
          <Text style={[styles.errorText, { color: colors.error[500] }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary[500] }]}
            onPress={() => dispatch(fetchNotifications())}
          >
            <Text style={[styles.retryButtonText, { color: colors.white }]}>
              Retry
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={notifications.length === 0 ? styles.emptyContainer : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  headerButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationInfo: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 4,
  },
  notificationDate: {
    fontSize: 11,
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  actionContainer: {
    marginTop: 8,
    paddingLeft: 52,
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    gap: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    gap: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default NotificationInbox;
