import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import usePaymentStore from '@/store/paymentStore';
import DocumentUploader from '@/components/documents/DocumentUploader';
import { Document } from '@/components/documents/types';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';

export default function NewPaymentScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const params = useLocalSearchParams();

  // Get payment store methods
  const { addPaymentProof } = usePaymentStore();

  // State - simplified to only handle payment details
  const [paymentDetails] = useState({
    amount: Number(params.amount) || 1000,
    currency: params.currency as string || 'BWP',
    description: params.description as string || 'Insurance Payment',
    applicationId: params.applicationId as string,
    reference: `PAY-${Math.floor(100000 + Math.random() * 900000)}`,
  });

  // Handle document uploaded - simplified to only upload proof
  const handleDocumentUploaded = async (document: Document) => {
    try {
      // Add payment proof
      await addPaymentProof({
        applicationId: paymentDetails.applicationId || '',
        reference: paymentDetails.reference,
        amount: paymentDetails.amount,
        currency: paymentDetails.currency,
        date: new Date().toISOString().split('T')[0],
        documentId: document.id
      });

      // Show success toast
      showToast(
        'success',
        'Proof Uploaded',
        'Your proof of payment has been uploaded successfully',
        { visibilityTime: 3000 }
      );

      // Navigate to dashboard
      router.push('/dashboard');
    } catch (error) {
      console.error('Error uploading proof of payment:', error);
      showToast(
        'error',
        'Upload Error',
        'Failed to upload proof of payment. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Simplified content - only show document upload
  const renderContent = () => {
    return (
      <ScrollView style={styles.content}>
        <View style={styles.container}>
          <View style={styles.referenceContainer}>
            <Text style={styles.referenceLabel}>Payment Reference</Text>
            <Text style={styles.referenceValue}>{paymentDetails.reference}</Text>
          </View>

          <View style={styles.paymentDetailsContainer}>
            <Text style={styles.paymentDetailsTitle}>Payment Details</Text>
            <View style={styles.paymentDetailsRow}>
              <Text style={styles.paymentDetailsLabel}>Amount:</Text>
              <Text style={styles.paymentDetailsValue}>
                {paymentDetails.currency} {paymentDetails.amount.toFixed(2)}
              </Text>
            </View>
            <View style={styles.paymentDetailsRow}>
              <Text style={styles.paymentDetailsLabel}>Description:</Text>
              <Text style={styles.paymentDetailsValue}>{paymentDetails.description}</Text>
            </View>
            <View style={styles.paymentDetailsRow}>
              <Text style={styles.paymentDetailsLabel}>Date:</Text>
              <Text style={styles.paymentDetailsValue}>{new Date().toISOString().split('T')[0]}</Text>
            </View>
          </View>

          <Text style={styles.uploadTitle}>Upload Proof of Payment</Text>
          <Text style={styles.uploadDescription}>
            Please upload a screenshot or PDF of your payment confirmation.
          </Text>

          <DocumentUploader
            onDocumentUploaded={handleDocumentUploaded}
            preselectedDocumentType="Payment Proof - Premium"
          />
        </View>
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.screenContainer} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Upload Proof of Payment</Text>
      </View>

      {renderContent()}

      <BottomNavBar currentRoute="payments" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  container: {
    padding: 16,
  },
  referenceContainer: {
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  referenceLabel: {
    fontSize: 12,
    color: '#0066cc',
    marginBottom: 4,
  },
  referenceValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0066cc',
  },
  paymentDetailsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  paymentDetailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  paymentDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentDetailsLabel: {
    fontSize: 14,
    color: '#666666',
  },
  paymentDetailsValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  uploadTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  uploadDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
  },
});
