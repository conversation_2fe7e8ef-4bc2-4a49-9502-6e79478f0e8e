import apiService from './api';
import { Document } from '@/store/documentStore';
import { showToast } from '@/utils/toast';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import * as Sharing from 'expo-sharing';
import * as IntentLauncher from 'expo-intent-launcher';
import * as WebBrowser from 'expo-web-browser';
import { getMimeType } from '@/utils/fileUtils';

// Document service for handling document-related API calls
export const documentService = {
  // Get all documents for the current user
  getAllDocuments: async (): Promise<Document[]> => {
    try {
      const response = await apiService.documents.getAllDocuments();
      
      // Map API response to Document type
      const documents: Document[] = response.map((item: any) => ({
        id: item.id,
        name: item.name,
        type: item.document_type,
        status: mapApiStatusToDocStatus(item.status),
        uploadDate: item.uploaded_at.split('T')[0],
        verificationDate: item.verified_at ? item.verified_at.split('T')[0] : undefined,
        rejectionReason: item.rejection_reason,
        fileSize: item.file_size,
        fileType: item.file_type,
        policyId: item.policy_id,
        url: item.url,
        thumbnailUrl: item.thumbnail_url,
        isRequired: item.is_required,
        category: item.category || 'general',
        tags: item.tags || [],
      }));
      
      return documents;
    } catch (error) {
      console.error('Error fetching documents:', error);
      showToast(
        'error',
        'Error',
        'Failed to fetch documents. Please try again.',
        { visibilityTime: 4000 }
      );
      return [];
    }
  },
  
  // Upload a document
  uploadDocument: async (file: any, documentType: string, policyId?: string): Promise<Document | null> => {
    try {
      const response = await apiService.documents.uploadDocument(file, policyId);
      
      // Map API response to Document type
      const document: Document = {
        id: response.id,
        name: response.name,
        type: response.document_type,
        status: mapApiStatusToDocStatus(response.status),
        uploadDate: response.uploaded_at.split('T')[0],
        verificationDate: response.verified_at ? response.verified_at.split('T')[0] : undefined,
        rejectionReason: response.rejection_reason,
        fileSize: response.file_size,
        fileType: response.file_type,
        policyId: response.policy_id,
        url: response.url,
        thumbnailUrl: response.thumbnail_url,
        isRequired: response.is_required,
        category: response.category || 'general',
        tags: response.tags || [],
      };
      
      showToast(
        'success',
        'Document Uploaded',
        'Your document has been uploaded successfully',
        { visibilityTime: 3000 }
      );
      
      return document;
    } catch (error) {
      console.error('Error uploading document:', error);
      showToast(
        'error',
        'Upload Failed',
        'Failed to upload document. Please try again.',
        { visibilityTime: 4000 }
      );
      return null;
    }
  },
  
  // View a document
  viewDocument: async (documentId: string): Promise<boolean> => {
    try {
      showToast(
        'info',
        'Opening Document',
        'Preparing document for viewing...',
        { visibilityTime: 2000 }
      );
      
      // Get document URL
      const document = await apiService.documents.viewDocument(documentId);
      
      // If we have a blob, we need to create a local file
      if (document instanceof Blob) {
        const fileReader = new FileReader();
        fileReader.readAsDataURL(document);
        
        return new Promise((resolve, reject) => {
          fileReader.onloadend = async () => {
            try {
              const base64Data = fileReader.result as string;
              const fileUri = `${FileSystem.cacheDirectory}document_${documentId}.pdf`;
              
              await FileSystem.writeAsStringAsync(fileUri, base64Data.split(',')[1], {
                encoding: FileSystem.EncodingType.Base64,
              });
              
              // Open the file
              await openFile(fileUri);
              resolve(true);
            } catch (error) {
              console.error('Error creating local file:', error);
              reject(error);
            }
          };
          
          fileReader.onerror = () => {
            reject(new Error('Failed to read file data'));
          };
        });
      } else if (typeof document === 'string') {
        // If we have a URL, open it directly
        await WebBrowser.openBrowserAsync(document);
        return true;
      } else {
        throw new Error('Invalid document data received');
      }
    } catch (error) {
      console.error('Error viewing document:', error);
      showToast(
        'error',
        'View Failed',
        'Failed to open document. Please try again.',
        { visibilityTime: 4000 }
      );
      return false;
    }
  },
  
  // Download a document
  downloadDocument: async (documentId: string): Promise<string | null> => {
    try {
      showToast(
        'info',
        'Downloading',
        'Downloading document...',
        { visibilityTime: 2000 }
      );
      
      // Get document data
      const document = await apiService.documents.downloadDocument(documentId);
      
      // If we have a blob, we need to create a local file
      if (document instanceof Blob) {
        const fileReader = new FileReader();
        fileReader.readAsDataURL(document);
        
        return new Promise((resolve, reject) => {
          fileReader.onloadend = async () => {
            try {
              const base64Data = fileReader.result as string;
              const fileUri = `${FileSystem.documentDirectory}document_${documentId}.pdf`;
              
              await FileSystem.writeAsStringAsync(fileUri, base64Data.split(',')[1], {
                encoding: FileSystem.EncodingType.Base64,
              });
              
              showToast(
                'success',
                'Download Complete',
                'Document has been downloaded successfully',
                { visibilityTime: 3000 }
              );
              
              resolve(fileUri);
            } catch (error) {
              console.error('Error creating local file:', error);
              reject(error);
            }
          };
          
          fileReader.onerror = () => {
            reject(new Error('Failed to read file data'));
          };
        });
      } else if (typeof document === 'string') {
        // If we have a URL, download it
        const fileUri = `${FileSystem.documentDirectory}document_${documentId}.pdf`;
        
        const downloadResult = await FileSystem.downloadAsync(
          document,
          fileUri
        );
        
        if (downloadResult.status === 200) {
          showToast(
            'success',
            'Download Complete',
            'Document has been downloaded successfully',
            { visibilityTime: 3000 }
          );
          
          return fileUri;
        } else {
          throw new Error(`Download failed with status ${downloadResult.status}`);
        }
      } else {
        throw new Error('Invalid document data received');
      }
    } catch (error) {
      console.error('Error downloading document:', error);
      showToast(
        'error',
        'Download Failed',
        'Failed to download document. Please try again.',
        { visibilityTime: 4000 }
      );
      return null;
    }
  },
  
  // Delete a document
  deleteDocument: async (documentId: string): Promise<boolean> => {
    try {
      await apiService.documents.deleteDocument(documentId);
      
      showToast(
        'success',
        'Document Deleted',
        'Your document has been deleted successfully',
        { visibilityTime: 3000 }
      );
      
      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      showToast(
        'error',
        'Delete Failed',
        'Failed to delete document. Please try again.',
        { visibilityTime: 4000 }
      );
      return false;
    }
  },
};

// Helper function to map API status to document status
function mapApiStatusToDocStatus(apiStatus: string): 'pending' | 'verified' | 'rejected' {
  const statusMap: Record<string, 'pending' | 'verified' | 'rejected'> = {
    'pending': 'pending',
    'verified': 'verified',
    'rejected': 'rejected',
  };
  
  return statusMap[apiStatus] || 'pending';
}

// Helper function to open a file
async function openFile(fileUri: string): Promise<void> {
  try {
    const mimeType = getMimeType(fileUri);
    
    if (Platform.OS === 'android') {
      const contentUri = await FileSystem.getContentUriAsync(fileUri);
      await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
        data: contentUri,
        flags: 1,
        type: mimeType,
      });
    } else {
      await Sharing.shareAsync(fileUri);
    }
  } catch (error) {
    console.error('Error opening file:', error);
    throw error;
  }
}

export default documentService;
