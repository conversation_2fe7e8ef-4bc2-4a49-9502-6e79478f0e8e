import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import {
  Download,
  ExternalLink,
  FileText,
  Building,
  User,
  Shield,
} from 'lucide-react-native';

import { useTheme } from '@/context/ThemeContext';
import { showToast } from '@/utils/toast';

interface PolicyViewerProps {
  policyNumber: string;
  policyType: string;
  userType: 'individual' | 'business';
  onDownload?: () => void;
}

const PolicyViewer: React.FC<PolicyViewerProps> = ({
  policyNumber,
  policyType,
  userType,
  onDownload,
}) => {
  const { colors } = useTheme();
  const [isDownloading, setIsDownloading] = useState(false);

  // Policy document URLs based on user type
  const policyUrls = {
    business: 'https://hollardint.my.sharepoint.com/:f:/g/personal/helpdesk_hollard_co_bw/Ek1jOwFt6lJEknIBIxmZdGwBkOKns1lt9XoGYIr0AcBpEA?e=82D7yS',
    individual: 'https://hollardint.my.sharepoint.com/:f:/g/personal/helpdesk_hollard_co_bw/EjljkWIOxoRHiAaAAmY6KEUB08jPyi2E2E7dpBNrKMttRQ?e=Y52VyJ',
  };

  const handleDownloadPolicy = async () => {
    try {
      setIsDownloading(true);
      
      // Simulate download delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showToast(
        'success',
        'Policy Downloaded',
        `Your ${policyType} policy has been downloaded successfully.`,
        { visibilityTime: 3000 }
      );

      if (onDownload) {
        onDownload();
      }
    } catch (error) {
      console.error('Error downloading policy:', error);
      showToast(
        'error',
        'Download Failed',
        'Failed to download policy. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const handleViewOnline = async () => {
    try {
      const url = policyUrls[userType];
      const supported = await Linking.canOpenURL(url);
      
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert(
          'Cannot Open Link',
          'Unable to open the policy document link. Please try again later.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error opening policy URL:', error);
      showToast(
        'error',
        'Error',
        'Failed to open policy document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  const handleSharePolicy = () => {
    Alert.alert(
      'Share Policy',
      'Choose how you would like to share your policy document:',
      [
        {
          text: 'Email',
          onPress: () => {
            showToast(
              'info',
              'Email Sharing',
              'Policy will be sent to your registered email address.',
              { visibilityTime: 3000 }
            );
          },
        },
        {
          text: 'Copy Link',
          onPress: () => {
            showToast(
              'success',
              'Link Copied',
              'Policy document link has been copied to clipboard.',
              { visibilityTime: 3000 }
            );
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  return (
    <Animated.View
      entering={FadeInDown.springify()}
      style={[styles.container, { backgroundColor: colors.surface }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: colors.primary[100] }]}>
          <Shield size={24} color={colors.primary[500]} />
        </View>
        <View style={styles.headerInfo}>
          <Text style={[styles.title, { color: colors.text }]}>
            Policy Document
          </Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {policyNumber}
          </Text>
        </View>
        <View style={styles.typeIndicator}>
          {userType === 'business' ? (
            <Building size={20} color={colors.primary[500]} />
          ) : (
            <User size={20} color={colors.primary[500]} />
          )}
        </View>
      </View>

      {/* Policy Info */}
      <View style={styles.policyInfo}>
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
            Policy Type
          </Text>
          <Text style={[styles.infoValue, { color: colors.text }]}>
            {policyType}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
            Account Type
          </Text>
          <Text style={[styles.infoValue, { color: colors.text }]}>
            {userType === 'business' ? 'Business' : 'Individual'}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
            Document Status
          </Text>
          <Text style={[styles.infoValue, { color: colors.success[500] }]}>
            Available
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.primaryButton,
            { backgroundColor: colors.primary[500] },
            isDownloading && { opacity: 0.6 },
          ]}
          onPress={handleDownloadPolicy}
          disabled={isDownloading}
        >
          <Download size={20} color={colors.white} />
          <Text style={[styles.actionButtonText, { color: colors.white }]}>
            {isDownloading ? 'Downloading...' : 'Download PDF'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.secondaryButton,
            { borderColor: colors.primary[500] },
          ]}
          onPress={handleViewOnline}
        >
          <ExternalLink size={20} color={colors.primary[500]} />
          <Text style={[styles.actionButtonText, { color: colors.primary[500] }]}>
            View Online
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.secondaryButton,
            { borderColor: colors.textSecondary },
          ]}
          onPress={handleSharePolicy}
        >
          <FileText size={20} color={colors.textSecondary} />
          <Text style={[styles.actionButtonText, { color: colors.textSecondary }]}>
            Share
          </Text>
        </TouchableOpacity>
      </View>

      {/* Policy Wording Notice */}
      <View style={[styles.notice, { backgroundColor: colors.warning[50] }]}>
        <Text style={[styles.noticeText, { color: colors.warning[700] }]}>
          📋 This document contains your complete policy terms and conditions. 
          Please review carefully and keep for your records.
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    margin: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  typeIndicator: {
    padding: 8,
  },
  policyInfo: {
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  actions: {
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    // backgroundColor set dynamically
  },
  secondaryButton: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  notice: {
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#F59E0B',
  },
  noticeText: {
    fontSize: 12,
    lineHeight: 16,
  },
});

export default PolicyViewer;
