import Toast from 'react-native-toast-message';

type ToastType = 'success' | 'error' | 'info';

interface ToastOptions {
  position?: 'top' | 'bottom';
  visibilityTime?: number;
  autoHide?: boolean;
  topOffset?: number;
  bottomOffset?: number;
}

/**
 * Show a toast notification
 * @param type Type of toast: 'success', 'error', or 'info'
 * @param title Title of the toast
 * @param message Message to display in the toast
 * @param options Additional options for the toast
 */
export const showToast = (
  type: ToastType,
  title: string,
  message: string,
  options?: ToastOptions
) => {
  try {
    Toast.show({
      type,
      text1: title,
      text2: message,
      position: options?.position || 'top',
      visibilityTime: options?.visibilityTime || 3000,
      autoHide: options?.autoHide !== false,
      topOffset: options?.topOffset || 50,
      bottomOffset: options?.bottomOffset || 40,
    });
  } catch (error) {
    console.error('Error showing toast:', error);
  }
};
