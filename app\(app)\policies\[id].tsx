import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Share } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import usePolicyStore, { Policy, PolicyDocument, PolicyPayment } from '@/store/policyStore';
import PolicyDetailView from '@/components/policies/PolicyDetailView';
import PolicyRenewalSection from '@/components/policies/PolicyRenewalSection';
import RenewalManager from '@/components/policies/RenewalManager';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';
import { useDocumentUpload } from '@/context/DocumentUploadContext';

export default function PolicyDetailScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getPolicyById, fetchPolicies, isLoading, initiateRenewal } = usePolicyStore();
  const { getDocumentById } = useDocumentUpload();
  const [policy, setPolicy] = useState<Policy | null>(null);

  // Load policy details
  useEffect(() => {
    const loadPolicy = async () => {
      await fetchPolicies();
      if (id) {
        const policyData = getPolicyById(id);
        if (policyData) {
          setPolicy(policyData);
        }
      }
    };

    loadPolicy();
  }, [id, fetchPolicies, getPolicyById]);



  // Handle document download
  const handleDownloadDocument = async (document: PolicyDocument) => {
    try {
      // In a real app, this would download from a server
      // For this demo, we'll simulate a download

      // Get the document from the document store if it exists
      const docData = getDocumentById(document.documentId);

      if (!docData) {
        throw new Error('Document not found');
      }

      // Show loading toast
      showToast(
        'info',
        'Downloading...',
        'Your document is being downloaded',
        { visibilityTime: 2000 }
      );

      // Simulate download delay
      setTimeout(() => {
        // Show success toast
        showToast(
          'success',
          'Download Complete',
          `${document.name} has been downloaded successfully`,
          { visibilityTime: 3000 }
        );
      }, 2000);
    } catch (error) {
      console.error('Error downloading document:', error);
      showToast(
        'error',
        'Download Failed',
        'There was an error downloading your document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle document sharing
  const handleShareDocument = async (document: PolicyDocument) => {
    try {
      // In a real app, this would share a downloaded file
      // For this demo, we'll simulate sharing

      await Share.share({
        title: document.name,
        message: `Check out my ${document.name} from Inerca Insurance`,
      });
    } catch (error) {
      console.error('Error sharing document:', error);
      showToast(
        'error',
        'Sharing Failed',
        'There was an error sharing your document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle payment
  const handleMakePayment = (_payment: PolicyPayment) => {
    // Show toast notification instead of navigating to a non-existent page
    showToast(
      'info',
      'Payment',
      'Payment functionality is not yet implemented',
      { visibilityTime: 3000 }
    );

    // In a real app, we would navigate to the payment screen
    // router.push({
    //   pathname: '/payment',
    //   params: { policyId: policy?.id, paymentId: _payment.id }
    // });
  };

  // Handle policy renewal
  const handleRenewPolicy = async () => {
    if (!policy) return;

    try {
      // Navigate to the renewal screen
      router.push(`/policies/renew/${policy.id}`);
      return true;
    } catch (error) {
      console.error('Error navigating to renewal screen:', error);
      showToast(
        'error',
        'Navigation Failed',
        'There was an error navigating to the renewal screen. Please try again.',
        { visibilityTime: 3000 }
      );
      throw error;
    }
  };

  // Handle file claim
  const handleFileClaimPress = () => {
    // Show toast notification instead of navigating to a non-existent page
    showToast(
      'info',
      'File Claim',
      'Claims functionality is not yet implemented',
      { visibilityTime: 3000 }
    );

    // In a real app, we would navigate to the claims screen
    // router.push({
    //   pathname: '/claims',
    //   params: { policyId: policy?.id }
    // });
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Policy Details</Text>
        </View>
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      </SafeAreaView>
    );
  }

  if (!policy) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Policy Details</Text>
        </View>
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.notFoundText}>Policy not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Policy Details</Text>
      </View>

      <View style={styles.content}>
        {policy && <RenewalManager />}
        <PolicyDetailView
          policy={policy}
          onDownloadDocument={handleDownloadDocument}
          onShareDocument={handleShareDocument}
          onMakePayment={handleMakePayment}
          onRenewPolicy={handleRenewPolicy}
          onFileClaimPress={handleFileClaimPress}
        />
      </View>

      <BottomNavBar currentRoute="policies" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  notFoundText: {
    fontSize: 16,
    color: 'gray',
  },
});
