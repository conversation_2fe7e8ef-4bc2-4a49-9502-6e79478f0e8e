import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { ArrowLeft, FileText } from 'lucide-react-native';
import { router } from 'expo-router';
import * as DocumentPicker from 'expo-document-picker';
import { openFileInExternalViewer } from '@/utils/fileUtils';
import Toast from 'react-native-toast-message';

const TestFileViewerScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [mimeType, setMimeType] = useState<string | null>(null);

  // Pick a file to test viewing
  const pickFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        copyToCacheDirectory: true,
      });

      if (result.canceled === false) {
        console.log('[TestFileViewer] File selected:', result.assets[0]);
        setSelectedFile(result.assets[0].uri);
        setFileName(result.assets[0].name);
        setMimeType(result.assets[0].mimeType || null);
      }
    } catch (error) {
      console.error('[TestFileViewer] Error picking file:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to pick file. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
      });
    }
  };

  // Open the selected file
  const openFile = async () => {
    if (!selectedFile) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please select a file first',
        visibilityTime: 4000,
        topOffset: 60,
      });
      return;
    }

    try {
      await openFileInExternalViewer(selectedFile, mimeType || undefined);
    } catch (error) {
      console.error('[TestFileViewer] Error opening file:', error);
      // Toast is handled by the utility function
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    content: {
      padding: spacing.lg,
      flex: 1,
    },
    button: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    buttonText: {
      color: colors.white,
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
    },
    fileInfo: {
      marginTop: spacing.lg,
      padding: spacing.md,
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      borderLeftWidth: 4,
      borderLeftColor: colors.primary[500],
    },
    fileInfoText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    noFileText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.xl,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Test File Viewer</Text>
      </View>

      <ScrollView style={styles.content}>
        <TouchableOpacity style={styles.button} onPress={pickFile}>
          <Text style={styles.buttonText}>Select File</Text>
        </TouchableOpacity>

        {selectedFile ? (
          <>
            <View style={styles.fileInfo}>
              <Text style={styles.fileInfoText}>
                <Text style={{ fontWeight: 'bold' }}>File Name:</Text> {fileName}
              </Text>
              <Text style={styles.fileInfoText}>
                <Text style={{ fontWeight: 'bold' }}>MIME Type:</Text> {mimeType || 'Unknown'}
              </Text>
              <Text style={styles.fileInfoText}>
                <Text style={{ fontWeight: 'bold' }}>URI:</Text> {selectedFile}
              </Text>
            </View>

            <TouchableOpacity style={[styles.button, { marginTop: spacing.lg }]} onPress={openFile}>
              <Text style={styles.buttonText}>Open File in External Viewer</Text>
            </TouchableOpacity>
          </>
        ) : (
          <Text style={styles.noFileText}>
            No file selected. Please select a file to test the external viewer.
          </Text>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default TestFileViewerScreen;
