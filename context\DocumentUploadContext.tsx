import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { Document } from '@/components/documents/types';
import Toast from 'react-native-toast-message';
import { AppState, AppStateStatus } from 'react-native';

// Define the context type
interface DocumentUploadContextType {
  uploadDocument: (document: Document) => Document;
  pendingDocuments: Document[];
  verifiedDocuments: Document[];
  getVerifiedDocumentByType: (documentType: string) => Document | null;
  clearPendingDocuments: () => void;
  isUploading: boolean;
  verifyDocument: (documentId: string) => void;
  rejectDocument: (documentId: string, reason: string) => void;
  areAllDocumentsVerified: (documentIds: string[]) => boolean;
  getDocumentById: (documentId: string) => Document | null;
  documentsInVerification: string[];
}

// Create the context with a default value
const DocumentUploadContext = createContext<DocumentUploadContextType>({
  uploadDocument: () => ({} as Document),
  pendingDocuments: [],
  verifiedDocuments: [],
  getVerifiedDocumentByType: () => null,
  clearPendingDocuments: () => {},
  isUploading: false,
  verifyDocument: () => {},
  rejectDocument: () => {},
  areAllDocumentsVerified: () => false,
  getDocumentById: () => null,
  documentsInVerification: [],
});

// Custom hook to use the document upload context
export const useDocumentUpload = () => useContext(DocumentUploadContext);

// Provider component
export const DocumentUploadProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State for documents - initialize with empty arrays
  const [pendingDocuments, setPendingDocuments] = useState<Document[]>([]);
  const [verifiedDocuments, setVerifiedDocuments] = useState<Document[]>([]);

  const [isUploading, setIsUploading] = useState(false);
  // Track documents currently in verification process
  const [documentsInVerification, setDocumentsInVerification] = useState<string[]>([]);

  // Use refs to prevent unnecessary re-renders
  const pendingDocumentsRef = useRef<Document[]>(pendingDocuments);
  const verifiedDocumentsRef = useRef<Document[]>(verifiedDocuments);
  const documentsInVerificationRef = useRef<string[]>(documentsInVerification);

  // Update refs when state changes
  useEffect(() => {
    pendingDocumentsRef.current = pendingDocuments;
  }, [pendingDocuments]);

  useEffect(() => {
    verifiedDocumentsRef.current = verifiedDocuments;
  }, [verifiedDocuments]);

  useEffect(() => {
    documentsInVerificationRef.current = documentsInVerification;
  }, [documentsInVerification]);

  // Use a ref to track app state
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Function to get a document by ID from either pending or verified documents
  const getDocumentById = useCallback((documentId: string): Document | null => {
    // Check pending documents first
    let document = pendingDocumentsRef.current.find(doc => doc.id === documentId);

    // If not found in pending, check verified documents
    if (!document) {
      document = verifiedDocumentsRef.current.find(doc => doc.id === documentId);
    }

    return document || null;
  }, []);

  // Function to verify a document (move from pending to verified)
  const verifyDocument = useCallback((documentId: string) => {
    console.log('[DocumentUploadContext] Verifying document:', documentId);

    // Find the document in pending documents
    const documentIndex = pendingDocumentsRef.current.findIndex(doc => doc.id === documentId);

    if (documentIndex !== -1) {
      // Get the document
      const document = {...pendingDocumentsRef.current[documentIndex]};

      // Update status to verified
      document.status = 'verified';

      // Update refs first to avoid stale data
      // Remove from pending documents ref
      pendingDocumentsRef.current = pendingDocumentsRef.current.filter(doc => doc.id !== documentId);

      // Add to verified documents ref
      verifiedDocumentsRef.current = [document, ...verifiedDocumentsRef.current];

      // Remove from documents in verification ref
      documentsInVerificationRef.current = documentsInVerificationRef.current.filter(id => id !== documentId);

      // Batch state updates in a single animation frame to reduce re-renders
      requestAnimationFrame(() => {
        // Remove from pending documents
        setPendingDocuments(prev => {
          const updated = [...prev];
          updated.splice(documentIndex, 1);
          return updated;
        });

        // Add to verified documents
        setVerifiedDocuments(prev => [document, ...prev]);

        // Remove from documents in verification
        setDocumentsInVerification(prev => prev.filter(id => id !== documentId));
      });

      // Show success toast
      Toast.show({
        type: 'success',
        text1: 'Document Verified',
        text2: `${document.name} has been verified successfully`,
        visibilityTime: 3000,
        topOffset: 60,
      });
    }
  }, []);

  // Function to reject a document
  const rejectDocument = useCallback((documentId: string, reason: string) => {
    console.log('[DocumentUploadContext] Rejecting document:', documentId, 'Reason:', reason);

    // Find the document in pending documents
    const documentIndex = pendingDocumentsRef.current.findIndex(doc => doc.id === documentId);

    if (documentIndex !== -1) {
      // Get the document
      const document = {...pendingDocumentsRef.current[documentIndex]};

      // Update status to rejected and add reason
      document.status = 'rejected';
      document.reason = reason;

      // Update refs first to avoid stale data
      // Update in pending documents ref (we keep rejected documents in the pending list)
      pendingDocumentsRef.current = pendingDocumentsRef.current.map(doc =>
        doc.id === documentId ? document : doc
      );

      // Remove from documents in verification ref
      documentsInVerificationRef.current = documentsInVerificationRef.current.filter(id => id !== documentId);

      // Batch state updates in a single animation frame to reduce re-renders
      requestAnimationFrame(() => {
        // Update in pending documents (we keep rejected documents in the pending list)
        setPendingDocuments(prev => {
          const updated = [...prev];
          updated[documentIndex] = document;
          return updated;
        });

        // Remove from documents in verification
        setDocumentsInVerification(prev => prev.filter(id => id !== documentId));
      });

      // Show rejection toast
      Toast.show({
        type: 'error',
        text1: 'Document Rejected',
        text2: `${document.name} has been rejected: ${reason}`,
        visibilityTime: 4000,
        topOffset: 60,
      });
    }
  }, []);

  // Function to check if all documents in a list are verified
  const areAllDocumentsVerified = useCallback((documentIds: string[]): boolean => {
    // If no documents, return true
    if (!documentIds || documentIds.length === 0) return true;

    // Check if any document is still in verification
    const anyInVerification = documentIds.some(id =>
      documentsInVerificationRef.current.includes(id)
    );

    if (anyInVerification) return false;

    // Check if any document is still pending
    const anyPending = documentIds.some(id => {
      const doc = pendingDocumentsRef.current.find(d => d.id === id);
      return doc !== undefined;
    });

    return !anyPending;
  }, []);

  // Store document verification timers
  const documentTimersRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Track uploading state with a ref to prevent re-renders
  const isUploadingRef = useRef(false);

  // Track document rejection counter to occasionally reject a document
  const rejectionCounterRef = useRef(0);

  // Function to upload a document with verification simulation
  const uploadDocument = useCallback((document: Document) => {
    console.log('[DocumentUploadContext] Uploading document:', document.name);

    // Set uploading state in ref without triggering re-render
    isUploadingRef.current = true;

    // Only update the state if we need to show loading UI somewhere
    // Use requestAnimationFrame to batch with other UI updates
    requestAnimationFrame(() => {
      setIsUploading(true);
    });

    // Check if a document with the same type already exists in verified documents
    const existingVerifiedDoc = verifiedDocumentsRef.current.find(
      doc => doc.name === document.name && doc.type === document.type
    );

    // Check if a document with the same type already exists in pending documents
    const existingPendingDoc = pendingDocumentsRef.current.find(
      doc => doc.name === document.name && doc.type === document.type
    );

    // If document already exists in verified documents, just return that document
    if (existingVerifiedDoc) {
      console.log('[DocumentUploadContext] Document already verified:', existingVerifiedDoc.id);

      // Reset uploading state without causing immediate re-render
      isUploadingRef.current = false;

      // Use requestAnimationFrame to batch UI updates
      requestAnimationFrame(() => {
        // Reset state after a short delay to allow UI to complete
        setTimeout(() => {
          setIsUploading(false);
        }, 300);
      });

      // Show reuse toast
      Toast.show({
        type: 'success',
        text1: 'Document Reused',
        text2: 'Using existing verified document',
        visibilityTime: 3000,
        topOffset: 60,
      });

      return existingVerifiedDoc;
    }

    // If document already exists in pending documents, just return that document
    if (existingPendingDoc) {
      console.log('[DocumentUploadContext] Document already pending:', existingPendingDoc.id);

      // Reset uploading state without causing immediate re-render
      isUploadingRef.current = false;

      // Use requestAnimationFrame to batch UI updates
      requestAnimationFrame(() => {
        // Reset state after a short delay to allow UI to complete
        setTimeout(() => {
          setIsUploading(false);
        }, 300);
      });

      // Show reuse toast
      Toast.show({
        type: 'info',
        text1: 'Document Pending',
        text2: 'This document is already pending verification',
        visibilityTime: 3000,
        topOffset: 60,
      });

      return existingPendingDoc;
    }

    // Create a copy of the document with a new ID if not provided
    const newDocument = {
      ...document,
      id: document.id || Date.now().toString(),
      status: 'pending' as const,
      date: new Date().toISOString().split('T')[0], // Ensure date is set
    };

    console.log('[DocumentUploadContext] Created new document with ID:', newDocument.id);

    // Batch state updates to reduce re-renders
    // First update our refs
    pendingDocumentsRef.current = [newDocument, ...pendingDocumentsRef.current];
    documentsInVerificationRef.current = [...documentsInVerificationRef.current, newDocument.id];

    // Then schedule the state updates in a single animation frame
    requestAnimationFrame(() => {
      // Add document to pending documents
      setPendingDocuments(prev => [newDocument, ...prev]);

      // Add to documents in verification
      setDocumentsInVerification(prev => [...prev, newDocument.id]);
    });

    // Only show upload success toast, not verification status
    Toast.show({
      type: 'info',
      text1: 'Document Uploaded',
      text2: 'Document uploaded successfully and is pending verification',
      visibilityTime: 3000,
      topOffset: 60,
    });

    // Reset uploading state without causing immediate re-render
    isUploadingRef.current = false;

    // Use requestAnimationFrame to batch UI updates
    requestAnimationFrame(() => {
      // Reset state after a short delay to allow UI to complete
      setTimeout(() => {
        setIsUploading(false);
      }, 300);
    });

    // Simulate verification process
    // Use exactly 15 seconds for verification as requested
    const verificationTime = 15000; // 15 seconds

    console.log(`[DocumentUploadContext] Document ${newDocument.id} will be verified in ${verificationTime/1000} seconds`);

    // Clear any existing timer for this document
    if (documentTimersRef.current[newDocument.id]) {
      clearTimeout(documentTimersRef.current[newDocument.id]);
      delete documentTimersRef.current[newDocument.id];
    }

    // Increment rejection counter
    rejectionCounterRef.current += 1;

    // Determine if this document should be rejected (every 5th document)
    const shouldReject = rejectionCounterRef.current % 5 === 0;

    // Use a more reliable timer approach with a fixed 15 second delay
    const timer = setTimeout(() => {
      console.log(`[DocumentUploadContext] Verification timer completed for document ${newDocument.id}`);

      // Remove the timer reference
      delete documentTimersRef.current[newDocument.id];

      if (shouldReject) {
        // Reject the document with a random reason
        const rejectionReasons = [
          'Document is not clearly legible',
          'Document has expired',
          'Document is missing required information',
          'Document appears to be modified or tampered with',
          'Document type does not match the required format'
        ];
        const randomReason = rejectionReasons[Math.floor(Math.random() * rejectionReasons.length)];
        rejectDocument(newDocument.id, randomReason);
      } else {
        // Verify the document
        verifyDocument(newDocument.id);
      }
    }, verificationTime);

    // Store the timer reference in our ref
    documentTimersRef.current[newDocument.id] = timer;

    // Also store in global for debugging
    (global as any).__documentTimers = (global as any).__documentTimers || {};
    (global as any).__documentTimers[newDocument.id] = timer;

    return newDocument;
  }, [verifyDocument, rejectDocument]);

  // Function to clear pending documents
  const clearPendingDocuments = useCallback(() => {
    setPendingDocuments([]);
  }, []);

  // Function to get a verified document by type
  const getVerifiedDocumentByType = useCallback((documentType: string) => {
    console.log('[DocumentUploadContext] Looking for verified document of type:', documentType);
    const document = verifiedDocumentsRef.current.find(doc =>
      doc.name === documentType && doc.status === 'verified'
    );

    if (document) {
      console.log('[DocumentUploadContext] Found verified document:', document.id);
    } else {
      console.log('[DocumentUploadContext] No verified document found for type:', documentType);
    }

    return document || null;
  }, []);

  // Set up app state change listener
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      // When app comes back to foreground, reset uploading state
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        // Reset uploading ref first
        isUploadingRef.current = false;

        // Then schedule the state update in an animation frame to prevent re-renders
        requestAnimationFrame(() => {
          setIsUploading(false);
        });

        // Check if any documents are in verification but don't have active timers
        documentsInVerificationRef.current.forEach(docId => {
          if (!documentTimersRef.current[docId]) {
            console.log(`[DocumentUploadContext] Document ${docId} is in verification but has no timer, creating new timer`);

            // Increment rejection counter to maintain the same pattern
            rejectionCounterRef.current += 1;

            // Determine if this document should be rejected (every 5th document)
            const shouldReject = rejectionCounterRef.current % 5 === 0;

            // Create a new timer for this document
            const timer = setTimeout(() => {
              console.log(`[DocumentUploadContext] Verification timer completed for document ${docId} (recreated)`);

              // Remove the timer reference
              delete documentTimersRef.current[docId];

              if (shouldReject) {
                // Reject the document with a random reason
                const rejectionReasons = [
                  'Document is not clearly legible',
                  'Document has expired',
                  'Document is missing required information',
                  'Document appears to be modified or tampered with',
                  'Document type does not match the required format'
                ];
                const randomReason = rejectionReasons[Math.floor(Math.random() * rejectionReasons.length)];
                rejectDocument(docId, randomReason);
              } else {
                // Verify the document
                verifyDocument(docId);
              }
            }, 5000); // Use a shorter time for recreated timers

            // Store the timer reference
            documentTimersRef.current[docId] = timer;
          }
        });
      }

      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();

      // Clean up any document timers when the component unmounts
      Object.values(documentTimersRef.current).forEach(timer => {
        clearTimeout(timer);
      });
      documentTimersRef.current = {};

      // Also clean up global timers
      if ((global as any).__documentTimers) {
        Object.values((global as any).__documentTimers).forEach((timer: any) => {
          clearTimeout(timer);
        });
        (global as any).__documentTimers = {};
      }
    };
  }, [verifyDocument, rejectDocument]);

  // Context value - memoize to prevent unnecessary re-renders
  const value = React.useMemo(() => ({
    uploadDocument,
    pendingDocuments,
    verifiedDocuments,
    getVerifiedDocumentByType,
    clearPendingDocuments,
    isUploading,
    verifyDocument,
    rejectDocument,
    areAllDocumentsVerified,
    getDocumentById,
    documentsInVerification,
  }), [
    uploadDocument,
    pendingDocuments,
    verifiedDocuments,
    getVerifiedDocumentByType,
    clearPendingDocuments,
    isUploading,
    verifyDocument,
    rejectDocument,
    areAllDocumentsVerified,
    getDocumentById,
    documentsInVerification
  ]);

  return (
    <DocumentUploadContext.Provider value={value}>
      {children}
    </DocumentUploadContext.Provider>
  );
};
