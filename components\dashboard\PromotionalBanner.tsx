import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { createTheme } from '@/constants/theme';
import { ArrowRight } from 'lucide-react-native';
import Carousel from 'react-native-reanimated-carousel';
import { useTheme } from '@/context/ThemeContext';

const { width } = Dimensions.get('window');

// Define promotion type
type Promotion = {
  id: string;
  title: string;
  description: string;
  image: string;
  color: string;
};

export default function PromotionalBanner() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(typography, spacing, borders);

  // Define promotions with the current theme colors - empty array for now
  // In a real app, these would be loaded from an API
  const promotions: Promotion[] = [
    {
      id: '1',
      title: 'Welcome to Inerca',
      description: 'Your comprehensive insurance management platform',
      image: 'https://images.pexels.com/photos/3943716/pexels-photo-3943716.jpeg',
      color: colors.primary[500],
    }
  ];

  const renderItem = ({ item }: { item: Promotion }) => (
    <TouchableOpacity
      style={[styles.bannerContainer]}
      onPress={() => console.log(`Promotion ${item.id} pressed`)}
    >
      <Image source={{ uri: item.image }} style={styles.bannerImage} />
      <View style={[styles.overlay, { backgroundColor: `${item.color}80` }]} />
      <View style={styles.bannerContent}>
        <Text style={[styles.bannerTitle, { color: colors.white }]}>{item.title}</Text>
        <Text style={[styles.bannerDescription, { color: colors.white }]}>{item.description}</Text>
        <View style={styles.learnMore}>
          <Text style={[styles.learnMoreText, { color: colors.white }]}>Learn More</Text>
          <ArrowRight size={16} color={colors.white} />
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Carousel
        loop
        width={width - spacing.lg * 2}
        height={180}
        autoPlay={true}
        data={promotions}
        scrollAnimationDuration={5000}
        renderItem={renderItem}
      />
    </View>
  );
}

// Define styles with a function to access theme values
const getStyles = (typography: any, spacing: any, borders: any) => StyleSheet.create({
  container: {
    marginVertical: spacing.md,
  },
  bannerContainer: {
    borderRadius: borders.radius.lg,
    overflow: 'hidden',
    marginHorizontal: spacing.md,
    height: 180,
    position: 'relative',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bannerContent: {
    padding: spacing.md,
    justifyContent: 'center',
    height: '100%',
    zIndex: 1,
  },
  bannerTitle: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.lg,
    marginBottom: spacing.xs,
  },
  bannerDescription: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.sm,
    marginBottom: spacing.sm,
  },
  learnMore: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  learnMoreText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
    marginRight: spacing.xs,
  },
});