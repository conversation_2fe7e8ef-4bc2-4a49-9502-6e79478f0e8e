/**
 * Backend Integration Test Utility
 * 
 * This utility provides functions to test the backend integration
 * and verify that all API endpoints are working correctly.
 */

import { apiService } from '@/services/api';
import { chatService } from '@/services/chatService';
import { claimsService } from '@/services/claimsService';
import { 
  UserCreate, 
  PolicyCreate, 
  ClaimCreate, 
  ChatSessionCreate, 
  PaymentCreate,
  MessageParticipant,
  PolicyType,
  PolicyProvider,
  PolicyStatus,
  PaymentCurrency
} from '@/types/backend';

export interface TestResult {
  endpoint: string;
  success: boolean;
  error?: string;
  data?: any;
}

export class BackendIntegrationTest {
  private results: TestResult[] = [];

  // Test authentication endpoints
  async testAuthentication(): Promise<TestResult[]> {
    const authTests: TestResult[] = [];

    // Test user registration
    try {
      const testUser: UserCreate = {
        email: `test${Date.now()}@example.com`,
        hashed_password: 'hashedpassword123',
        username: 'testuser',
        phone_number: '+26771234567',
      };

      console.log('[BackendTest] Testing user registration...');
      const user = await apiService.auth.register(testUser);
      authTests.push({
        endpoint: 'POST /api/v1/auth/register',
        success: true,
        data: { userId: user.id }
      });
    } catch (error: any) {
      authTests.push({
        endpoint: 'POST /api/v1/auth/register',
        success: false,
        error: error.message
      });
    }

    // Test user login
    try {
      console.log('[BackendTest] Testing user login...');
      const loginResult = await apiService.auth.login('<EMAIL>', 'password123');
      authTests.push({
        endpoint: 'POST /api/v1/auth/login',
        success: true,
        data: { hasToken: !!loginResult.access_token }
      });
    } catch (error: any) {
      authTests.push({
        endpoint: 'POST /api/v1/auth/login',
        success: false,
        error: error.message
      });
    }

    return authTests;
  }

  // Test chat endpoints
  async testChatSystem(): Promise<TestResult[]> {
    const chatTests: TestResult[] = [];

    try {
      // Test creating a chat session
      console.log('[BackendTest] Testing chat session creation...');
      const sessionData: ChatSessionCreate = {
        title: 'Test Support Session',
        participant_type: MessageParticipant.USER
      };

      const session = await chatService.createSession(sessionData);
      chatTests.push({
        endpoint: 'POST /api/v1/session',
        success: true,
        data: { sessionId: session.id }
      });

      // Test sending a message
      console.log('[BackendTest] Testing message sending...');
      const message = await chatService.sendUserMessage(session.id, 'Hello, this is a test message');
      chatTests.push({
        endpoint: 'POST /api/v1/session/{id}/message',
        success: true,
        data: { messageId: message.id }
      });

      // Test getting sessions
      console.log('[BackendTest] Testing session retrieval...');
      const sessions = await chatService.getSessions();
      chatTests.push({
        endpoint: 'GET /api/v1/conversations',
        success: true,
        data: { sessionCount: sessions.length }
      });

    } catch (error: any) {
      chatTests.push({
        endpoint: 'Chat System',
        success: false,
        error: error.message
      });
    }

    return chatTests;
  }

  // Test claims endpoints
  async testClaimsSystem(): Promise<TestResult[]> {
    const claimsTests: TestResult[] = [];

    try {
      // Test creating a claim
      console.log('[BackendTest] Testing claim creation...');
      const claimData: ClaimCreate = {
        policy_id: 'test-policy-id',
        claim_type: 'motor',
        description: 'Test claim for backend integration',
        incident_date: new Date().toISOString(),
        claim_amount: 5000
      };

      const claim = await claimsService.createClaim(claimData);
      claimsTests.push({
        endpoint: 'POST /api/v1/claims',
        success: true,
        data: { claimId: claim.id }
      });

      // Test getting claims
      console.log('[BackendTest] Testing claims retrieval...');
      const claims = await claimsService.getClaims();
      claimsTests.push({
        endpoint: 'GET /api/v1/claims',
        success: true,
        data: { claimCount: claims.length }
      });

    } catch (error: any) {
      claimsTests.push({
        endpoint: 'Claims System',
        success: false,
        error: error.message
      });
    }

    return claimsTests;
  }

  // Test policy endpoints
  async testPolicySystem(): Promise<TestResult[]> {
    const policyTests: TestResult[] = [];

    try {
      // Test creating a policy
      console.log('[BackendTest] Testing policy creation...');
      const policyData: PolicyCreate = {
        policy_type: PolicyType.MOTOR,
        policy_provider: PolicyProvider.HOLLAND,
        status: PolicyStatus.PENDING,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        policy_details: {
          make: 'TOYOTA',
          model: 'Corolla',
          year: 2020,
          engine_number: 'ENG123456',
          chassis_number: 'CHS789012',
          registration_number: 'ABC123GP',
          current_mileage: 50000,
          insured_value: 150000,
          use: 'PRIVATE',
          is_grey_import: false,
          addtional_extensions: {
            radio_cassette_player: true,
            loss_of_use: false,
            towing_costs: true,
            personal_accident: false
          },
          required_documents: {
            driver_license: true,
            vehicle_registration_book: true,
            vehicle_valuation_report: false,
            previous_insurance_policy: false
          }
        },
        user_id: 'test-user-id'
      };

      const policy = await apiService.policies.createPolicy(policyData);
      policyTests.push({
        endpoint: 'POST /api/v1/policies',
        success: true,
        data: { policyId: policy.id }
      });

      // Test getting policies
      console.log('[BackendTest] Testing policies retrieval...');
      const policies = await apiService.policies.getPolicies();
      policyTests.push({
        endpoint: 'GET /api/v1/policies',
        success: true,
        data: { policyCount: policies.length }
      });

    } catch (error: any) {
      policyTests.push({
        endpoint: 'Policy System',
        success: false,
        error: error.message
      });
    }

    return policyTests;
  }

  // Test payment endpoints
  async testPaymentSystem(): Promise<TestResult[]> {
    const paymentTests: TestResult[] = [];

    try {
      // Test creating a payment
      console.log('[BackendTest] Testing payment creation...');
      const paymentData: PaymentCreate = {
        policy_id: 'test-policy-id',
        currency: PaymentCurrency.BWP,
        amount: 1500,
        payment_date: new Date().toISOString()
      };

      const payment = await apiService.payments.createPayment(paymentData);
      paymentTests.push({
        endpoint: 'POST /api/v1/payments',
        success: true,
        data: { paymentId: payment.id }
      });

      // Test getting payments
      console.log('[BackendTest] Testing payments retrieval...');
      const payments = await apiService.payments.getPayments();
      paymentTests.push({
        endpoint: 'GET /api/v1/payments',
        success: true,
        data: { paymentCount: payments.length }
      });

    } catch (error: any) {
      paymentTests.push({
        endpoint: 'Payment System',
        success: false,
        error: error.message
      });
    }

    return paymentTests;
  }

  // Run all tests
  async runAllTests(): Promise<TestResult[]> {
    console.log('[BackendTest] Starting comprehensive backend integration test...');
    
    const allResults: TestResult[] = [];

    // Run authentication tests
    const authResults = await this.testAuthentication();
    allResults.push(...authResults);

    // Run chat system tests
    const chatResults = await this.testChatSystem();
    allResults.push(...chatResults);

    // Run claims system tests
    const claimsResults = await this.testClaimsSystem();
    allResults.push(...claimsResults);

    // Run policy system tests
    const policyResults = await this.testPolicySystem();
    allResults.push(...policyResults);

    // Run payment system tests
    const paymentResults = await this.testPaymentSystem();
    allResults.push(...paymentResults);

    this.results = allResults;
    this.logResults();

    return allResults;
  }

  // Log test results
  private logResults(): void {
    console.log('\n=== Backend Integration Test Results ===');
    
    const successful = this.results.filter(r => r.success);
    const failed = this.results.filter(r => !r.success);

    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log(`📊 Total: ${this.results.length}`);

    if (failed.length > 0) {
      console.log('\n❌ Failed Tests:');
      failed.forEach(result => {
        console.log(`  - ${result.endpoint}: ${result.error}`);
      });
    }

    if (successful.length > 0) {
      console.log('\n✅ Successful Tests:');
      successful.forEach(result => {
        console.log(`  - ${result.endpoint}`);
      });
    }

    console.log('\n=== End Test Results ===\n');
  }

  // Get test summary
  getTestSummary(): { total: number; successful: number; failed: number; successRate: number } {
    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    const total = this.results.length;
    const successRate = total > 0 ? (successful / total) * 100 : 0;

    return { total, successful, failed, successRate };
  }
}

// Export singleton instance
export const backendIntegrationTest = new BackendIntegrationTest();
