import React, { memo } from 'react';
import { View } from 'react-native';
import { Document } from './types';
import PureDocumentUploader from './PureDocumentUploader';

interface IsolatedDocumentUploaderProps {
  preselectedDocumentType?: string;
  onDocumentUploaded?: () => void;
}

/**
 * A completely isolated document uploader component that won't cause app rebundling
 * This is now a simple wrapper around PureDocumentUploader to prevent re-renders
 */
const IsolatedDocumentUploader: React.FC<IsolatedDocumentUploaderProps> = memo(({
  preselectedDocumentType,
  onDocumentUploaded
}) => {
  // Create a document uploaded handler that calls the callback
  const handleDocumentUploaded = (document: Document) => {
    console.log('[IsolatedDocumentUploader] Document uploaded:', document.id);
    if (onDocumentUploaded) {
      onDocumentUploaded();
    }
  };

  return (
    <View style={{ width: '100%' }}>
      <PureDocumentUploader
        preselectedDocumentType={preselectedDocumentType}
        onDocumentUploaded={handleDocumentUploaded}
      />
    </View>
  );
});

export default IsolatedDocumentUploader;
