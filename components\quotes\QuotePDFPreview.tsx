import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  FileText,
  Download,
  Share2,
  ExternalLink,
  AlertCircle,
} from 'lucide-react-native';
import { Quote } from '@/types/quote.types';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import Toast from 'react-native-toast-message';
import { FileSharing } from '@/utils/FileSharing';
import Animated, { FadeIn } from 'react-native-reanimated';

interface QuotePDFPreviewProps {
  quote: Quote;
  pdfUrl?: string;
}

const QuotePDFPreview: React.FC<QuotePDFPreviewProps> = ({
  quote,
  pdfUrl,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get the PDF URL from the quote or from props
  const documentUrl = pdfUrl || quote.pdfUrl;

  // Handle opening the PDF in an external viewer
  const handleOpenPDF = async () => {
    if (!documentUrl) {
      setError('PDF URL is not available');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await FileSharing.openFile(documentUrl, 'application/pdf');
    } catch (error) {
      console.error('Error opening PDF:', error);
      setError('Failed to open PDF. Please try again.');

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to open PDF. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
        props: {
          style: {
            width: '90%',
            alignSelf: 'center'
          }
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle downloading the PDF
  const handleDownloadPDF = async () => {
    if (!documentUrl) {
      setError('PDF URL is not available');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Show downloading toast
      Toast.show({
        type: 'info',
        text1: 'Downloading',
        text2: 'Preparing PDF for download...',
        visibilityTime: 2000,
        topOffset: 60,
        props: {
          style: {
            width: '90%',
            alignSelf: 'center'
          }
        }
      });

      console.log('[QuotePDFPreview] Downloading PDF from URL:', documentUrl);

      // Create a unique filename
      const filename = `quote_${quote.id}_${Date.now()}.pdf`;

      // Use the downloads directory which is more accessible to the user
      const fileUri = `${FileSystem.documentDirectory}downloads/`;

      // Ensure the downloads directory exists
      const dirInfo = await FileSystem.getInfoAsync(fileUri);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(fileUri, { intermediates: true });
      }

      const fullPath = `${fileUri}${filename}`;

      console.log('[QuotePDFPreview] Downloading to:', fullPath);

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(
        documentUrl,
        fullPath
      );

      console.log('[QuotePDFPreview] Download result:', downloadResult);

      if (downloadResult.status === 200) {
        // Show success toast
        Toast.show({
          type: 'success',
          text1: 'Download Complete',
          text2: 'PDF downloaded successfully',
          visibilityTime: 3000,
          topOffset: 60,
          props: {
            style: {
              width: '90%',
              alignSelf: 'center'
            }
          }
        });

        // Don't automatically open the file after download
        // Just notify the user that the download is complete
        console.log('[QuotePDFPreview] File downloaded to:', fullPath);

        return fullPath;
      } else {
        throw new Error(`Download failed with status ${downloadResult.status}`);
      }
    } catch (error) {
      console.error('Error downloading PDF:', error);
      setError('Failed to download PDF. Please try again.');

      Toast.show({
        type: 'error',
        text1: 'Download Failed',
        text2: 'Failed to download PDF. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
        props: {
          style: {
            width: '90%',
            alignSelf: 'center'
          }
        }
      });

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sharing the PDF
  const handleSharePDF = async () => {
    if (!documentUrl) {
      setError('PDF URL is not available');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Show downloading toast
      Toast.show({
        type: 'info',
        text1: 'Preparing',
        text2: 'Getting PDF ready for sharing...',
        visibilityTime: 2000,
        topOffset: 60,
        props: {
          style: {
            width: '90%',
            alignSelf: 'center'
          }
        }
      });

      // Create a unique filename
      const filename = `quote_${quote.id}_${Date.now()}.pdf`;
      const fileUri = `${FileSystem.documentDirectory}${filename}`;

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(
        documentUrl,
        fileUri
      );

      if (downloadResult.status === 200) {
        // Share the file
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/pdf',
          dialogTitle: `Share Quote #${quote.id}`,
          UTI: 'com.adobe.pdf'
        });

        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'PDF ready for sharing',
          visibilityTime: 3000,
          topOffset: 60,
          props: {
            style: {
              width: '90%',
              alignSelf: 'center'
            }
          }
        });
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('Error sharing PDF:', error);
      setError('Failed to share PDF. Please try again.');

      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to share PDF. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
        props: {
          style: {
            width: '90%',
            alignSelf: 'center'
          }
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.lg,
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    pdfContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
      backgroundColor: `${colors.primary[500]}10`,
      borderRadius: borders.radius.md,
      marginBottom: spacing.md,
    },
    pdfIcon: {
      marginBottom: spacing.md,
    },
    pdfText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      textAlign: 'center',
      marginBottom: spacing.md,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginTop: spacing.md,
    },
    actionButton: {
      alignItems: 'center',
      padding: spacing.sm,
    },
    actionText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginTop: spacing.xs,
    },
    errorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${colors.error[500]}15`,
      padding: spacing.md,
      borderRadius: borders.radius.md,
      marginTop: spacing.md,
    },
    errorText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.error[500],
      marginLeft: spacing.sm,
      flex: 1,
    },
  });

  return (
    <Animated.View
      style={styles.container}
      entering={FadeIn.delay(200).duration(500)}
    >
      <Text style={styles.title}>Quote PDF</Text>

      <View style={styles.pdfContainer}>
        <FileText
          size={64}
          color={colors.primary[500]}
          style={styles.pdfIcon}
        />
        <Text style={styles.pdfText}>
          {documentUrl
            ? `Quote #${quote.id} PDF\n${quote.type.toUpperCase()} Insurance Quote`
            : 'PDF not available for this quote yet'}
        </Text>

        {documentUrl && (
          <View style={styles.actionsContainer}>
            {isLoading ? (
              <ActivityIndicator color={colors.primary[500]} size="large" />
            ) : (
              <>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={handleOpenPDF}
                  disabled={!documentUrl}
                >
                  <ExternalLink size={24} color={colors.primary[500]} />
                  <Text style={styles.actionText}>Open</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={handleDownloadPDF}
                  disabled={!documentUrl}
                >
                  <Download size={24} color={colors.primary[500]} />
                  <Text style={styles.actionText}>Download</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={handleSharePDF}
                  disabled={!documentUrl}
                >
                  <Share2 size={24} color={colors.primary[500]} />
                  <Text style={styles.actionText}>Share</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        )}
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <AlertCircle size={20} color={colors.error[500]} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </Animated.View>
  );
};

export default QuotePDFPreview;
