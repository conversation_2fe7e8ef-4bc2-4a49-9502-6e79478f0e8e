import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import usePolicyStore, { Policy } from '@/store/policyStore';
import RenewalNotification from './RenewalNotification';
import AsyncStorage from '@react-native-async-storage/async-storage';

const NotificationManager: React.FC = () => {
  const { policies } = usePolicyStore();
  const [renewalNotifications, setRenewalNotifications] = useState<Array<{
    policy: Policy;
    daysUntilRenewal: number;
  }>>([]);
  const [dismissedNotifications, setDismissedNotifications] = useState<string[]>([]);

  // Load dismissed notifications from storage
  useEffect(() => {
    const loadDismissedNotifications = async () => {
      try {
        const dismissed = await AsyncStorage.getItem('dismissedRenewalNotifications');
        if (dismissed) {
          setDismissedNotifications(JSON.parse(dismissed));
        }
      } catch (error) {
        console.error('Error loading dismissed notifications:', error);
      }
    };

    loadDismissedNotifications();
  }, []);

  // Check for policies that need renewal notifications
  useEffect(() => {
    const checkForRenewalNotifications = () => {
      const today = new Date();
      const notifications: Array<{ policy: Policy; daysUntilRenewal: number }> = [];

      policies.forEach(policy => {
        // Skip if policy is not active or if notification has been dismissed
        if (policy.status !== 'active' || dismissedNotifications.includes(policy.id)) {
          return;
        }

        const endDate = new Date(policy.endDate);
        const diffTime = endDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Show notification if policy expires within 30 days or is overdue
        if (diffDays <= 30) {
          notifications.push({
            policy,
            daysUntilRenewal: diffDays,
          });
        }
      });

      // Sort notifications by urgency (overdue first, then by days until renewal)
      notifications.sort((a, b) => {
        if (a.daysUntilRenewal < 0 && b.daysUntilRenewal >= 0) return -1;
        if (a.daysUntilRenewal >= 0 && b.daysUntilRenewal < 0) return 1;
        return a.daysUntilRenewal - b.daysUntilRenewal;
      });

      setRenewalNotifications(notifications);
    };

    checkForRenewalNotifications();
  }, [policies, dismissedNotifications]);

  // Handle dismissing a notification
  const handleDismissNotification = async (policyId: string) => {
    const updatedDismissed = [...dismissedNotifications, policyId];
    setDismissedNotifications(updatedDismissed);
    
    try {
      await AsyncStorage.setItem('dismissedRenewalNotifications', JSON.stringify(updatedDismissed));
    } catch (error) {
      console.error('Error saving dismissed notifications:', error);
    }
  };

  // Limit the number of notifications shown at once
  const visibleNotifications = renewalNotifications.slice(0, 3);

  return (
    <View style={styles.container}>
      {visibleNotifications.map(({ policy, daysUntilRenewal }) => (
        <RenewalNotification
          key={policy.id}
          policy={policy}
          daysUntilRenewal={daysUntilRenewal}
          onDismiss={() => handleDismissNotification(policy.id)}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
});

export default NotificationManager;
