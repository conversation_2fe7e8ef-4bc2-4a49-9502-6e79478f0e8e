import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { Document as DocumentType } from '@/components/documents/types';

// Export Document type for use in other files
export type Document = DocumentType;

// Define document store state
interface DocumentState {
  documents: Document[];
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchDocuments: () => Promise<void>;
  getDocumentById: (id: string) => Document | null;
  addDocument: (document: Omit<Document, 'id'>) => Promise<Document>;
  updateDocumentStatus: (id: string, status: 'pending' | 'verified' | 'rejected', details?: any) => Promise<void>;
  deleteDocument: (id: string) => Promise<void>;
  viewDocument: (id: string) => Promise<boolean>;
}

// Helper function to load documents from storage
const loadDocumentsFromStorage = async (): Promise<Document[] | null> => {
  try {
    const documentsJson = await AsyncStorage.getItem('documents');
    if (documentsJson) {
      return JSON.parse(documentsJson);
    }
    return null;
  } catch (error) {
    console.error('Error loading documents from storage:', error);
    return null;
  }
};

// Helper function to save documents to storage
const saveDocumentsToStorage = async (documents: Document[]): Promise<void> => {
  try {
    await AsyncStorage.setItem('documents', JSON.stringify(documents));
  } catch (error) {
    console.error('Error saving documents to storage:', error);
  }
};

// Create the store
const useDocumentStore = create<DocumentState>((set, get) => ({
  documents: [],
  isLoading: false,
  error: null,

  // Fetch all documents
  fetchDocuments: async () => {
    set({ isLoading: true, error: null });
    try {
      // Import documentService dynamically to avoid circular dependencies
      const { documentService } = await import('@/services/documentService');

      // Fetch documents from API
      const documents = await documentService.getAllDocuments();

      // Save to AsyncStorage for offline access
      await saveDocumentsToStorage(documents);

      // Update state
      set({ documents, isLoading: false });
    } catch (error) {
      console.error('Error fetching documents:', error);

      // Try to load from AsyncStorage as fallback
      const storedDocuments = await loadDocumentsFromStorage();

      if (storedDocuments && storedDocuments.length > 0) {
        console.log('Using documents from AsyncStorage as fallback:', storedDocuments.length);
        set({ documents: storedDocuments, isLoading: false });
        return;
      }

      set({
        documents: [],
        error: 'Failed to fetch documents. Please try again.',
        isLoading: false
      });
    }
  },

  // Get a document by ID
  getDocumentById: (id: string) => {
    return get().documents.find(document => document.id === id) || null;
  },

  // Add a new document
  addDocument: async (document: Omit<Document, 'id'>) => {
    set({ isLoading: true, error: null });
    try {
      // Import documentService dynamically to avoid circular dependencies
      const { documentService } = await import('@/services/documentService');

      // Prepare file object for upload
      const file = {
        uri: document.uri,
        name: document.fileName || 'document.pdf',
        type: document.fileType || 'application/pdf',
      };

      // Upload document to API
      const newDocument = await documentService.uploadDocument(
        file,
        document.type.toString(),
        document.metadata?.policyId
      );

      if (!newDocument) {
        throw new Error('Failed to upload document');
      }

      // Update state with new document
      let updatedDocuments: Document[] = [];
      set(state => {
        const newDocuments = [...state.documents, newDocument];
        updatedDocuments = newDocuments;
        return {
          documents: newDocuments,
          isLoading: false
        };
      });

      // Save to AsyncStorage
      await saveDocumentsToStorage(updatedDocuments);

      return newDocument;
    } catch (error) {
      console.error('Error adding document:', error);
      set({
        error: 'Failed to add document. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update document status
  updateDocumentStatus: async (id: string, status: 'pending' | 'verified' | 'rejected', details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Find the document
      const document = get().documents.find(d => d.id === id);

      if (!document) {
        throw new Error(`Document with ID ${id} not found`);
      }

      // In a real implementation, we would call an API to update the document status
      // For now, we'll just update the local state

      // Update the document
      const updatedDocument = {
        ...document,
        status,
        ...(status === 'rejected' && details?.reason ? { reason: details.reason } : {})
      };

      // Update state
      let updatedDocuments: Document[] = [];
      set(state => {
        const newDocuments = state.documents.map(d =>
          d.id === id ? updatedDocument : d
        );
        updatedDocuments = newDocuments;
        return {
          documents: newDocuments,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveDocumentsToStorage(updatedDocuments);

      // Show success toast
      if (status === 'verified') {
        showToast(
          'success',
          'Document Verified',
          'Document has been verified successfully',
          { visibilityTime: 3000 }
        );
      } else if (status === 'rejected') {
        showToast(
          'error',
          'Document Rejected',
          details?.reason || 'Document has been rejected',
          { visibilityTime: 3000 }
        );
      }

      return;
    } catch (error) {
      console.error('Error updating document status:', error);
      set({
        error: 'Failed to update document status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Delete a document
  deleteDocument: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      // Import documentService dynamically to avoid circular dependencies
      const { documentService } = await import('@/services/documentService');

      // Delete document from API
      const success = await documentService.deleteDocument(id);

      if (!success) {
        throw new Error('Failed to delete document');
      }

      // Update state
      let updatedDocuments: Document[] = [];
      set(state => {
        const newDocuments = state.documents.filter(d => d.id !== id);
        updatedDocuments = newDocuments;
        return {
          documents: newDocuments,
          isLoading: false,
        };
      });

      // Save to AsyncStorage
      await saveDocumentsToStorage(updatedDocuments);

      return;
    } catch (error) {
      console.error('Error deleting document:', error);
      set({
        error: 'Failed to delete document. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // View a document
  viewDocument: async (id: string): Promise<boolean> => {
    set({ isLoading: true, error: null });
    try {
      // Find the document
      const document = get().documents.find(d => d.id === id);

      if (!document) {
        throw new Error(`Document with ID ${id} not found`);
      }

      // Import documentService dynamically to avoid circular dependencies
      const { documentService } = await import('@/services/documentService');

      // View document using the service
      const success = await documentService.viewDocument(id);

      set({ isLoading: false });
      return success;
    } catch (error) {
      console.error('Error viewing document:', error);
      set({
        error: 'Failed to view document. Please try again.',
        isLoading: false
      });
      return false;
    }
  },
}));

export default useDocumentStore;
