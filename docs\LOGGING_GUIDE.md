# Comprehensive Logging Guide

This document outlines all the logging that has been added throughout the application for easy debugging.

## 🔍 **Logging Categories**

### **1. API Service Logging (`services/api.ts`)**

#### **Request Interceptor**
- Logs every API request with method, URL, headers, data, and params
- Logs auth token presence/absence
- Format: `[API Request] GET /api/v1/endpoint`

#### **Response Interceptor**
- Logs every API response with status, data, and headers
- Logs all errors with detailed error information
- Format: `[API Response] GET /api/v1/endpoint`

#### **Individual Endpoints**
All API endpoints now log:
- **Auth endpoints**: Login, register, refresh token, etc.
- **Profile endpoints**: Get, create, update profile
- **Policy endpoints**: Create, fetch, update policies
- **Document endpoints**: Upload, download, view documents
- **Claims endpoints**: All claim operations
- **Payment endpoints**: Payment proof uploads
- **Application endpoints**: Application management
- **Notification endpoints**: Notification operations
- **Admin endpoints**: Admin review operations

**Example Log Output:**
```
[Auth] Login attempt for email: <EMAIL>
[Auth] Login form data prepared
[API Request] POST /api/v1/login
[API Response] POST /api/v1/login
[Auth] Login response received: {access_token: "...", ...}
```

### **2. Redux Store Logging**

#### **Auth Slice (`store/authSlice.ts`)**
- Login process with detailed steps
- Registration process with user data
- Token storage and retrieval
- User object creation
- Profile completion checking

**Example Log Output:**
```
[Auth Slice] Attempting <NAME_EMAIL> with rememberMe=true, userType=individual
[Auth Slice] Calling backend login API
[Auth Slice] Login API response received: {...}
[Auth Slice] Getting current user details
[Auth Slice] Storing tokens in AsyncStorage
[Auth Slice] Login successful: {...}
```

#### **Profile Slice (`store/profileSlice.ts`)**
- Profile fetching operations
- Profile creation with data validation
- Profile updates with change tracking
- Error handling and messages

**Example Log Output:**
```
[Profile Slice] Fetching user profile
[Profile Slice] Profile fetched successfully: {...}
[Profile Slice] Creating profile with data: {...}
[Profile Slice] Profile created successfully: {...}
```

#### **Policy Slice (`store/policySlice.ts`)**
- Policy creation from quotes
- Policy fetching with filters
- Policy updates and status changes
- Error handling for policy operations

**Example Log Output:**
```
[Policy Slice] Creating policy with data: {...}
[Policy Slice] Policy created successfully: {...}
[Policy Slice] Fetching policies with filters: {...}
```

#### **Document Slice (`store/documentSlice.ts`)**
- Document upload with progress tracking
- File information and policy associations
- Upload progress simulation
- Error handling for document operations

**Example Log Output:**
```
[Document Slice] Starting document upload
[Document Slice] File: {...}
[Document Slice] Policy ID: abc123
[Document Slice] Upload progress: 45%
[Document Slice] Document uploaded successfully: {...}
```

### **3. Component Logging**

#### **ProfileForm Component (`components/profile/ProfileForm.tsx`)**
- Form submission attempts
- Form data validation
- Success/failure handling
- Redux dispatch results

**Example Log Output:**
```
[ProfileForm] Submit button pressed
[ProfileForm] Form data: {...}
[ProfileForm] Form validation passed
[ProfileForm] Creating new profile
[ProfileForm] Profile save successful
```

#### **Dashboard Component (`app/(app)/(tabs)/index.tsx`)**
- Component mounting
- Data fetching operations
- Profile completion checking

**Example Log Output:**
```
[Dashboard] Component mounted, fetching data
[Dashboard] Dispatching fetchProfile
[Dashboard] Fetching applications
```

### **4. Utility Function Logging**

#### **Policy Conversion (`utils/policyConversion.ts`)**
- Quote to policy conversion process
- Policy type mapping
- Policy provider selection
- Policy details mapping for each insurance type
- Final policy object creation

**Example Log Output:**
```
[Policy Conversion] Converting quote to policy: {...}
[Policy Conversion] User ID: user123
[Policy Conversion] Mapped policy type: motor
[Policy Conversion] Policy provider: holland
[Policy Conversion] Mapping motor policy details
[Policy Conversion] Final policy create object: {...}
```

## 🛠 **How to Use the Logs**

### **1. Development Environment**
All logs are visible in:
- **React Native Debugger**
- **Metro bundler console**
- **Browser console** (for web)
- **Expo Dev Tools**

### **2. Log Filtering**
Use browser/debugger console filters to focus on specific areas:
- `[API Request]` - All API requests
- `[API Response]` - All API responses
- `[Auth Slice]` - Authentication operations
- `[Profile Slice]` - Profile operations
- `[Policy Slice]` - Policy operations
- `[Document Slice]` - Document operations
- `[ProfileForm]` - Profile form interactions
- `[Dashboard]` - Dashboard operations
- `[Policy Conversion]` - Quote to policy conversion

### **3. Common Debugging Scenarios**

#### **Login Issues**
Look for:
```
[Auth Slice] Attempting login for...
[API Request] POST /api/v1/login
[API Response] POST /api/v1/login
[Auth] Login response received:
```

#### **Profile Creation Issues**
Look for:
```
[ProfileForm] Submit button pressed
[ProfileForm] Form validation passed
[Profile Slice] Creating profile with data:
[API Request] POST /api/v1/user-profile
```

#### **Policy Creation Issues**
Look for:
```
[Policy Conversion] Converting quote to policy:
[Policy Slice] Creating policy with data:
[API Request] POST /api/v1/policies
```

#### **Document Upload Issues**
Look for:
```
[Document Slice] Starting document upload
[Document Slice] File: {...}
[API Request] POST /api/v1/documents
```

## 🚨 **Error Tracking**

All errors are logged with:
- **Error source** (API, Redux, Component)
- **Error details** (status, message, data)
- **Context information** (user action, form data, etc.)

**Example Error Log:**
```
[API Error] POST /api/v1/login
[API Error] Status: 401
[API Error] Data: {detail: "Invalid credentials"}
[Auth Slice] Login error: Invalid credentials
```

## 📊 **Performance Monitoring**

Logs include timing information for:
- API request/response cycles
- Redux action dispatching
- Component mounting and data fetching
- Form submission processes

## 🔧 **Production Considerations**

For production builds:
1. Consider using a logging service (e.g., Sentry, LogRocket)
2. Filter out sensitive information (passwords, tokens)
3. Implement log levels (debug, info, warn, error)
4. Add remote logging capabilities

## 📝 **Log Format Standards**

All logs follow this format:
```
[Component/Service] Action description: data
```

Where:
- **Component/Service**: Source of the log (API Request, Auth Slice, etc.)
- **Action description**: What is happening
- **data**: Relevant data (optional, object/string)

This consistent format makes it easy to search, filter, and understand logs across the entire application.
