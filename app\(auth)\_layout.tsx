import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';

export default function AuthLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  return (
    <>
      <StatusBar style={isDarkMode ? "light" : "dark"} />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: colors.background },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen name="login" options={{ gestureEnabled: false }} />
        <Stack.Screen name="register" options={{ gestureEnabled: true }} />
        <Stack.Screen name="verify-otp" options={{ gestureEnabled: false }} />
        <Stack.Screen name="forgot-password" options={{ gestureEnabled: true }} />
      </Stack>
    </>
  );
}
