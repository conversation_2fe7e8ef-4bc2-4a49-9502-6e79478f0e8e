import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService from '@/services/notificationService';

// Define notification types
export type NotificationType = 
  | 'info' 
  | 'warning' 
  | 'error' 
  | 'success' 
  | 'policy' 
  | 'claim' 
  | 'application' 
  | 'document' 
  | 'payment' 
  | 'renewal'
  | 'quotation_sent'
  | 'document_reminder'
  | 'admin_notes'
  | 'document_verified'
  | 'quote_generated'
  | 'quote_accepted'
  | 'policy_bound'
  | 'invoice_generated'
  | 'payment_uploaded'
  | 'payment_verified'
  | 'policy_issued'
  | 'policy_dispatched'
  | 'renewal_reminder';

// Define notification interface
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt: string;
  actionRoute?: string;
  actionLabel?: string;
  actionData?: any;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  expiresAt?: string;
  userId?: string;
}

// Define notification preferences
export interface NotificationPreferences {
  enablePush: boolean;
  enableEmail: boolean;
  enableSMS: boolean;
  categories: {
    policy: boolean;
    claim: boolean;
    payment: boolean;
    document: boolean;
    application: boolean;
    renewal: boolean;
    system: boolean;
  };
}

// Define notification state
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  preferences: NotificationPreferences;
}

// Default notification preferences
const defaultPreferences: NotificationPreferences = {
  enablePush: true,
  enableEmail: true,
  enableSMS: false,
  categories: {
    policy: true,
    claim: true,
    payment: true,
    document: true,
    application: true,
    renewal: true,
    system: true,
  },
};

// Initial state
const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  preferences: defaultPreferences,
};

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notification/fetchNotifications',
  async (_, { rejectWithValue }) => {
    try {
      console.log('[NotificationSlice] Fetching notifications');
      const notifications = await notificationService.getNotifications();
      
      // Load preferences from AsyncStorage
      try {
        const preferencesJson = await AsyncStorage.getItem('notificationPreferences');
        const preferences = preferencesJson ? JSON.parse(preferencesJson) : defaultPreferences;
        return { notifications, preferences };
      } catch (error) {
        console.error('[NotificationSlice] Error loading preferences:', error);
        return { notifications, preferences: defaultPreferences };
      }
    } catch (error) {
      console.error('[NotificationSlice] Error fetching notifications:', error);
      return rejectWithValue('Failed to fetch notifications');
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notification/markAsRead',
  async (id: string, { rejectWithValue }) => {
    try {
      console.log('[NotificationSlice] Marking notification as read:', id);
      await notificationService.markAsRead(id);
      return id;
    } catch (error) {
      console.error('[NotificationSlice] Error marking notification as read:', error);
      return rejectWithValue('Failed to mark notification as read');
    }
  }
);

export const markAllAsRead = createAsyncThunk(
  'notification/markAllAsRead',
  async (_, { getState, rejectWithValue }) => {
    try {
      console.log('[NotificationSlice] Marking all notifications as read');
      const state = getState() as { notification: NotificationState };
      const unreadIds = state.notification.notifications
        .filter(n => !n.read)
        .map(n => n.id);
      
      await Promise.all(unreadIds.map(id => notificationService.markAsRead(id)));
      return unreadIds;
    } catch (error) {
      console.error('[NotificationSlice] Error marking all notifications as read:', error);
      return rejectWithValue('Failed to mark all notifications as read');
    }
  }
);

export const deleteNotification = createAsyncThunk(
  'notification/deleteNotification',
  async (id: string, { rejectWithValue }) => {
    try {
      console.log('[NotificationSlice] Deleting notification:', id);
      await notificationService.deleteNotification(id);
      return id;
    } catch (error) {
      console.error('[NotificationSlice] Error deleting notification:', error);
      return rejectWithValue('Failed to delete notification');
    }
  }
);

export const addNotification = createAsyncThunk(
  'notification/addNotification',
  async (notification: Omit<Notification, 'id' | 'createdAt'>, { getState, rejectWithValue }) => {
    try {
      console.log('[NotificationSlice] Adding notification:', notification.title);
      
      // Create new notification
      const newNotification: Notification = {
        ...notification,
        id: `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        createdAt: new Date().toISOString(),
      };

      // Get current preferences
      const state = getState() as { notification: NotificationState };
      const { preferences } = state.notification;

      // Send local notification if push is enabled
      if (preferences.enablePush) {
        await notificationService.sendLocalNotification(
          notification.title,
          notification.message,
          notification.actionData
        );
      }

      return newNotification;
    } catch (error) {
      console.error('[NotificationSlice] Error adding notification:', error);
      return rejectWithValue('Failed to add notification');
    }
  }
);

export const updatePreferences = createAsyncThunk(
  'notification/updatePreferences',
  async (preferences: Partial<NotificationPreferences>, { getState, rejectWithValue }) => {
    try {
      console.log('[NotificationSlice] Updating preferences');
      const state = getState() as { notification: NotificationState };
      const updatedPreferences = { ...state.notification.preferences, ...preferences };
      
      await AsyncStorage.setItem('notificationPreferences', JSON.stringify(updatedPreferences));
      return updatedPreferences;
    } catch (error) {
      console.error('[NotificationSlice] Error updating preferences:', error);
      return rejectWithValue('Failed to update preferences');
    }
  }
);

// Create the slice
const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch notifications
      .addCase(fetchNotifications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.isLoading = false;
        state.notifications = action.payload.notifications;
        state.preferences = action.payload.preferences;
        state.unreadCount = action.payload.notifications.filter(n => !n.read).length;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Mark as read
      .addCase(markAsRead.fulfilled, (state, action) => {
        const notification = state.notifications.find(n => n.id === action.payload);
        if (notification && !notification.read) {
          notification.read = true;
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      })
      
      // Mark all as read
      .addCase(markAllAsRead.fulfilled, (state, action) => {
        action.payload.forEach(id => {
          const notification = state.notifications.find(n => n.id === id);
          if (notification) {
            notification.read = true;
          }
        });
        state.unreadCount = 0;
      })
      
      // Delete notification
      .addCase(deleteNotification.fulfilled, (state, action) => {
        const index = state.notifications.findIndex(n => n.id === action.payload);
        if (index !== -1) {
          const wasUnread = !state.notifications[index].read;
          state.notifications.splice(index, 1);
          if (wasUnread) {
            state.unreadCount = Math.max(0, state.unreadCount - 1);
          }
        }
      })
      
      // Add notification
      .addCase(addNotification.fulfilled, (state, action) => {
        state.notifications.unshift(action.payload);
        if (!action.payload.read) {
          state.unreadCount += 1;
        }
      })
      
      // Update preferences
      .addCase(updatePreferences.fulfilled, (state, action) => {
        state.preferences = action.payload;
      });
  },
});

export const { clearError, clearAllNotifications } = notificationSlice.actions;
export default notificationSlice.reducer;
