import React, { useEffect } from 'react';
import { Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  withDelay,
  Easing,
  cancelAnimation,
} from 'react-native-reanimated';

interface WavingHandProps {
  size?: number;
  delay?: number;
  duration?: number;
  style?: any;
}

const WavingHand: React.FC<WavingHandProps> = ({
  size = 24,
  delay = 500,
  duration = 500,
  style,
}) => {
  // Rotation animation value
  const rotation = useSharedValue(0);

  // Start the waving animation when component mounts
  useEffect(() => {
    // Start with a delay, then do a sequence of rotations
    rotation.value = withDelay(
      delay,
      withRepeat(
        withSequence(
          // Wave to the right
          withTiming(-0.3, {
            duration: duration * 0.5,
            easing: Easing.inOut(Easing.quad),
          }),
          // Wave to the left
          withTiming(0.3, {
            duration: duration,
            easing: Easing.inOut(Easing.quad),
          }),
          // Back to the right
          withTiming(-0.2, {
            duration: duration * 0.5,
            easing: Easing.inOut(Easing.quad),
          }),
          // Return to center
          withTiming(0, {
            duration: duration * 0.5,
            easing: Easing.inOut(Easing.quad),
          })
        ),
        2, // Repeat the sequence twice
        false // Don't reverse the animation
      )
    );

    // Clean up animation when component unmounts
    return () => {
      cancelAnimation(rotation);
    };
  }, [rotation, delay, duration]);

  // Create animated style for the hand
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { rotate: `${rotation.value}rad` },
        { translateX: rotation.value * 5 }, // Add a slight horizontal movement
      ],
    };
  });

  return (
    <Animated.Text
      style={[
        {
          fontSize: size,
        },
        animatedStyle,
        style,
      ]}
    >
      👋
    </Animated.Text>
  );
};

export default WavingHand;
