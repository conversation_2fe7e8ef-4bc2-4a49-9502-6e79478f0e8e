import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ClaimInput, ClaimType, CLAIM_TYPE_DISPLAY_NAMES } from '@/types/claim.types';
import useClaimStore from '@/store/claimStore';
import ClaimForm from '@/components/claims/ClaimForm';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';

export default function ClaimFormScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const claimType = params.type as ClaimType;
  
  // Get claim store methods
  const { createClaim, isLoading } = useClaimStore();
  
  // State
  const [policies, setPolicies] = useState<Array<{ id: string; policyNumber: string; type: string }>>([]);
  const [isPoliciesLoading, setIsPoliciesLoading] = useState<boolean>(true);
  
  // Fetch policies on mount
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setPolicies([
        { id: '1', policyNumber: 'POL-2023-0001', type: 'Motor Insurance' },
        { id: '2', policyNumber: 'POL-2023-0002', type: 'Household Insurance' },
        { id: '3', policyNumber: 'POL-2023-0003', type: 'Life Insurance' },
      ]);
      setIsPoliciesLoading(false);
    }, 1000);
  }, []);
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.sm,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    content: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });
  
  // Handle form submission
  const handleSubmit = async (claimInput: ClaimInput) => {
    try {
      const newClaim = await createClaim(claimInput);
      
      // Show success toast
      showToast(
        'success',
        'Claim Created',
        'Your claim has been created successfully',
        { visibilityTime: 3000 }
      );
      
      // Navigate to claim detail page
      router.push({
        pathname: '/claims/[id]',
        params: { id: newClaim.id }
      });
    } catch (error) {
      console.error('Error creating claim:', error);
      showToast(
        'error',
        'Error',
        'Failed to create claim. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {CLAIM_TYPE_DISPLAY_NAMES[claimType] || 'New Claim'}
        </Text>
      </View>
      
      <View style={styles.content}>
        {isPoliciesLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
          </View>
        ) : (
          <ClaimForm
            onSubmit={handleSubmit}
            policies={policies}
            initialType={claimType}
          />
        )}
      </View>
      
      <BottomNavBar currentRoute="claims" />
    </SafeAreaView>
  );
}
