import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Bell } from 'lucide-react-native';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import useNotificationStore from '@/store/notificationStore';

interface NotificationBadgeProps {
  size?: 'small' | 'medium' | 'large';
  showCount?: boolean;
}

export default function NotificationBadge({ 
  size = 'medium', 
  showCount = true 
}: NotificationBadgeProps) {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { unreadCount, fetchNotifications } = useNotificationStore();

  // Load notifications on mount
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Get icon size based on size prop
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'large':
        return 28;
      case 'medium':
      default:
        return 22;
    }
  };

  // Get badge size based on size prop
  const getBadgeSize = () => {
    switch (size) {
      case 'small':
        return 14;
      case 'large':
        return 22;
      case 'medium':
      default:
        return 18;
    }
  };

  // Get font size based on size prop
  const getFontSize = () => {
    switch (size) {
      case 'small':
        return typography.sizes.xs;
      case 'large':
        return typography.sizes.sm;
      case 'medium':
      default:
        return typography.sizes.xs;
    }
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      position: 'relative',
    },
    badge: {
      position: 'absolute',
      top: -5,
      right: -5,
      backgroundColor: colors.error[500],
      borderRadius: 50,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
      borderWidth: 1,
      borderColor: colors.background,
    },
    badgeText: {
      color: colors.white,
      fontFamily: typography.fonts.medium,
      textAlign: 'center',
    },
  });

  // Calculate badge size
  const badgeSize = getBadgeSize();
  const badgeStyle = {
    ...styles.badge,
    width: showCount ? (unreadCount > 9 ? badgeSize + 6 : badgeSize) : badgeSize / 1.5,
    height: badgeSize,
    minWidth: badgeSize,
  };

  // Handle press
  const handlePress = () => {
    router.push('/notifications');
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress} activeOpacity={0.7}>
      <Bell size={getIconSize()} color={colors.text} />
      {unreadCount > 0 && (
        <View style={badgeStyle}>
          {showCount ? (
            <Text style={[styles.badgeText, { fontSize: getFontSize() }]}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          ) : null}
        </View>
      )}
    </TouchableOpacity>
  );
}
