import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Bell } from 'lucide-react-native';
import { RootState, AppDispatch } from '@/store/store';
import { fetchNotifications } from '@/store/notificationSlice';
import NotificationCenter from './NotificationCenter';
import Animated, { FadeIn } from 'react-native-reanimated';

interface NotificationBellProps {
  color?: string;
  size?: number;
}

const NotificationBell: React.FC<NotificationBellProps> = ({
  color,
  size = 24
}) => {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const [isNotificationCenterVisible, setIsNotificationCenterVisible] = useState(false);
  const dispatch = useDispatch<AppDispatch>();

  // Get unread count from Redux notification store
  const { unreadCount } = useSelector((state: RootState) => state.notification);

  // Fetch notifications on mount
  useEffect(() => {
    dispatch(fetchNotifications());
  }, [dispatch]);

  // Toggle notification center
  const toggleNotificationCenter = () => {
    setIsNotificationCenterVisible(!isNotificationCenterVisible);
  };

  // Create styles
  const styles = StyleSheet.create({
    container: {
      position: 'relative',
    },
    badge: {
      position: 'absolute',
      top: -5,
      right: -5,
      backgroundColor: colors.error[500],
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1,
    },
    badgeText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xs,
      color: colors.white,
      paddingHorizontal: 4,
    },
  });

  return (
    <>
      <TouchableOpacity
        style={styles.container}
        onPress={toggleNotificationCenter}
        accessibilityLabel="Notifications"
        accessibilityHint="Opens the notification center"
      >
        <Bell size={size} color={color || colors.text} />
        {unreadCount > 0 && (
          <Animated.View
            style={styles.badge}
            entering={FadeIn}
          >
            <Text style={styles.badgeText}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </Animated.View>
        )}
      </TouchableOpacity>

      <NotificationCenter
        isVisible={isNotificationCenterVisible}
        onClose={() => setIsNotificationCenterVisible(false)}
      />
    </>
  );
};

export default NotificationBell;
