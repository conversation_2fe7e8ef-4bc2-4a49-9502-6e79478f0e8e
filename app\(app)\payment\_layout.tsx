import React from 'react';
import { Stack } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function PaymentLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  console.log('[PaymentLayout] Rendering payment layout');

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: colors.background },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="[id]" />
      <Stack.Screen name="new" />
      <Stack.Screen name="history" />
      <Stack.Screen name="verify" />
      <Stack.Screen name="upload-proof" />
    </Stack>
  );
}
