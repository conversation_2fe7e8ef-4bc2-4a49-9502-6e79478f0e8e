import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Check } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';

interface Step {
  id: string;
  title: string;
}

interface FormStepIndicatorProps {
  steps: Step[];
  currentStep: number;
  completedSteps: number[];
}

const FormStepIndicator: React.FC<FormStepIndicatorProps> = ({
  steps,
  currentStep,
  completedSteps,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    stepsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    stepContainer: {
      alignItems: 'center',
      flex: 1,
    },
    stepCircle: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    stepNumber: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
    },
    stepTitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      textAlign: 'center',
      maxWidth: 80,
    },
    line: {
      height: 2,
      flex: 1,
      marginHorizontal: spacing.xs,
    },
    lineContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'absolute',
      top: 16,
      left: 0,
      right: 0,
      zIndex: -1,
      paddingHorizontal: 16,
    },
  });

  // Render a single step
  const renderStep = (step: Step, index: number) => {
    const isCompleted = completedSteps.includes(index);
    const isCurrent = index === currentStep;
    
    // Determine circle style based on step status
    const circleStyle = {
      backgroundColor: isCompleted 
        ? colors.success[500] 
        : isCurrent 
          ? colors.primary[500] 
          : colors.neutral[200],
    };
    
    // Determine text color based on step status
    const textColor = {
      color: isCompleted 
        ? colors.success[500] 
        : isCurrent 
          ? colors.primary[500] 
          : colors.textSecondary,
    };

    return (
      <View key={step.id} style={styles.stepContainer}>
        <View style={[styles.stepCircle, circleStyle]}>
          {isCompleted ? (
            <Check size={16} color={colors.white} />
          ) : (
            <Text style={[styles.stepNumber, { color: colors.white }]}>
              {index + 1}
            </Text>
          )}
        </View>
        <Text style={[styles.stepTitle, textColor]} numberOfLines={1}>
          {step.title}
        </Text>
      </View>
    );
  };

  // Render the connecting lines between steps
  const renderLines = () => {
    return (
      <View style={styles.lineContainer}>
        {steps.map((_, index) => {
          if (index === steps.length - 1) return null;
          
          const isCompleted = completedSteps.includes(index);
          
          return (
            <View 
              key={`line-${index}`} 
              style={[
                styles.line, 
                { 
                  backgroundColor: isCompleted 
                    ? colors.success[500] 
                    : colors.neutral[200] 
                }
              ]} 
            />
          );
        })}
      </View>
    );
  };

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeIn.duration(500)}
    >
      {renderLines()}
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => renderStep(step, index))}
      </View>
    </Animated.View>
  );
};

export default FormStepIndicator;
