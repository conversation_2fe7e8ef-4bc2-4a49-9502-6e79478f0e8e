import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TextInput, 
  TouchableOpacity,
  ActivityIndicator,
  Alert
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { 
  ClaimInput, 
  ClaimType, 
  CLAIM_TYPE_DISPLAY_NAMES, 
  REQUIRED_DOCUMENTS_BY_CLAIM_TYPE,
  ClaimDocument
} from '@/types/claim.types';
import { 
  Calendar, 
  DollarSign, 
  FileText, 
  ChevronDown, 
  ChevronUp, 
  Camera,
  Upload,
  AlertCircle
} from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { showToast } from '@/utils/toast';
import { useDocumentUpload } from '@/context/DocumentUploadContext';
import DocumentUploader from '@/components/documents/DocumentUploader';
import DocumentItem from '@/components/documents/DocumentItem';
import { Document } from '@/components/documents/types';

interface EnhancedClaimFormProps {
  onSubmit: (claimInput: ClaimInput, documents: Document[]) => Promise<void>;
  policies: Array<{ id: string; policyNumber: string; type: string }>;
  initialType?: ClaimType;
}

const EnhancedClaimForm: React.FC<EnhancedClaimFormProps> = ({
  onSubmit,
  policies,
  initialType
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { uploadDocument, pendingDocuments, verifiedDocuments } = useDocumentUpload();

  // Form state
  const [policyId, setPolicyId] = useState<string>('');
  const [claimType, setClaimType] = useState<ClaimType>(initialType || 'motor_accident');
  const [incidentDate, setIncidentDate] = useState<Date>(new Date());
  const [description, setDescription] = useState<string>('');
  const [claimAmount, setClaimAmount] = useState<string>('');
  const [currency, setCurrency] = useState<string>('BWP');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // UI state
  const [showPolicyPicker, setShowPolicyPicker] = useState<boolean>(false);
  const [showTypePicker, setShowTypePicker] = useState<boolean>(false);
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [showCurrencyPicker, setShowCurrencyPicker] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<string>('details');
  
  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get required documents for the selected claim type
  const requiredDocuments = REQUIRED_DOCUMENTS_BY_CLAIM_TYPE[claimType] || [];

  // Update documents when pendingDocuments changes
  useEffect(() => {
    setDocuments([...pendingDocuments, ...verifiedDocuments]);
  }, [pendingDocuments, verifiedDocuments]);

  // Format date for display
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  // Handle date picker change
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setIncidentDate(selectedDate);
      validateField('incidentDate', selectedDate);
    }
  };

  // Validate a single field
  const validateField = (field: string, value: any): boolean => {
    let isValid = true;
    const newErrors = { ...errors };
    
    switch (field) {
      case 'policyId':
        if (!value) {
          newErrors.policyId = 'Please select a policy';
          isValid = false;
        } else {
          delete newErrors.policyId;
        }
        break;
        
      case 'claimType':
        if (!value) {
          newErrors.claimType = 'Please select a claim type';
          isValid = false;
        } else {
          delete newErrors.claimType;
        }
        break;
        
      case 'incidentDate':
        if (!value) {
          newErrors.incidentDate = 'Please select an incident date';
          isValid = false;
        } else if (value > new Date()) {
          newErrors.incidentDate = 'Incident date cannot be in the future';
          isValid = false;
        } else {
          delete newErrors.incidentDate;
        }
        break;
        
      case 'description':
        if (!value || value.trim().length < 10) {
          newErrors.description = 'Please provide a detailed description (at least 10 characters)';
          isValid = false;
        } else {
          delete newErrors.description;
        }
        break;
        
      case 'claimAmount':
        if (!value || isNaN(Number(value)) || Number(value) <= 0) {
          newErrors.claimAmount = 'Please enter a valid claim amount';
          isValid = false;
        } else {
          delete newErrors.claimAmount;
        }
        break;
        
      case 'documents':
        const requiredDocTypes = requiredDocuments
          .filter(doc => doc.required)
          .map(doc => doc.type);
          
        const uploadedDocTypes = documents.map(doc => doc.type);
        
        const missingRequiredDocs = requiredDocTypes.filter(
          type => !uploadedDocTypes.includes(type.toString())
        );
        
        if (missingRequiredDocs.length > 0) {
          newErrors.documents = `Missing required documents: ${missingRequiredDocs.join(', ')}`;
          isValid = false;
        } else {
          delete newErrors.documents;
        }
        break;
    }
    
    setErrors(newErrors);
    return isValid;
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const validations = [
      validateField('policyId', policyId),
      validateField('claimType', claimType),
      validateField('incidentDate', incidentDate),
      validateField('description', description),
      validateField('claimAmount', claimAmount),
      validateField('documents', documents)
    ];
    
    return validations.every(isValid => isValid);
  };

  // Handle document upload
  const handleDocumentUploaded = (document: Document) => {
    setDocuments(prev => [...prev, document]);
    validateField('documents', [...documents, document]);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      // Show error toast
      showToast(
        'error',
        'Validation Error',
        'Please fix the errors in the form',
        { visibilityTime: 3000 }
      );
      return;
    }
    
    setIsSubmitting(true);

    try {
      const selectedPolicy = policies.find(p => p.id === policyId);
      
      if (!selectedPolicy) {
        throw new Error('Selected policy not found');
      }

      const claimInput: ClaimInput = {
        policyId,
        policyNumber: selectedPolicy.policyNumber,
        type: claimType,
        incidentDate: incidentDate.toISOString().split('T')[0],
        description,
        claimAmount: Number(claimAmount),
        currency
      };

      await onSubmit(claimInput, documents);
    } catch (error) {
      console.error('Error submitting claim:', error);
      showToast(
        'error',
        'Submission Error',
        'Failed to submit claim. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.formContainer}>
        <Text style={styles.sectionTitle}>New Claim</Text>

        {/* Navigation tabs */}
        <View style={styles.tabContainer}>
          <TouchableOpacity 
            style={[
              styles.tab, 
              activeSection === 'details' && styles.activeTab
            ]}
            onPress={() => setActiveSection('details')}
          >
            <Text style={[
              styles.tabText,
              activeSection === 'details' && styles.activeTabText
            ]}>
              Claim Details
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[
              styles.tab, 
              activeSection === 'documents' && styles.activeTab
            ]}
            onPress={() => setActiveSection('documents')}
          >
            <Text style={[
              styles.tabText,
              activeSection === 'documents' && styles.activeTabText
            ]}>
              Documents
            </Text>
            {errors.documents && (
              <View style={styles.tabErrorIndicator}>
                <AlertCircle size={16} color={colors.white} />
              </View>
            )}
          </TouchableOpacity>
        </View>

        {activeSection === 'details' ? (
          // Claim details section
          <>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Select Policy</Text>
              <View style={[
                styles.pickerContainer,
                errors.policyId ? styles.inputError : {}
              ]}>
                <TouchableOpacity
                  style={styles.pickerTrigger}
                  onPress={() => setShowPolicyPicker(!showPolicyPicker)}
                >
                  <Text style={styles.pickerTriggerText}>
                    {policyId ? policies.find(p => p.id === policyId)?.policyNumber : 'Select a policy'}
                  </Text>
                  {showPolicyPicker ? (
                    <ChevronUp size={20} color={colors.text} />
                  ) : (
                    <ChevronDown size={20} color={colors.text} />
                  )}
                </TouchableOpacity>
              </View>
              {errors.policyId && <Text style={styles.errorText}>{errors.policyId}</Text>}
              
              {showPolicyPicker && (
                <View style={styles.pickerDropdown}>
                  <ScrollView style={styles.pickerScrollView}>
                    {policies.map(policy => (
                      <TouchableOpacity
                        key={policy.id}
                        style={styles.pickerItem}
                        onPress={() => {
                          setPolicyId(policy.id);
                          setShowPolicyPicker(false);
                          validateField('policyId', policy.id);
                        }}
                      >
                        <Text style={styles.pickerItemText}>
                          {policy.policyNumber} - {policy.type}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Claim Type</Text>
              <View style={[
                styles.pickerContainer,
                errors.claimType ? styles.inputError : {}
              ]}>
                <TouchableOpacity
                  style={styles.pickerTrigger}
                  onPress={() => setShowTypePicker(!showTypePicker)}
                >
                  <Text style={styles.pickerTriggerText}>
                    {CLAIM_TYPE_DISPLAY_NAMES[claimType]}
                  </Text>
                  {showTypePicker ? (
                    <ChevronUp size={20} color={colors.text} />
                  ) : (
                    <ChevronDown size={20} color={colors.text} />
                  )}
                </TouchableOpacity>
              </View>
              {errors.claimType && <Text style={styles.errorText}>{errors.claimType}</Text>}
              
              {showTypePicker && (
                <View style={styles.pickerDropdown}>
                  <ScrollView style={styles.pickerScrollView}>
                    {Object.entries(CLAIM_TYPE_DISPLAY_NAMES).map(([type, name]) => (
                      <TouchableOpacity
                        key={type}
                        style={styles.pickerItem}
                        onPress={() => {
                          setClaimType(type as ClaimType);
                          setShowTypePicker(false);
                          validateField('claimType', type);
                        }}
                      >
                        <Text style={styles.pickerItemText}>{name}</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Incident Date</Text>
              <TouchableOpacity
                style={[
                  styles.datePickerTrigger,
                  errors.incidentDate ? styles.inputError : {}
                ]}
                onPress={() => setShowDatePicker(true)}
              >
                <Calendar size={20} color={colors.text} />
                <Text style={styles.datePickerText}>
                  {formatDate(incidentDate)}
                </Text>
              </TouchableOpacity>
              {errors.incidentDate && <Text style={styles.errorText}>{errors.incidentDate}</Text>}
              
              {showDatePicker && (
                <DateTimePicker
                  value={incidentDate}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                  maximumDate={new Date()}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[
                  styles.textArea,
                  errors.description ? styles.inputError : {}
                ]}
                placeholder="Provide a detailed description of the incident..."
                placeholderTextColor={colors.textSecondary}
                value={description}
                onChangeText={(text) => {
                  setDescription(text);
                  validateField('description', text);
                }}
                multiline
                numberOfLines={5}
                textAlignVertical="top"
              />
              {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Claim Amount</Text>
              <View style={[
                styles.amountContainer,
                errors.claimAmount ? styles.inputError : {}
              ]}>
                <DollarSign size={20} color={colors.text} />
                <TextInput
                  style={styles.amountInput}
                  placeholder="0.00"
                  placeholderTextColor={colors.textSecondary}
                  value={claimAmount}
                  onChangeText={(text) => {
                    setClaimAmount(text.replace(/[^0-9.]/g, ''));
                    validateField('claimAmount', text);
                  }}
                  keyboardType="numeric"
                />
                <TouchableOpacity
                  style={styles.currencySelector}
                  onPress={() => setShowCurrencyPicker(!showCurrencyPicker)}
                >
                  <Text style={styles.currencyText}>{currency}</Text>
                  {showCurrencyPicker ? (
                    <ChevronUp size={16} color={colors.text} />
                  ) : (
                    <ChevronDown size={16} color={colors.text} />
                  )}
                </TouchableOpacity>
              </View>
              {errors.claimAmount && <Text style={styles.errorText}>{errors.claimAmount}</Text>}
              
              {showCurrencyPicker && (
                <View style={styles.currencyDropdown}>
                  {['BWP', 'USD', 'ZAR', 'EUR', 'GBP'].map(curr => (
                    <TouchableOpacity
                      key={curr}
                      style={styles.currencyItem}
                      onPress={() => {
                        setCurrency(curr);
                        setShowCurrencyPicker(false);
                      }}
                    >
                      <Text style={styles.currencyItemText}>{curr}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </>
        ) : (
          // Documents section
          <View style={styles.documentsSection}>
            <Text style={styles.documentsSectionTitle}>Required Documents</Text>
            <Text style={styles.documentsSectionSubtitle}>
              Please upload the following documents to support your claim
            </Text>
            
            {errors.documents && (
              <View style={styles.documentsError}>
                <AlertCircle size={20} color={colors.error[500]} />
                <Text style={styles.documentsErrorText}>{errors.documents}</Text>
              </View>
            )}
            
            <View style={styles.requiredDocumentsList}>
              {requiredDocuments.map((doc, index) => (
                <View key={index} style={styles.requiredDocumentItem}>
                  <View style={styles.requiredDocumentInfo}>
                    <FileText size={20} color={colors.text} />
                    <Text style={styles.requiredDocumentName}>
                      {doc.name} {doc.required && <Text style={styles.requiredTag}>*Required</Text>}
                    </Text>
                  </View>
                  
                  {/* Check if this document type has been uploaded */}
                  {documents.some(d => d.type.toString() === doc.type) ? (
                    <View style={styles.documentUploadedBadge}>
                      <Text style={styles.documentUploadedText}>Uploaded</Text>
                    </View>
                  ) : (
                    <TouchableOpacity 
                      style={styles.uploadButton}
                      onPress={() => {
                        // Navigate to document upload with preselected type
                        // This would typically navigate to the document upload screen
                        // For now, we'll just show a message
                        Alert.alert(
                          "Upload Document",
                          `Please upload ${doc.name}`,
                          [
                            { text: "OK" }
                          ]
                        );
                      }}
                    >
                      <Upload size={16} color={colors.white} />
                      <Text style={styles.uploadButtonText}>Upload</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
            
            <View style={styles.documentUploader}>
              <DocumentUploader onDocumentUploaded={handleDocumentUploaded} />
            </View>
            
            {documents.length > 0 && (
              <View style={styles.uploadedDocuments}>
                <Text style={styles.uploadedDocumentsTitle}>Uploaded Documents</Text>
                {documents.map((doc, index) => (
                  <DocumentItem 
                    key={index} 
                    document={doc}
                    onView={() => {
                      // View document logic
                    }}
                    onDelete={() => {
                      // Delete document logic
                      setDocuments(prev => prev.filter(d => d.id !== doc.id));
                    }}
                  />
                ))}
              </View>
            )}
          </View>
        )}
        
        <TouchableOpacity
          style={[
            styles.submitButton,
            isSubmitting && { opacity: 0.7 }
          ]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator color={colors.white} />
          ) : (
            <Text style={styles.submitButtonText}>Submit Claim</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    position: 'relative',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#6200ee',
  },
  tabText: {
    fontSize: 14,
    color: '#757575',
  },
  activeTabText: {
    color: '#6200ee',
    fontWeight: 'bold',
  },
  tabErrorIndicator: {
    position: 'absolute',
    top: 0,
    right: 20,
    backgroundColor: '#f44336',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  pickerTrigger: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  pickerTriggerText: {
    fontSize: 16,
  },
  pickerDropdown: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#ffffff',
    marginTop: 4,
    maxHeight: 200,
  },
  pickerScrollView: {
    maxHeight: 200,
  },
  pickerItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  pickerItemText: {
    fontSize: 16,
  },
  datePickerTrigger: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  datePickerText: {
    fontSize: 16,
    marginLeft: 8,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    padding: 12,
    fontSize: 16,
    minHeight: 100,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
  },
  amountInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
  },
  currencySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
    borderLeftWidth: 1,
    borderLeftColor: '#e0e0e0',
  },
  currencyText: {
    fontSize: 16,
    marginRight: 4,
  },
  currencyDropdown: {
    position: 'absolute',
    right: 0,
    top: 80,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    backgroundColor: '#ffffff',
    zIndex: 1000,
  },
  currencyItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  currencyItemText: {
    fontSize: 16,
  },
  inputError: {
    borderColor: '#f44336',
  },
  errorText: {
    color: '#f44336',
    fontSize: 12,
    marginTop: 4,
  },
  documentsSection: {
    marginBottom: 20,
  },
  documentsSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  documentsSectionSubtitle: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 16,
  },
  documentsError: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffebee',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  documentsErrorText: {
    color: '#f44336',
    fontSize: 14,
    marginLeft: 8,
  },
  requiredDocumentsList: {
    marginBottom: 20,
  },
  requiredDocumentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  requiredDocumentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  requiredDocumentName: {
    fontSize: 16,
    marginLeft: 8,
  },
  requiredTag: {
    color: '#f44336',
    fontSize: 12,
  },
  documentUploadedBadge: {
    backgroundColor: '#e8f5e9',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  documentUploadedText: {
    color: '#4caf50',
    fontSize: 12,
    fontWeight: 'bold',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6200ee',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  uploadButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  documentUploader: {
    marginBottom: 20,
  },
  uploadedDocuments: {
    marginBottom: 20,
  },
  uploadedDocumentsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  submitButton: {
    backgroundColor: '#6200ee',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default EnhancedClaimForm;
