import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { BaseToast, ErrorToast, InfoToast, ToastConfig } from 'react-native-toast-message';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { CheckCircle, AlertCircle, Info, X } from 'lucide-react-native';

/**
 * Custom toast configuration component
 * Creates enhanced toast notifications with better visual appeal
 */
export const toastConfig: ToastConfig = {
  success: (props) => {
    const { isDarkMode } = useTheme();
    const { colors, spacing, typography, borders } = createTheme(isDarkMode);
    
    return (
      <View 
        style={[
          styles.container, 
          { 
            backgroundColor: isDarkMode ? colors.success[900] : colors.success[50],
            borderLeftColor: colors.success[500],
          }
        ]}
      >
        <View style={styles.iconContainer}>
          <CheckCircle size={24} color={colors.success[500]} />
        </View>
        <View style={styles.textContainer}>
          <Text 
            style={[
              styles.title, 
              { 
                color: isDarkMode ? colors.white : colors.text,
                fontFamily: typography.fonts.medium,
              }
            ]}
          >
            {props.text1}
          </Text>
          {props.text2 && (
            <Text 
              style={[
                styles.message, 
                { 
                  color: isDarkMode ? colors.gray[300] : colors.textSecondary,
                  fontFamily: typography.fonts.regular,
                }
              ]}
            >
              {props.text2}
            </Text>
          )}
        </View>
        <TouchableOpacity 
          style={styles.closeButton} 
          onPress={() => props.onPress?.()}
        >
          <X size={20} color={isDarkMode ? colors.gray[400] : colors.textSecondary} />
        </TouchableOpacity>
      </View>
    );
  },
  
  error: (props) => {
    const { isDarkMode } = useTheme();
    const { colors, spacing, typography, borders } = createTheme(isDarkMode);
    
    return (
      <View 
        style={[
          styles.container, 
          { 
            backgroundColor: isDarkMode ? colors.error[900] : colors.error[50],
            borderLeftColor: colors.error[500],
          }
        ]}
      >
        <View style={styles.iconContainer}>
          <AlertCircle size={24} color={colors.error[500]} />
        </View>
        <View style={styles.textContainer}>
          <Text 
            style={[
              styles.title, 
              { 
                color: isDarkMode ? colors.white : colors.text,
                fontFamily: typography.fonts.medium,
              }
            ]}
          >
            {props.text1}
          </Text>
          {props.text2 && (
            <Text 
              style={[
                styles.message, 
                { 
                  color: isDarkMode ? colors.gray[300] : colors.textSecondary,
                  fontFamily: typography.fonts.regular,
                }
              ]}
            >
              {props.text2}
            </Text>
          )}
        </View>
        <TouchableOpacity 
          style={styles.closeButton} 
          onPress={() => props.onPress?.()}
        >
          <X size={20} color={isDarkMode ? colors.gray[400] : colors.textSecondary} />
        </TouchableOpacity>
      </View>
    );
  },
  
  info: (props) => {
    const { isDarkMode } = useTheme();
    const { colors, spacing, typography, borders } = createTheme(isDarkMode);
    
    return (
      <View 
        style={[
          styles.container, 
          { 
            backgroundColor: isDarkMode ? colors.primary[900] : colors.primary[50],
            borderLeftColor: colors.primary[500],
          }
        ]}
      >
        <View style={styles.iconContainer}>
          <Info size={24} color={colors.primary[500]} />
        </View>
        <View style={styles.textContainer}>
          <Text 
            style={[
              styles.title, 
              { 
                color: isDarkMode ? colors.white : colors.text,
                fontFamily: typography.fonts.medium,
              }
            ]}
          >
            {props.text1}
          </Text>
          {props.text2 && (
            <Text 
              style={[
                styles.message, 
                { 
                  color: isDarkMode ? colors.gray[300] : colors.textSecondary,
                  fontFamily: typography.fonts.regular,
                }
              ]}
            >
              {props.text2}
            </Text>
          )}
        </View>
        <TouchableOpacity 
          style={styles.closeButton} 
          onPress={() => props.onPress?.()}
        >
          <X size={20} color={isDarkMode ? colors.gray[400] : colors.textSecondary} />
        </TouchableOpacity>
      </View>
    );
  },
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    minHeight: 70,
    maxHeight: 120,
    borderRadius: 8,
    borderLeftWidth: 4,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginTop: 10,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
  },
  closeButton: {
    padding: 4,
  },
});

export default toastConfig;
