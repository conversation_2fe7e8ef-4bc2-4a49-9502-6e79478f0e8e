import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { RefreshCw, Bell, Calendar, AlertTriangle, X } from 'lucide-react-native';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { router } from 'expo-router';
import { Policy } from '@/store/policyStore';
import { formatCurrency } from '@/utils/quoteCalculations';

interface RenewalNotificationProps {
  policy: Policy;
  daysUntilRenewal: number;
  onDismiss: () => void;
}

const RenewalNotification: React.FC<RenewalNotificationProps> = ({
  policy,
  daysUntilRenewal,
  onDismiss,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const isRenewalCritical = daysUntilRenewal <= 7;
  const isRenewalOverdue = daysUntilRenewal < 0;

  const handleRenewalPress = () => {
    router.push(`/policies/renew/${policy.id}`);
    onDismiss();
  };

  const styles = StyleSheet.create({
    container: {
      marginHorizontal: spacing.md,
      marginBottom: spacing.md,
      borderRadius: borders.radius.lg,
      overflow: 'hidden',
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    content: {
      backgroundColor: isRenewalOverdue ? colors.error[50] :
                      isRenewalCritical ? colors.warning[50] :
                      colors.card,
      borderLeftWidth: 4,
      borderLeftColor: isRenewalOverdue ? colors.error[500] :
                      isRenewalCritical ? colors.warning[500] :
                      colors.primary[500],
      padding: spacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    icon: {
      marginRight: spacing.sm,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: isRenewalOverdue ? colors.error[700] :
            isRenewalCritical ? colors.warning[700] :
            colors.text,
    },
    closeButton: {
      padding: spacing.xs,
    },
    message: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    label: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    value: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    buttonContainer: {
      marginTop: spacing.md,
    },
    renewButton: {
      backgroundColor: isRenewalOverdue ? colors.error[500] :
                      isRenewalCritical ? colors.warning[500] :
                      colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      alignItems: 'center',
    },
    renewButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
    },
  });

  return (
    <Animated.View
      style={styles.container}
      entering={SlideInRight.duration(500).springify()}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            {isRenewalOverdue ? (
              <AlertTriangle
                size={20}
                color={colors.error[500]}
                style={styles.icon}
              />
            ) : isRenewalCritical ? (
              <AlertTriangle
                size={20}
                color={colors.warning[500]}
                style={styles.icon}
              />
            ) : (
              <Bell
                size={20}
                color={colors.primary[500]}
                style={styles.icon}
              />
            )}
            <Text style={styles.title}>
              {isRenewalOverdue
                ? 'Policy Renewal Overdue'
                : isRenewalCritical
                ? 'Urgent: Policy Renewal Due Soon'
                : 'Policy Renewal Reminder'}
            </Text>
          </View>
          <TouchableOpacity style={styles.closeButton} onPress={onDismiss}>
            <X size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <Text style={styles.message}>
          {isRenewalOverdue
            ? `Your ${policy.type} insurance policy has expired. Renew now to restore coverage.`
            : isRenewalCritical
            ? `Your ${policy.type} insurance policy will expire in ${daysUntilRenewal} days. Immediate action required.`
            : `Your ${policy.type} insurance policy is due for renewal in ${daysUntilRenewal} days.`}
        </Text>

        <View style={styles.infoRow}>
          <Text style={styles.label}>Policy Number:</Text>
          <Text style={styles.value}>{policy.policyNumber}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.label}>Expiry Date:</Text>
          <Text style={styles.value}>{policy.endDate}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.label}>Current Premium:</Text>
          <Text style={styles.value}>{formatCurrency(policy.premium, policy.currency)}</Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.renewButton} onPress={handleRenewalPress}>
            <Text style={styles.renewButtonText}>
              {isRenewalOverdue
                ? 'Renew Now (Urgent)'
                : isRenewalCritical
                ? 'Renew Now'
                : 'View Renewal Options'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

export default RenewalNotification;
