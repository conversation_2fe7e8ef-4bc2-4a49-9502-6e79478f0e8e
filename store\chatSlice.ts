import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  ChatSessionPublic,
  ChatMessagePublic,
  ChatSessionCreate,
  ChatMessageCreate
} from '@/types/backend';
import { chatService } from '@/services/chatService';

// State interface
export interface ChatState {
  sessions: ChatSessionPublic[];
  currentSession: ChatSessionPublic | null;
  isLoading: boolean;
  isSending: boolean;
  error: string | null;
  unreadCount: number;
}

// Initial state
const initialState: ChatState = {
  sessions: [],
  currentSession: null,
  isLoading: false,
  isSending: false,
  error: null,
  unreadCount: 0,
};

// Async thunks
export const fetchChatSessions = createAsyncThunk(
  'chat/fetchSessions',
  async (_, { rejectWithValue }) => {
    try {
      console.log('[Chat Slice] Fetching chat sessions');
      const sessions = await chatService.getSessions();
      console.log('[Chat Slice] Chat sessions fetched successfully:', sessions.length);
      return sessions;
    } catch (error: any) {
      console.error('[Chat Slice] Error fetching chat sessions:', error);
      return rejectWithValue(error.message || 'Failed to fetch chat sessions');
    }
  }
);

export const fetchChatSession = createAsyncThunk(
  'chat/fetchSession',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      console.log('[Chat Slice] Fetching chat session:', sessionId);
      const session = await chatService.getSession(sessionId);
      console.log('[Chat Slice] Chat session fetched successfully:', session.id);
      return session;
    } catch (error: any) {
      console.error('[Chat Slice] Error fetching chat session:', error);
      return rejectWithValue(error.message || 'Failed to fetch chat session');
    }
  }
);

export const createChatSession = createAsyncThunk(
  'chat/createSession',
  async (sessionData: ChatSessionCreate, { rejectWithValue }) => {
    try {
      console.log('[Chat Slice] Creating chat session:', sessionData);
      const session = await chatService.createSession(sessionData);
      console.log('[Chat Slice] Chat session created successfully:', session.id);
      return session;
    } catch (error: any) {
      console.error('[Chat Slice] Error creating chat session:', error);
      return rejectWithValue(error.message || 'Failed to create chat session');
    }
  }
);

export const sendChatMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ sessionId, messageData }: { sessionId: string; messageData: ChatMessageCreate }, { rejectWithValue }) => {
    try {
      console.log('[Chat Slice] Sending message to session:', sessionId);
      const message = await chatService.sendMessage(sessionId, messageData);
      console.log('[Chat Slice] Message sent successfully:', message.id);
      return { sessionId, message };
    } catch (error: any) {
      console.error('[Chat Slice] Error sending message:', error);
      return rejectWithValue(error.message || 'Failed to send message');
    }
  }
);

export const markMessageAsRead = createAsyncThunk(
  'chat/markMessageAsRead',
  async (messageId: string, { rejectWithValue }) => {
    try {
      console.log('[Chat Slice] Marking message as read:', messageId);
      await chatService.markMessageAsRead(messageId);
      console.log('[Chat Slice] Message marked as read successfully');
      return messageId;
    } catch (error: any) {
      console.error('[Chat Slice] Error marking message as read:', error);
      return rejectWithValue(error.message || 'Failed to mark message as read');
    }
  }
);

export const createSupportSession = createAsyncThunk(
  'chat/createSupportSession',
  async (title?: string, { rejectWithValue }) => {
    try {
      console.log('[Chat Slice] Creating support session');
      const session = await chatService.createSupportSession(title);
      console.log('[Chat Slice] Support session created successfully:', session.id);
      return session;
    } catch (error: any) {
      console.error('[Chat Slice] Error creating support session:', error);
      return rejectWithValue(error.message || 'Failed to create support session');
    }
  }
);

// Chat slice
const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<ChatSessionPublic | null>) => {
      state.currentSession = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    addMessageToSession: (state, action: PayloadAction<{ sessionId: string; message: ChatMessagePublic }>) => {
      const { sessionId, message } = action.payload;
      
      // Update current session if it matches
      if (state.currentSession && state.currentSession.id === sessionId) {
        state.currentSession.messages.push(message);
      }
      
      // Update session in sessions list
      const sessionIndex = state.sessions.findIndex(s => s.id === sessionId);
      if (sessionIndex !== -1) {
        state.sessions[sessionIndex].messages.push(message);
      }
    },
    updateUnreadCount: (state) => {
      state.unreadCount = state.sessions.reduce((total, session) => {
        return total + chatService.getUnreadCount(session);
      }, 0);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch chat sessions
      .addCase(fetchChatSessions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchChatSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sessions = action.payload;
        // Update unread count
        state.unreadCount = action.payload.reduce((total, session) => {
          return total + chatService.getUnreadCount(session);
        }, 0);
      })
      .addCase(fetchChatSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch single chat session
      .addCase(fetchChatSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchChatSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentSession = action.payload;
        
        // Update session in sessions list
        const sessionIndex = state.sessions.findIndex(s => s.id === action.payload.id);
        if (sessionIndex !== -1) {
          state.sessions[sessionIndex] = action.payload;
        } else {
          state.sessions.push(action.payload);
        }
      })
      .addCase(fetchChatSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create chat session
      .addCase(createChatSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createChatSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sessions.unshift(action.payload);
        state.currentSession = action.payload;
      })
      .addCase(createChatSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Create support session
      .addCase(createSupportSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSupportSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sessions.unshift(action.payload);
        state.currentSession = action.payload;
      })
      .addCase(createSupportSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Send message
      .addCase(sendChatMessage.pending, (state) => {
        state.isSending = true;
        state.error = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.isSending = false;
        const { sessionId, message } = action.payload;
        
        // Update current session if it matches
        if (state.currentSession && state.currentSession.id === sessionId) {
          state.currentSession.messages.push(message);
        }
        
        // Update session in sessions list
        const sessionIndex = state.sessions.findIndex(s => s.id === sessionId);
        if (sessionIndex !== -1) {
          state.sessions[sessionIndex].messages.push(message);
        }
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.isSending = false;
        state.error = action.payload as string;
      })
      
      // Mark message as read
      .addCase(markMessageAsRead.fulfilled, (state, action) => {
        const messageId = action.payload;
        
        // Update current session messages
        if (state.currentSession) {
          const messageIndex = state.currentSession.messages.findIndex(m => m.id === messageId);
          if (messageIndex !== -1) {
            state.currentSession.messages[messageIndex].is_read = true;
          }
        }
        
        // Update sessions list
        state.sessions.forEach(session => {
          const messageIndex = session.messages.findIndex(m => m.id === messageId);
          if (messageIndex !== -1) {
            session.messages[messageIndex].is_read = true;
          }
        });
        
        // Update unread count
        state.unreadCount = state.sessions.reduce((total, session) => {
          return total + chatService.getUnreadCount(session);
        }, 0);
      });
  },
});

export const { setCurrentSession, clearError, addMessageToSession, updateUnreadCount } = chatSlice.actions;
export default chatSlice.reducer;
