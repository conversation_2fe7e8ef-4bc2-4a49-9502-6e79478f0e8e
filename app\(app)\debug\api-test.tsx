import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, RefreshCw, CheckCircle, XCircle, ChevronDown, ChevronUp } from 'lucide-react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import apiTestUtils from '@/utils/apiTestUtils';
import { checkApiHealthWithToast } from '@/utils/apiHealthCheck';
import Button from '@/components/ui/Button';

export default function ApiTestScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [expandedResults, setExpandedResults] = useState<string[]>([]);
  
  // Run API tests
  const runTests = async () => {
    setIsLoading(true);
    setTestResults(null);
    
    try {
      // First check basic health
      const healthCheck = await checkApiHealthWithToast(true);
      
      if (healthCheck) {
        // Run critical endpoint tests
        const results = await apiTestUtils.testCriticalEndpoints(true);
        setTestResults(results);
      } else {
        setTestResults({
          success: 0,
          failed: 1,
          results: [
            {
              name: 'Health Check',
              success: false,
              error: 'API health check failed'
            }
          ]
        });
      }
    } catch (error) {
      console.error('Error running API tests:', error);
      setTestResults({
        success: 0,
        failed: 1,
        results: [
          {
            name: 'Test Runner',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        ]
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Toggle expanded result
  const toggleExpanded = (name: string) => {
    if (expandedResults.includes(name)) {
      setExpandedResults(expandedResults.filter(item => item !== name));
    } else {
      setExpandedResults([...expandedResults, name]);
    }
  };
  
  // Check if a result is expanded
  const isExpanded = (name: string) => {
    return expandedResults.includes(name);
  };
  
  // Render test results
  const renderTestResults = () => {
    if (!testResults) return null;
    
    return (
      <View style={styles.resultsContainer}>
        <View style={[styles.summaryCard, { backgroundColor: colors.card }]}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            Test Summary
          </Text>
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.success[500] }]}>
                {testResults.success}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Passed
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.error[500] }]}>
                {testResults.failed}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Failed
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {testResults.results.length}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Total
              </Text>
            </View>
          </View>
        </View>
        
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Endpoint Results
        </Text>
        
        {testResults.results.map((result: any, index: number) => (
          <View 
            key={result.name} 
            style={[
              styles.resultCard, 
              { 
                backgroundColor: colors.card,
                borderLeftColor: result.success ? colors.success[500] : colors.error[500],
              }
            ]}
          >
            <TouchableOpacity
              style={styles.resultHeader}
              onPress={() => toggleExpanded(result.name)}
            >
              <View style={styles.resultHeaderLeft}>
                {result.success ? (
                  <CheckCircle size={20} color={colors.success[500]} />
                ) : (
                  <XCircle size={20} color={colors.error[500]} />
                )}
                <Text style={[styles.resultName, { color: colors.text }]}>
                  {result.name}
                </Text>
              </View>
              {isExpanded(result.name) ? (
                <ChevronUp size={20} color={colors.textSecondary} />
              ) : (
                <ChevronDown size={20} color={colors.textSecondary} />
              )}
            </TouchableOpacity>
            
            {isExpanded(result.name) && (
              <View style={styles.resultDetails}>
                <Text style={[styles.resultLabel, { color: colors.textSecondary }]}>
                  Status:
                </Text>
                <Text 
                  style={[
                    styles.resultValue, 
                    { 
                      color: result.success ? colors.success[500] : colors.error[500],
                      fontFamily: typography.fonts.medium,
                    }
                  ]}
                >
                  {result.success ? 'Success' : 'Failed'}
                </Text>
                
                {result.data && (
                  <>
                    <Text style={[styles.resultLabel, { color: colors.textSecondary }]}>
                      Response Data:
                    </Text>
                    <Text 
                      style={[styles.resultValue, { color: colors.text }]}
                      numberOfLines={5}
                    >
                      {JSON.stringify(result.data, null, 2)}
                    </Text>
                  </>
                )}
                
                {result.error && (
                  <>
                    <Text style={[styles.resultLabel, { color: colors.textSecondary }]}>
                      Error:
                    </Text>
                    <Text 
                      style={[styles.resultValue, { color: colors.error[500] }]}
                      numberOfLines={5}
                    >
                      {typeof result.error === 'object' 
                        ? JSON.stringify(result.error, null, 2) 
                        : result.error.toString()}
                    </Text>
                  </>
                )}
              </View>
            )}
          </View>
        ))}
      </View>
    );
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />
      
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>API Integration Test</Text>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={[styles.infoCard, { backgroundColor: colors.primary[50] }]}>
          <Text style={[styles.infoText, { color: colors.text }]}>
            This screen tests the integration with the Inerca backend API. It will check connectivity and test critical endpoints.
          </Text>
        </View>
        
        <Button
          title="Run API Tests"
          variant="primary"
          icon={<RefreshCw size={20} color={colors.white} />}
          onPress={runTests}
          loading={isLoading}
          disabled={isLoading}
          style={{ marginVertical: 16 }}
        />
        
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Running API tests...
            </Text>
          </View>
        ) : (
          renderTestResults()
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 40,
  },
  infoCard: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  resultsContainer: {
    marginTop: 16,
  },
  summaryCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  resultCard: {
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    overflow: 'hidden',
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  resultHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultName: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  resultDetails: {
    padding: 16,
    paddingTop: 0,
  },
  resultLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  resultValue: {
    fontSize: 14,
    marginBottom: 12,
  },
});
