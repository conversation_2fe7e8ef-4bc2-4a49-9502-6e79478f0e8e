import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Calendar, AlertCircle, Clock, CheckCircle } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { formatCurrency } from '@/utils/quoteCalculations';

interface RenewalInfoSectionProps {
  renewalDate: string;
  currentPremium: number;
  newPremium?: number;
  currency?: string;
  status: 'upcoming' | 'due' | 'processing' | 'completed';
  onRenewNow?: () => void;
  onViewDetails?: () => void;
}

const RenewalInfoSection: React.FC<RenewalInfoSectionProps> = ({
  renewalDate,
  currentPremium,
  newPremium,
  currency = 'P',
  status,
  onRenewNow,
  onViewDetails,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate days until renewal
  const getDaysUntilRenewal = () => {
    const today = new Date();
    const renewal = new Date(renewalDate);
    const diffTime = renewal.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get status info
  const getStatusInfo = () => {
    switch (status) {
      case 'upcoming':
        return {
          icon: <Clock size={20} color={colors.info[500]} />,
          color: colors.info[500],
          backgroundColor: colors.info[50],
          text: 'Upcoming Renewal',
        };
      case 'due':
        return {
          icon: <AlertCircle size={20} color={colors.warning[500]} />,
          color: colors.warning[500],
          backgroundColor: colors.warning[50],
          text: 'Renewal Due',
        };
      case 'processing':
        return {
          icon: <Clock size={20} color={colors.primary[500]} />,
          color: colors.primary[500],
          backgroundColor: colors.primary[50],
          text: 'Renewal Processing',
        };
      case 'completed':
        return {
          icon: <CheckCircle size={20} color={colors.success[500]} />,
          color: colors.success[500],
          backgroundColor: colors.success[50],
          text: 'Renewal Completed',
        };
      default:
        return {
          icon: <Clock size={20} color={colors.info[500]} />,
          color: colors.info[500],
          backgroundColor: colors.info[50],
          text: 'Upcoming Renewal',
        };
    }
  };

  const statusInfo = getStatusInfo();
  const daysUntilRenewal = getDaysUntilRenewal();
  const premiumChange = newPremium ? newPremium - currentPremium : 0;
  const premiumChangePercentage = newPremium ? ((newPremium - currentPremium) / currentPremium) * 100 : 0;

  const styles = StyleSheet.create({
    container: {
      backgroundColor: statusInfo.backgroundColor,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginVertical: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    headerIcon: {
      marginRight: spacing.sm,
    },
    headerTitle: {
      ...typography.h4,
      color: statusInfo.color,
      flex: 1,
    },
    content: {
      marginBottom: spacing.md,
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    dateIcon: {
      marginRight: spacing.sm,
    },
    dateText: {
      ...typography.body,
      color: colors.text,
    },
    countdownText: {
      ...typography.body,
      color: statusInfo.color,
      fontWeight: 'bold',
      marginBottom: spacing.sm,
    },
    premiumContainer: {
      marginBottom: spacing.md,
    },
    premiumRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    premiumLabel: {
      ...typography.body,
      color: colors.textSecondary,
    },
    premiumValue: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
    },
    premiumChangeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    premiumChangeText: {
      ...typography.caption,
      color: premiumChange > 0 ? colors.error[500] : colors.success[500],
      marginLeft: spacing.xs,
    },
    buttonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    primaryButton: {
      backgroundColor: statusInfo.color,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      flex: 1,
      marginRight: spacing.sm,
      alignItems: 'center',
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: statusInfo.color,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      flex: 1,
      marginLeft: spacing.sm,
      alignItems: 'center',
    },
    primaryButtonText: {
      ...typography.button,
      color: colors.white,
    },
    secondaryButtonText: {
      ...typography.button,
      color: statusInfo.color,
    },
    disabledButton: {
      opacity: 0.5,
    },
  });

  return (
    <Animated.View style={styles.container} entering={FadeIn.duration(300)}>
      <View style={styles.header}>
        {statusInfo.icon}
        <Text style={styles.headerTitle}>{statusInfo.text}</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.dateContainer}>
          <Calendar size={16} color={colors.textSecondary} style={styles.dateIcon} />
          <Text style={styles.dateText}>Renewal Date: {formatDate(renewalDate)}</Text>
        </View>

        {daysUntilRenewal > 0 && status !== 'completed' && (
          <Text style={styles.countdownText}>
            {daysUntilRenewal} {daysUntilRenewal === 1 ? 'day' : 'days'} until renewal
          </Text>
        )}

        <View style={styles.premiumContainer}>
          <View style={styles.premiumRow}>
            <Text style={styles.premiumLabel}>Current Premium:</Text>
            <Text style={styles.premiumValue}>{formatCurrency(currentPremium, currency)}</Text>
          </View>

          {newPremium && (
            <>
              <View style={styles.premiumRow}>
                <Text style={styles.premiumLabel}>New Premium:</Text>
                <Text style={styles.premiumValue}>{formatCurrency(newPremium, currency)}</Text>
              </View>

              <View style={styles.premiumChangeContainer}>
                <Text style={styles.premiumChangeText}>
                  {premiumChange > 0 ? '+' : ''}
                  {formatCurrency(premiumChange, currency)} ({premiumChangePercentage.toFixed(1)}%)
                </Text>
              </View>
            </>
          )}
        </View>
      </View>

      <View style={styles.buttonsContainer}>
        {(status === 'upcoming' || status === 'due') && onRenewNow && (
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={onRenewNow}
            activeOpacity={0.7}
          >
            <Text style={styles.primaryButtonText}>Renew Now</Text>
          </TouchableOpacity>
        )}

        {status === 'processing' && (
          <View style={[styles.primaryButton, styles.disabledButton]}>
            <Text style={styles.primaryButtonText}>Processing...</Text>
          </View>
        )}

        {status === 'completed' && (
          <View style={styles.primaryButton}>
            <Text style={styles.primaryButtonText}>Renewed Successfully</Text>
          </View>
        )}

        {onViewDetails && (
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={onViewDetails}
            activeOpacity={0.7}
          >
            <Text style={styles.secondaryButtonText}>View Details</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

export default RenewalInfoSection;
