import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';

// Define styles with a function to access theme values
const getStyles = (spacing, typography, borders) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.xl,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  title: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes['2xl'],
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.md,
    opacity: 0.7,
  },
});

export default function PoliciesScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(spacing, typography, borders);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={[
        styles.header,
        {
          backgroundColor: colors.background,
          borderBottomColor: colors.border
        }
      ]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>My Policies</Text>
      </View>

      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>No Policies Found</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>Your insurance policies will appear here</Text>
      </View>
    </SafeAreaView>
  );
}