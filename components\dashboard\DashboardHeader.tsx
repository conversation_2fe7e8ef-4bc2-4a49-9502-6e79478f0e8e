import { View, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import { createTheme } from '@/constants/theme';
import { Bell } from 'lucide-react-native';
import { useTheme } from '@/context/ThemeContext';

type DashboardHeaderProps = {
  title: string;
  notificationCount?: number;
  onNotificationPress?: () => void;
};

export default function DashboardHeader({
  title,
  notificationCount = 0,
  onNotificationPress,
}: DashboardHeaderProps) {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(colors, spacing, typography, borders);

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: colors.background,
        borderBottomColor: colors.border
      }
    ]}>
      <Image
            source={require('../../assets/images/logo.png')} 
            style={styles.logoImageContainer}
            resizeMode="cover"/>
      <View style={styles.titleContainer}>
        <Text style={[
          styles.title,
          { color: colors.text }
        ]}>
          
          {title}</Text>
      </View>

      <TouchableOpacity
        style={[
          styles.notificationButton,
          { backgroundColor: colors.card }
        ]}
        onPress={onNotificationPress}
      >
        <Bell size={24} color={colors.primary[500]} />
        {notificationCount > 0 && (
          <View style={[styles.notificationBadge, { backgroundColor: colors.error[500] }]}>
            <Text style={[styles.notificationBadgeText, { color: colors.white }]}>
              {notificationCount > 9 ? '9+' : notificationCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
}

// Define styles with a function to access theme values
const getStyles = (colors: any, spacing: any, typography: any, borders: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
  },
  titleContainer: {
    flex: 1,
    marginLeft: 10,
  },
  title: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.xl,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoImageContainer: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: borders.radius.full,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    fontFamily: typography.fonts.bold,
    fontSize: 10,
    textAlign: 'center',
  },
});