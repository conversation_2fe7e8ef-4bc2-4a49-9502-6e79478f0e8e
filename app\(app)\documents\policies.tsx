import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Platform, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft, FileText, Info, Upload, FileCheck, FileBarChart,
  Search, Filter, SortAsc, SortDesc, X, Tag, Calendar, AlertCircle
} from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import DocumentListSection from '@/components/documents/DocumentListSection';
import DocumentViewer from '@/components/documents/DocumentViewer';
import { Document, PolicyDocument, PolicyDocumentType } from '@/components/documents/types';
import TabNavigation from '@/components/navigation/TabNavigation';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import { showToast } from '@/utils/toast';

export default function PoliciesScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // State for documents and document viewer - initialize with empty array
  const [documents, setDocuments] = useState<PolicyDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [viewerVisible, setViewerVisible] = useState(false);

  // State for search and filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [selectedDocumentTypes, setSelectedDocumentTypes] = useState<PolicyDocumentType[]>([]);
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedImportance, setSelectedImportance] = useState<('high' | 'medium' | 'low')[]>([]);

  // Sample documents for testing - in a real app, these would come from an API or store
  useEffect(() => {
    // Generate some sample documents for testing
    const sampleDocuments: PolicyDocument[] = [
      {
        id: 'doc1',
        name: 'Motor Insurance Policy',
        type: 'Policy',
        status: 'verified',
        date: '2023-05-15',
        policyId: 'pol1',
        policyNumber: 'POL-2023-12345',
        issueDate: '2023-05-15',
        documentType: 'policy_contract',
        importance: 'high',
        fileType: 'pdf',
        tags: ['motor', 'contract']
      },
      {
        id: 'doc2',
        name: 'Policy Schedule',
        type: 'Policy',
        status: 'verified',
        date: '2023-05-15',
        policyId: 'pol1',
        policyNumber: 'POL-2023-12345',
        issueDate: '2023-05-15',
        documentType: 'policy_schedule',
        importance: 'high',
        fileType: 'pdf',
        tags: ['motor', 'schedule']
      },
      {
        id: 'doc3',
        name: 'Premium Receipt - May 2023',
        type: 'Financial',
        status: 'verified',
        date: '2023-05-15',
        policyId: 'pol1',
        policyNumber: 'POL-2023-12345',
        issueDate: '2023-05-15',
        documentType: 'receipt',
        importance: 'medium',
        fileType: 'pdf',
        tags: ['payment', 'receipt']
      },
      {
        id: 'doc4',
        name: 'Policy Endorsement - Address Change',
        type: 'Policy',
        status: 'verified',
        date: '2023-06-20',
        policyId: 'pol1',
        policyNumber: 'POL-2023-12345',
        issueDate: '2023-06-20',
        documentType: 'endorsement',
        importance: 'medium',
        fileType: 'pdf',
        tags: ['endorsement', 'address']
      },
      {
        id: 'doc5',
        name: 'Renewal Notice',
        type: 'Policy',
        status: 'verified',
        date: '2023-11-15',
        policyId: 'pol1',
        policyNumber: 'POL-2023-12345',
        issueDate: '2023-11-15',
        documentType: 'renewal_notice',
        importance: 'high',
        fileType: 'pdf',
        tags: ['renewal']
      },
      {
        id: 'doc6',
        name: 'Terms and Conditions',
        type: 'Policy',
        status: 'verified',
        date: '2023-05-15',
        policyId: 'pol1',
        policyNumber: 'POL-2023-12345',
        issueDate: '2023-05-15',
        documentType: 'terms_and_conditions',
        importance: 'low',
        fileType: 'pdf',
        tags: ['terms']
      }
    ];

    setDocuments(sampleDocuments);
  }, []);

  // Toggle sort order
  const toggleSortOrder = useCallback(() => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setSelectedDocumentTypes([]);
    setSelectedImportance([]);
    setShowFilterModal(false);
  }, []);

  // Toggle document type selection
  const toggleDocumentType = useCallback((type: PolicyDocumentType) => {
    setSelectedDocumentTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  }, []);

  // Toggle importance selection
  const toggleImportance = useCallback((importance: 'high' | 'medium' | 'low') => {
    setSelectedImportance(prev =>
      prev.includes(importance)
        ? prev.filter(i => i !== importance)
        : [...prev, importance]
    );
  }, []);

  // Filter documents based on search query, document types, and importance
  const filteredDocuments = documents.filter(doc => {
    // Filter by search query
    const matchesSearch = searchQuery === '' ||
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.tags && doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));

    // Filter by document type
    const matchesType = selectedDocumentTypes.length === 0 ||
      selectedDocumentTypes.includes(doc.documentType);

    // Filter by importance
    const matchesImportance = selectedImportance.length === 0 ||
      (doc.importance && selectedImportance.includes(doc.importance));

    return matchesSearch && matchesType && matchesImportance;
  });

  // Sort the filtered documents
  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    if (sortBy === 'date') {
      const dateA = new Date(a.issueDate).getTime();
      const dateB = new Date(b.issueDate).getTime();
      return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
    } else if (sortBy === 'name') {
      return sortOrder === 'asc'
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortBy === 'type') {
      return sortOrder === 'asc'
        ? a.documentType.localeCompare(b.documentType)
        : b.documentType.localeCompare(a.documentType);
    }
    return 0;
  });

  console.log('[PoliciesScreen] Rendering with', documents.length, 'documents, filtered to', filteredDocuments.length);

  // Define tabs for navigation
  const tabs = [
    {
      id: 'upload',
      title: 'Upload Hub',
      icon: <Upload size={16} color={colors.textSecondary} />
    },
    {
      id: 'verification',
      title: 'Verification Status',
      icon: <FileCheck size={16} color={colors.textSecondary} />
    },
    {
      id: 'policies',
      title: 'Policy Repository',
      icon: <FileBarChart size={16} color={colors.primary[500]} />
    }
  ];

  // Handle tab change
  const handleTabChange = useCallback((tabId: string) => {
    if (tabId === 'upload') {
      router.push('/documents');
    } else if (tabId === 'verification') {
      router.push('/documents/verification');
    } else if (tabId === 'policies') {
      // Stay on current page
      return;
    }
  }, []);

  // Handle document view
  const handleViewDocument = (document: Document) => {
    console.log('[PoliciesScreen] Viewing document:', document);
    setSelectedDocument(document);
    setViewerVisible(true);
  };

  // Handle document download
  const handleDownloadDocument = (document: Document) => {
    console.log('[PoliciesScreen] Downloading document:', document);
    Alert.alert(
      'Download Document',
      'The document will be downloaded to your device.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Download',
          onPress: () => {
            // Simulate download
            Alert.alert('Success', 'Document downloaded successfully');
          },
        },
      ]
    );
  };

  // Group documents by type using the updated document types
  const policyDocuments = sortedDocuments.filter(doc =>
    doc.documentType === 'policy_contract' || doc.documentType === 'policy_schedule');
  const endorsementDocuments = sortedDocuments.filter(doc =>
    doc.documentType === 'endorsement');
  const receiptDocuments = sortedDocuments.filter(doc =>
    doc.documentType === 'receipt');
  const renewalDocuments = sortedDocuments.filter(doc =>
    doc.documentType === 'renewal_notice' || doc.documentType === 'premium_adjustment');
  const termsDocuments = sortedDocuments.filter(doc =>
    doc.documentType === 'terms_and_conditions');
  const otherDocuments = sortedDocuments.filter(doc =>
    !['policy_contract', 'policy_schedule', 'endorsement', 'receipt',
      'renewal_notice', 'premium_adjustment', 'terms_and_conditions'].includes(doc.documentType));

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionButton: {
      marginLeft: spacing.sm,
      padding: spacing.xs,
      borderRadius: borders.radius.full,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.md,
      marginBottom: spacing.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchInput: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      paddingVertical: spacing.sm,
      marginLeft: spacing.sm,
    },
    filterContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    filterRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: spacing.sm,
    },
    filterChip: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      marginRight: spacing.sm,
      marginBottom: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipSelected: {
      backgroundColor: `${colors.primary[500]}20`,
      borderColor: colors.primary[500],
    },
    filterChipText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    filterChipTextSelected: {
      color: colors.primary[500],
      fontFamily: typography.fonts.medium,
    },
    filterActions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.sm,
    },
    resetButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.border,
      marginRight: spacing.sm,
    },
    resetButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    applyButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
      backgroundColor: colors.primary[500],
    },
    applyButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
    },
    sortContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      marginBottom: spacing.md,
    },
    sortButton: {
      padding: spacing.xs,
      borderRadius: borders.radius.full,
      marginRight: spacing.sm,
    },
    sortByButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderWidth: 1,
      borderColor: colors.border,
    },
    sortByText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginRight: spacing.xs,
    },
    scrollView: {
      flex: 1,
      marginBottom: Platform.OS === 'ios' ? 88 : 60, // Add margin for bottom nav bar
    },
    content: {
      padding: spacing.lg,
    },
    infoCard: {
      backgroundColor: `${colors.primary[500]}10`,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
    },
    infoIcon: {
      marginRight: spacing.md,
    },
    infoText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      flex: 1,
    },
    emptyStateContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    emptyStateIcon: {
      marginBottom: spacing.md,
    },
    emptyStateText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.xs,
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${colors.primary[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      marginRight: spacing.xs,
      marginBottom: spacing.xs,
    },
    tagText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.primary[500],
      marginLeft: 2,
    },
    importanceHigh: {
      backgroundColor: `${colors.error[500]}10`,
      borderColor: colors.error[500],
    },
    importanceMedium: {
      backgroundColor: `${colors.warning[500]}10`,
      borderColor: colors.warning[500],
    },
    importanceLow: {
      backgroundColor: `${colors.success[500]}10`,
      borderColor: colors.success[500],
    },
    importanceText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.text,
    },
    importanceTextHigh: {
      color: colors.error[500],
    },
    importanceTextMedium: {
      color: colors.warning[500],
    },
    importanceTextLow: {
      color: colors.success[500],
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Policy Documents</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setShowSearchBar(!showSearchBar)}
          >
            <Search size={22} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setShowFilterModal(!showFilterModal)}
          >
            <Filter size={22} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={toggleSortOrder}
          >
            {sortOrder === 'asc' ? (
              <SortAsc size={22} color={colors.text} />
            ) : (
              <SortDesc size={22} color={colors.text} />
            )}
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <TabNavigation
            tabs={tabs}
            activeTab="policies"
            onTabChange={handleTabChange}
            scrollable={true}
            style={{ marginBottom: spacing.md }}
          />

          {/* Search Bar */}
          {showSearchBar && (
            <Animated.View
              entering={FadeInDown.duration(200)}
              style={styles.searchContainer}
            >
              <Search size={20} color={colors.textSecondary} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search documents by name or tag..."
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus
              />
              {searchQuery !== '' && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <X size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </Animated.View>
          )}

          {/* Filter Modal */}
          {showFilterModal && (
            <Animated.View
              entering={FadeInDown.duration(200)}
              style={styles.filterContainer}
            >
              <Text style={styles.filterTitle}>Filter Documents</Text>

              {/* Document Type Filters */}
              <Text style={[styles.filterTitle, { fontSize: typography.sizes.sm }]}>Document Type</Text>
              <View style={styles.filterRow}>
                {['policy_contract', 'policy_schedule', 'endorsement', 'receipt',
                  'renewal_notice', 'terms_and_conditions'].map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.filterChip,
                      selectedDocumentTypes.includes(type as PolicyDocumentType) && styles.filterChipSelected
                    ]}
                    onPress={() => toggleDocumentType(type as PolicyDocumentType)}
                  >
                    <Text
                      style={[
                        styles.filterChipText,
                        selectedDocumentTypes.includes(type as PolicyDocumentType) && styles.filterChipTextSelected
                      ]}
                    >
                      {type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Importance Filters */}
              <Text style={[styles.filterTitle, { fontSize: typography.sizes.sm }]}>Importance</Text>
              <View style={styles.filterRow}>
                {['high', 'medium', 'low'].map((importance) => (
                  <TouchableOpacity
                    key={importance}
                    style={[
                      styles.filterChip,
                      selectedImportance.includes(importance as 'high' | 'medium' | 'low') && styles.filterChipSelected
                    ]}
                    onPress={() => toggleImportance(importance as 'high' | 'medium' | 'low')}
                  >
                    <Text
                      style={[
                        styles.filterChipText,
                        selectedImportance.includes(importance as 'high' | 'medium' | 'low') && styles.filterChipTextSelected
                      ]}
                    >
                      {importance.charAt(0).toUpperCase() + importance.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Filter Actions */}
              <View style={styles.filterActions}>
                <TouchableOpacity
                  style={styles.resetButton}
                  onPress={resetFilters}
                >
                  <Text style={styles.resetButtonText}>Reset</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.applyButton}
                  onPress={() => setShowFilterModal(false)}
                >
                  <Text style={styles.applyButtonText}>Apply</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          )}

          {/* Sort Controls */}
          <View style={styles.sortContainer}>
            <TouchableOpacity
              style={styles.sortByButton}
              onPress={() => {
                // Cycle through sort options
                if (sortBy === 'date') setSortBy('name');
                else if (sortBy === 'name') setSortBy('type');
                else setSortBy('date');
              }}
            >
              <Text style={styles.sortByText}>
                Sort: {sortBy === 'date' ? 'Date' : sortBy === 'name' ? 'Name' : 'Type'}
              </Text>
            </TouchableOpacity>
          </View>

          <Animated.View
            style={styles.infoCard}
            entering={FadeInDown.delay(100).springify()}
          >
            <Info
              size={24}
              color={colors.primary[500]}
              style={styles.infoIcon}
            />
            <Text style={styles.infoText}>
              Access all your insurance policy documents, endorsements, and receipts.
              You can view and download these documents at any time.
            </Text>
          </Animated.View>

          {documents.length === 0 ? (
            <Animated.View
              style={styles.emptyStateContainer}
              entering={FadeInDown.delay(200).springify()}
            >
              <FileText
                size={48}
                color={colors.textSecondary}
                style={styles.emptyStateIcon}
              />
              <Text style={styles.emptyStateText}>
                You don't have any policy documents yet
              </Text>
            </Animated.View>
          ) : (
            <>
              {policyDocuments.length > 0 && (
                <DocumentListSection
                  title="Insurance Policies"
                  documents={policyDocuments}
                  onViewDocument={handleViewDocument}
                  emptyStateMessage="No policy documents available"
                />
              )}

              {endorsementDocuments.length > 0 && (
                <DocumentListSection
                  title="Policy Endorsements"
                  documents={endorsementDocuments}
                  onViewDocument={handleViewDocument}
                  emptyStateMessage="No endorsement documents available"
                />
              )}

              {receiptDocuments.length > 0 && (
                <DocumentListSection
                  title="Payment Receipts"
                  documents={receiptDocuments}
                  onViewDocument={handleViewDocument}
                  emptyStateMessage="No receipt documents available"
                />
              )}

              {renewalDocuments.length > 0 && (
                <DocumentListSection
                  title="Renewal Documents"
                  documents={renewalDocuments}
                  onViewDocument={handleViewDocument}
                  emptyStateMessage="No renewal documents available"
                />
              )}

              {termsDocuments.length > 0 && (
                <DocumentListSection
                  title="Terms & Conditions"
                  documents={termsDocuments}
                  onViewDocument={handleViewDocument}
                  emptyStateMessage="No terms documents available"
                />
              )}

              {otherDocuments.length > 0 && (
                <DocumentListSection
                  title="Other Documents"
                  documents={otherDocuments}
                  onViewDocument={handleViewDocument}
                  emptyStateMessage="No other documents available"
                />
              )}
            </>
          )}
        </View>
      </ScrollView>

      {selectedDocument && (
        <DocumentViewer
          document={selectedDocument}
          visible={viewerVisible}
          onClose={() => setViewerVisible(false)}
        />
      )}

      {/* Bottom Navigation Bar */}
      <BottomNavBar />
    </SafeAreaView>
  );
}
