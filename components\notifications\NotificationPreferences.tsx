import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Bell, Mail, MessageSquare, ChevronDown, ChevronUp } from 'lucide-react-native';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import useNotificationStore, { NotificationPreferences } from '@/store/notificationStore';

interface NotificationPreferencesProps {
  onClose?: () => void;
}

const NotificationPreferencesComponent: React.FC<NotificationPreferencesProps> = ({ onClose }) => {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  
  // Get notification preferences from store
  const { preferences, updatePreferences } = useNotificationStore();
  
  // Local state for preferences
  const [localPreferences, setLocalPreferences] = useState<NotificationPreferences>(preferences);
  const [showCategoryOptions, setShowCategoryOptions] = useState(false);
  
  // Update local preferences when store preferences change
  useEffect(() => {
    setLocalPreferences(preferences);
  }, [preferences]);
  
  // Handle toggle changes
  const handleToggleChange = (key: keyof NotificationPreferences, value: boolean) => {
    setLocalPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // Handle category toggle changes
  const handleCategoryToggleChange = (category: string, value: boolean) => {
    setLocalPreferences(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [category]: value
      }
    }));
  };
  
  // Save preferences
  const savePreferences = async () => {
    await updatePreferences(localPreferences);
    if (onClose) onClose();
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    section: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.md,
    },
    preferenceItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.md,
    },
    preferenceItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    iconContainer: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: `${colors.primary[500]}20`,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    preferenceLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    preferenceDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
    categoryHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacing.sm,
    },
    categoryHeaderText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    categoryContent: {
      marginLeft: spacing.lg,
      marginTop: spacing.sm,
    },
    categoryItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    categoryLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    buttonContainer: {
      padding: spacing.md,
    },
    saveButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      alignItems: 'center',
    },
    saveButtonText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Notification Preferences</Text>
        <Text style={styles.subtitle}>
          Manage how you receive notifications from the app
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Channels</Text>
        
        <View style={styles.preferenceItem}>
          <View style={styles.preferenceItemLeft}>
            <View style={styles.iconContainer}>
              <Bell size={20} color={colors.primary[500]} />
            </View>
            <View>
              <Text style={styles.preferenceLabel}>Push Notifications</Text>
              <Text style={styles.preferenceDescription}>
                Receive notifications on your device
              </Text>
            </View>
          </View>
          <Switch
            value={localPreferences.enablePush}
            onValueChange={(value) => handleToggleChange('enablePush', value)}
            trackColor={{ false: colors.border, true: colors.primary[500] }}
            thumbColor={colors.white}
          />
        </View>
        
        <View style={styles.preferenceItem}>
          <View style={styles.preferenceItemLeft}>
            <View style={styles.iconContainer}>
              <Mail size={20} color={colors.primary[500]} />
            </View>
            <View>
              <Text style={styles.preferenceLabel}>Email Notifications</Text>
              <Text style={styles.preferenceDescription}>
                Receive notifications via email
              </Text>
            </View>
          </View>
          <Switch
            value={localPreferences.enableEmail}
            onValueChange={(value) => handleToggleChange('enableEmail', value)}
            trackColor={{ false: colors.border, true: colors.primary[500] }}
            thumbColor={colors.white}
          />
        </View>
        
        <View style={styles.preferenceItem}>
          <View style={styles.preferenceItemLeft}>
            <View style={styles.iconContainer}>
              <MessageSquare size={20} color={colors.primary[500]} />
            </View>
            <View>
              <Text style={styles.preferenceLabel}>SMS Notifications</Text>
              <Text style={styles.preferenceDescription}>
                Receive notifications via SMS
              </Text>
            </View>
          </View>
          <Switch
            value={localPreferences.enableSMS}
            onValueChange={(value) => handleToggleChange('enableSMS', value)}
            trackColor={{ false: colors.border, true: colors.primary[500] }}
            thumbColor={colors.white}
          />
        </View>
      </View>

      <View style={styles.section}>
        <TouchableOpacity 
          style={styles.categoryHeader}
          onPress={() => setShowCategoryOptions(!showCategoryOptions)}
        >
          <Text style={styles.categoryHeaderText}>Notification Categories</Text>
          {showCategoryOptions ? (
            <ChevronUp size={20} color={colors.textSecondary} />
          ) : (
            <ChevronDown size={20} color={colors.textSecondary} />
          )}
        </TouchableOpacity>
        
        {showCategoryOptions && (
          <Animated.View 
            style={styles.categoryContent}
            entering={FadeIn}
          >
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>Policy Updates</Text>
              <Switch
                value={localPreferences.categories.policy}
                onValueChange={(value) => handleCategoryToggleChange('policy', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>Claims</Text>
              <Switch
                value={localPreferences.categories.claim}
                onValueChange={(value) => handleCategoryToggleChange('claim', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>Payments</Text>
              <Switch
                value={localPreferences.categories.payment}
                onValueChange={(value) => handleCategoryToggleChange('payment', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>Documents</Text>
              <Switch
                value={localPreferences.categories.document}
                onValueChange={(value) => handleCategoryToggleChange('document', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>Applications</Text>
              <Switch
                value={localPreferences.categories.application}
                onValueChange={(value) => handleCategoryToggleChange('application', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>Renewals</Text>
              <Switch
                value={localPreferences.categories.renewal}
                onValueChange={(value) => handleCategoryToggleChange('renewal', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
            
            <View style={styles.categoryItem}>
              <Text style={styles.categoryLabel}>System Notifications</Text>
              <Switch
                value={localPreferences.categories.system}
                onValueChange={(value) => handleCategoryToggleChange('system', value)}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>
          </Animated.View>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={savePreferences}
        >
          <Text style={styles.saveButtonText}>Save Preferences</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default NotificationPreferencesComponent;
