import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { DocumentPublic, Status } from '@/types/backend';

// Document state interface
interface DocumentState {
  documents: DocumentPublic[];
  policyDocuments: { [policyId: string]: DocumentPublic[] };
  isLoading: boolean;
  uploadProgress: { [documentId: string]: number };
  error: string | null;
}

// Initial state
const initialState: DocumentState = {
  documents: [],
  policyDocuments: {},
  isLoading: false,
  uploadProgress: {},
  error: null,
};

// Async thunks
export const fetchDocuments = createAsyncThunk(
  'document/fetchDocuments',
  async (_, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const documents = await apiService.documents.getAllDocuments();
      return documents;
    } catch (error: any) {
      console.error('Fetch documents error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to fetch documents';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchPolicyDocuments = createAsyncThunk(
  'document/fetchPolicyDocuments',
  async (policyId: string, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const documents = await apiService.documents.getPolicyDocuments(policyId);
      return { policyId, documents };
    } catch (error: any) {
      console.error('Fetch policy documents error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to fetch policy documents';
      return rejectWithValue(errorMessage);
    }
  }
);

export const uploadDocument = createAsyncThunk(
  'document/uploadDocument',
  async ({ file, policyId }: { file: any; policyId?: string }, { rejectWithValue, dispatch }) => {
    try {
      console.log('[Document Slice] Starting document upload');
      console.log('[Document Slice] File:', file);
      console.log('[Document Slice] Policy ID:', policyId);

      const { apiService } = await import('@/services/api');

      // Create a temporary document ID for progress tracking
      const tempId = `temp_${Date.now()}`;
      console.log('[Document Slice] Created temp ID for progress tracking:', tempId);
      dispatch(setUploadProgress({ documentId: tempId, progress: 0 }));

      // Simulate upload progress (in real implementation, you'd track actual progress)
      const progressInterval = setInterval(() => {
        const progress = Math.random() * 80;
        console.log('[Document Slice] Upload progress:', progress);
        dispatch(setUploadProgress({ documentId: tempId, progress }));
      }, 500);

      console.log('[Document Slice] Calling upload API');
      const document = await apiService.documents.uploadDocument(file, policyId);
      console.log('[Document Slice] Document uploaded successfully:', document);

      clearInterval(progressInterval);
      dispatch(setUploadProgress({ documentId: tempId, progress: 100 }));
      console.log('[Document Slice] Upload progress set to 100%');

      // Clean up progress after a short delay
      setTimeout(() => {
        console.log('[Document Slice] Cleaning up progress tracking');
        dispatch(clearUploadProgress(tempId));
      }, 2000);

      return document;
    } catch (error: any) {
      console.error('[Document Slice] Upload document error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to upload document';
      console.error('[Document Slice] Error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const downloadDocument = createAsyncThunk(
  'document/downloadDocument',
  async (documentId: string, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const blob = await apiService.documents.downloadDocument(documentId);
      return { documentId, blob };
    } catch (error: any) {
      console.error('Download document error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to download document';
      return rejectWithValue(errorMessage);
    }
  }
);

export const viewDocument = createAsyncThunk(
  'document/viewDocument',
  async (documentId: string, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      const blob = await apiService.documents.viewDocument(documentId);
      return { documentId, blob };
    } catch (error: any) {
      console.error('View document error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to view document';
      return rejectWithValue(errorMessage);
    }
  }
);

export const deleteDocument = createAsyncThunk(
  'document/deleteDocument',
  async (documentId: string, { rejectWithValue }) => {
    try {
      const { apiService } = await import('@/services/api');
      await apiService.documents.deleteDocument(documentId);
      return documentId;
    } catch (error: any) {
      console.error('Delete document error:', error);
      const errorMessage = error?.response?.data?.detail || error?.message || 'Failed to delete document';
      return rejectWithValue(errorMessage);
    }
  }
);

// Document slice
const documentSlice = createSlice({
  name: 'document',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearDocuments: (state) => {
      state.documents = [];
      state.policyDocuments = {};
    },
    setUploadProgress: (state, action: PayloadAction<{ documentId: string; progress: number }>) => {
      state.uploadProgress[action.payload.documentId] = action.payload.progress;
    },
    clearUploadProgress: (state, action: PayloadAction<string>) => {
      delete state.uploadProgress[action.payload];
    },
    updateDocumentStatus: (state, action: PayloadAction<{ documentId: string; status: Status }>) => {
      const { documentId, status } = action.payload;

      // Update in main documents array
      const docIndex = state.documents.findIndex(doc => doc.id === documentId);
      if (docIndex !== -1) {
        state.documents[docIndex].status = status;
      }

      // Update in policy documents
      Object.keys(state.policyDocuments).forEach(policyId => {
        const policyDocIndex = state.policyDocuments[policyId].findIndex(doc => doc.id === documentId);
        if (policyDocIndex !== -1) {
          state.policyDocuments[policyId][policyDocIndex].status = status;
        }
      });
    },
  },
  extraReducers: (builder) => {
    // Fetch documents
    builder.addCase(fetchDocuments.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchDocuments.fulfilled, (state, action) => {
      state.isLoading = false;
      state.documents = action.payload;
    });
    builder.addCase(fetchDocuments.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Fetch policy documents
    builder.addCase(fetchPolicyDocuments.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchPolicyDocuments.fulfilled, (state, action) => {
      state.isLoading = false;
      state.policyDocuments[action.payload.policyId] = action.payload.documents;
    });
    builder.addCase(fetchPolicyDocuments.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Upload document
    builder.addCase(uploadDocument.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(uploadDocument.fulfilled, (state, action) => {
      state.isLoading = false;
      state.documents.push(action.payload);

      // If document has a policy_id, add it to policy documents
      if (action.payload.policy_id) {
        if (!state.policyDocuments[action.payload.policy_id]) {
          state.policyDocuments[action.payload.policy_id] = [];
        }
        state.policyDocuments[action.payload.policy_id].push(action.payload);
      }
    });
    builder.addCase(uploadDocument.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Delete document
    builder.addCase(deleteDocument.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(deleteDocument.fulfilled, (state, action) => {
      state.isLoading = false;
      const documentId = action.payload;

      // Remove from main documents array
      state.documents = state.documents.filter(doc => doc.id !== documentId);

      // Remove from policy documents
      Object.keys(state.policyDocuments).forEach(policyId => {
        state.policyDocuments[policyId] = state.policyDocuments[policyId].filter(doc => doc.id !== documentId);
      });
    });
    builder.addCase(deleteDocument.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
  },
});

export const {
  clearError,
  clearDocuments,
  setUploadProgress,
  clearUploadProgress,
  updateDocumentStatus
} = documentSlice.actions;

export default documentSlice.reducer;

// Selectors
export const selectDocuments = (state: { document: DocumentState }) => state.document.documents;
export const selectPolicyDocuments = (policyId: string) => (state: { document: DocumentState }) =>
  state.document.policyDocuments[policyId] || [];
export const selectIsDocumentLoading = (state: { document: DocumentState }) => state.document.isLoading;
export const selectDocumentError = (state: { document: DocumentState }) => state.document.error;
export const selectUploadProgress = (documentId: string) => (state: { document: DocumentState }) =>
  state.document.uploadProgress[documentId] || 0;

// Computed selectors
export const selectDocumentsByStatus = (status: Status) => (state: { document: DocumentState }) =>
  state.document.documents.filter(doc => doc.status === status);

export const selectVerifiedDocuments = (state: { document: DocumentState }) =>
  state.document.documents.filter(doc => doc.status === Status.APPROVED);

export const selectPendingDocuments = (state: { document: DocumentState }) =>
  state.document.documents.filter(doc => doc.status === Status.PENDING);

export const selectRejectedDocuments = (state: { document: DocumentState }) =>
  state.document.documents.filter(doc => doc.status === Status.REJECTED);
