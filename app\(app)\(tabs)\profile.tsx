import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { router } from 'expo-router';
import { User, Mail, Phone, MapPin, Briefcase, Edit2, Camera, FileText, Building, CreditCard, UserCheck } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { updateProfileImage, UserType } from '@/store/authSlice';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import Button from '@/components/ui/Button';

export default function ProfileScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();
  const [isImageLoading, setIsImageLoading] = useState(false);

  // Get user from Redux store
  const user = useAppSelector(state => state.auth.user);
  const isLoading = useAppSelector(state => state.auth.isLoading);

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
    },
    profileHeader: {
      alignItems: 'center',
      marginBottom: spacing.xl,
    },
    profileImageContainer: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: 'gray',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.md,
      overflow: 'hidden',
      position: 'relative', // Add this to position the overlay correctly
    },
    profileImage: {
      width: '100%',
      height: '100%',
    },
    profileInitial: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.primary[500],
    },
    userName: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    userEmail: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    infoCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.lg,
      marginBottom: spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    infoIcon: {
      marginRight: spacing.md,
      width: 24,
    },
    infoLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      width: 80,
    },
    infoValue: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      flex: 1,
    },
    editButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.lg,
    },
    editButtonText: {
      color: colors.white,
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      marginLeft: spacing.xs,
    },
    kycButton: {
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.md,
    },
    kycButtonText: {
      color: colors.primary[500],
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      marginLeft: spacing.xs,
    },
    editImageOverlay: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      padding: spacing.xs,
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      width: '100%',
      height: '100%',
    },
  });

  const handleProfileImagePress = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Please enable photo library access');
        return;
      }

      setIsImageLoading(true);

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled) {
        console.log('Selected image:', result.assets[0].uri);
        await dispatch(updateProfileImage(result.assets[0].uri));
        Alert.alert('Success', 'Profile image updated successfully');
      }
    } catch (error) {
      console.error('Error updating profile image:', error);
      Alert.alert('Error', 'Failed to update profile image. Please try again.');
    } finally {
      setIsImageLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Profile</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View
          style={styles.profileHeader}
          entering={FadeInDown.delay(100).springify()}
        >
          <TouchableOpacity
            style={styles.profileImageContainer}
            onPress={handleProfileImagePress}
            disabled={isImageLoading}
          >
            {isImageLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary[500]} />
              </View>
            ) : user?.profileImage ? (
              <Image
                source={{ uri: user.profileImage }}
                style={styles.profileImage}
                resizeMode="cover"
              />
            ) : (
              <Text style={styles.profileInitial}>
                {user?.userType === 'individual'
                  ? (user?.firstName?.charAt(0) || 'U')
                  : (user?.companyName?.charAt(0) || 'C')
                }
              </Text>
            )}
            <View style={styles.editImageOverlay}>
              <Camera size={16} color={colors.white} />
            </View>
          </TouchableOpacity>
          <Text style={styles.userName}>
            {user?.userType === 'business'
              ? (user?.companyName || 'Company Name')
              : (user ? `${user.firstName || ''} ${user.lastName || ''}` : 'User Name')}
          </Text>
          <Text style={styles.userEmail}>{user?.email || '<EMAIL>'}</Text>
        </Animated.View>

        {user?.userType === 'individual' ? (
          <>
            <Animated.Text
              style={styles.sectionTitle}
              entering={FadeInDown.delay(200).springify()}
            >
              Personal Information
            </Animated.Text>

            <Animated.View
              style={styles.infoCard}
              entering={FadeInDown.delay(300).springify()}
            >
              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <User size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Full Name</Text>
                <Text style={styles.infoValue}>
                  {user ? `${user.firstName || ''} ${user.lastName || ''}` : 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <CreditCard size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>ID Number</Text>
                <Text style={styles.infoValue}>
                  {user?.idNumber || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Mail size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{user?.email || 'Not provided'}</Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Phone size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{user?.phone || 'Not provided'}</Text>
              </View>
            </Animated.View>

            <Animated.Text
              style={styles.sectionTitle}
              entering={FadeInDown.delay(400).springify()}
            >
              Additional Information
            </Animated.Text>

            <Animated.View
              style={styles.infoCard}
              entering={FadeInDown.delay(500).springify()}
            >
              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <MapPin size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>
                  {user?.address || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Briefcase size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Occupation</Text>
                <Text style={styles.infoValue}>
                  {user?.occupation || 'Not provided'}
                </Text>
              </View>
            </Animated.View>
          </>
        ) : (
          <>
            <Animated.Text
              style={styles.sectionTitle}
              entering={FadeInDown.delay(200).springify()}
            >
              Business Information
            </Animated.Text>

            <Animated.View
              style={styles.infoCard}
              entering={FadeInDown.delay(300).springify()}
            >
              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Building size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Company</Text>
                <Text style={styles.infoValue}>
                  {user?.companyName || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <FileText size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Reg. No.</Text>
                <Text style={styles.infoValue}>
                  {user?.registrationNumber || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <CreditCard size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Tax No.</Text>
                <Text style={styles.infoValue}>
                  {user?.taxNumber || 'Not provided'}
                </Text>
              </View>
            </Animated.View>

            <Animated.Text
              style={styles.sectionTitle}
              entering={FadeInDown.delay(400).springify()}
            >
              Contact Information
            </Animated.Text>

            <Animated.View
              style={styles.infoCard}
              entering={FadeInDown.delay(500).springify()}
            >
              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <UserCheck size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Contact</Text>
                <Text style={styles.infoValue}>
                  {user?.contactPersonName || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Mail size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{user?.email || 'Not provided'}</Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Phone size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{user?.phone || 'Not provided'}</Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <MapPin size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>
                  {user?.address || 'Not provided'}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <View style={styles.infoIcon}>
                  <Briefcase size={20} color={colors.primary[500]} />
                </View>
                <Text style={styles.infoLabel}>Industry</Text>
                <Text style={styles.infoValue}>
                  {user?.industry || 'Not provided'}
                </Text>
              </View>
            </Animated.View>
          </>
        )}

        <Animated.View
          entering={FadeInDown.delay(600).springify()}
        >
          <Button
            title="Edit Profile"
            variant="primary"
            size="medium"
            icon={<Edit2 size={18} color={colors.white} />}
            iconPosition="left"
            onPress={() => router.push('/(app)/profile/edit')}
            style={{ marginTop: spacing.lg }}
          />
        </Animated.View>

        <Animated.View
          entering={FadeInDown.delay(700).springify()}
          style={{ marginBottom: spacing.xl }} // Add bottom margin to ensure it's not hidden
        >
          <Button
            title="Documents"
            variant="outline"
            size="medium"
            icon={<FileText size={18} color={colors.primary[500]} />}
            iconPosition="left"
            onPress={() => router.push('/(app)/documents')}
            style={{ marginTop: spacing.md }}
          />
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

