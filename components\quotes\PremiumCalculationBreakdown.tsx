import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { formatCurrency } from '@/utils/quoteCalculations';

interface PremiumItem {
  id: string;
  name: string;
  amount: number;
  isDiscount?: boolean;
}

interface PremiumCalculationBreakdownProps {
  basePremium: number;
  items: PremiumItem[];
  totalPremium: number;
  currency?: string;
}

const PremiumCalculationBreakdown: React.FC<PremiumCalculationBreakdownProps> = ({
  basePremium,
  items,
  totalPremium,
  currency = 'P',
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // We're using the imported formatCurrency function from utils/quoteCalculations

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.lg,
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    itemRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    itemName: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    itemAmount: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      textAlign: 'right',
    },
    basePremiumRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.md,
    },
    basePremiumLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    basePremiumAmount: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      textAlign: 'right',
    },
    divider: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: spacing.md,
    },
    totalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.sm,
    },
    totalLabel: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    totalAmount: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.primary[500],
      textAlign: 'right',
    },
  });

  return (
    <Animated.View
      style={styles.container}
      entering={FadeInDown.delay(100).springify()}
    >
      <Text style={styles.title}>Premium Breakdown</Text>

      <View style={styles.basePremiumRow}>
        <Text style={styles.basePremiumLabel}>Base Premium</Text>
        <Text style={styles.basePremiumAmount}>{formatCurrency(basePremium, currency)}</Text>
      </View>

      {items.map((item, index) => (
        <Animated.View
          key={item.id}
          style={styles.itemRow}
          entering={FadeInDown.delay(150 + index * 50).springify()}
        >
          <Text style={styles.itemName}>{item.name}</Text>
          <Text
            style={[
              styles.itemAmount,
              {
                color: item.isDiscount ? colors.success[500] : colors.text,
              },
            ]}
          >
            {item.isDiscount ? '-' : '+'}{formatCurrency(item.amount, currency)}
          </Text>
        </Animated.View>
      ))}

      <View style={styles.divider} />

      <View style={styles.totalRow}>
        <Text style={styles.totalLabel}>Total Premium</Text>
        <Text style={styles.totalAmount}>{formatCurrency(totalPremium, currency)}</Text>
      </View>
    </Animated.View>
  );
};

export default PremiumCalculationBreakdown;
