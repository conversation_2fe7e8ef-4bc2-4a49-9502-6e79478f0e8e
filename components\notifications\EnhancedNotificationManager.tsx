import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { router } from 'expo-router';

import { RootState, AppDispatch } from '@/store/store';
import { markAsRead } from '@/store/notificationSlice';
import NotificationToast from './NotificationToast';

const EnhancedNotificationManager: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { notifications } = useSelector((state: RootState) => state.notification);
  const [visibleNotifications, setVisibleNotifications] = useState<string[]>([]);

  useEffect(() => {
    // Show new unread notifications as toasts
    const newNotifications = notifications
      .filter(notification => !notification.read && !visibleNotifications.includes(notification.id))
      .slice(0, 3); // Limit to 3 visible toasts

    if (newNotifications.length > 0) {
      setVisibleNotifications(prev => [
        ...prev,
        ...newNotifications.map(n => n.id)
      ]);
    }
  }, [notifications, visibleNotifications]);

  const handleNotificationPress = (notificationId: string) => {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
      // Mark as read
      dispatch(markAsRead(notificationId));
      
      // Navigate to action route if available
      if (notification.actionRoute) {
        router.push(notification.actionRoute);
      }
    }
    
    // Remove from visible notifications
    setVisibleNotifications(prev => prev.filter(id => id !== notificationId));
  };

  const handleNotificationDismiss = (notificationId: string) => {
    // Mark as read
    dispatch(markAsRead(notificationId));
    
    // Remove from visible notifications
    setVisibleNotifications(prev => prev.filter(id => id !== notificationId));
  };

  const visibleNotificationData = notifications.filter(n => 
    visibleNotifications.includes(n.id)
  );

  return (
    <View style={styles.container}>
      {visibleNotificationData.map((notification, index) => (
        <View key={notification.id} style={{ top: 60 + (index * 80) }}>
          <NotificationToast
            notification={notification}
            onPress={() => handleNotificationPress(notification.id)}
            onDismiss={() => handleNotificationDismiss(notification.id)}
          />
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    pointerEvents: 'box-none',
  },
});

export default EnhancedNotificationManager;
