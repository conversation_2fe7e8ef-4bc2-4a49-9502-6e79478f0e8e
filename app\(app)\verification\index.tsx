import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, FileText, AlertCircle, Upload, Check, User, Building, Home, Briefcase, CreditCard } from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { useAppSelector } from '@/store/hooks';
import { UserType } from '@/store/authSlice';
import useQuoteStore from '@/store/quoteStore';
import DocumentUploader from '@/components/documents/DocumentUploader';
import { Document } from '@/components/documents/types';
import { showToast } from '@/utils/toast';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import Button from '@/components/ui/Button';
import { useDocumentUpload } from '@/context/DocumentUploadContext';

// Define required documents based on user type
const getRequiredDocuments = (userType: UserType) => {
  const commonDocuments = [
    {
      id: '1',
      name: 'Proof of Identity',
      description: 'National ID, Passport, or Driver\'s License',
      type: 'ID',
      icon: User,
      required: true,
    },
    {
      id: '2',
      name: 'Proof of Address',
      description: userType === 'individual' ? 'Utility bill or bank statement' : 'Business address verification',
      type: 'Proof of Address',
      icon: Home,
      required: true,
    },
  ];

  const individualDocuments = [
    {
      id: '3',
      name: 'Proof of Income',
      description: 'Recent payslip or bank statement',
      type: 'Financial',
      icon: CreditCard,
      required: true,
    },
  ];

  const businessDocuments = [
    {
      id: '3',
      name: 'Business Registration',
      description: 'Company registration certificate',
      type: 'Legal',
      icon: Building,
      required: true,
    },
    {
      id: '4',
      name: 'Tax Clearance',
      description: 'Valid tax clearance certificate',
      type: 'Financial',
      icon: CreditCard,
      required: true,
    },
    {
      id: '5',
      name: 'Business License',
      description: 'Current business operating license',
      type: 'Legal',
      icon: Briefcase,
      required: false,
    },
  ];

  return userType === 'individual'
    ? [...commonDocuments, ...individualDocuments]
    : [...commonDocuments, ...businessDocuments];
};

export default function VerificationScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const user = useAppSelector(state => state.auth.user);
  const userType = user?.userType || 'individual';

  // Get document upload context
  const {
    verifiedDocuments,
    pendingDocuments,
    documentsInVerification,
    uploadDocument
  } = useDocumentUpload();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentDocumentId, setCurrentDocumentId] = useState<string | null>(null);

  // Get required documents based on user type
  const requiredDocuments = getRequiredDocuments(userType);

  // Check document verification status
  const getDocumentStatus = (documentType: string) => {
    // Check if document is verified
    const verified = verifiedDocuments.find(doc =>
      doc.name.toLowerCase().includes(documentType.toLowerCase())
    );

    if (verified) return 'verified';

    // Check if document is pending verification
    const pending = pendingDocuments.find(doc =>
      doc.name.toLowerCase().includes(documentType.toLowerCase())
    );

    if (pending) {
      const isInVerification = documentsInVerification.includes(pending.id);
      return isInVerification ? 'in_verification' : 'pending';
    }

    return 'not_uploaded';
  };

  // Handle document upload
  const handleDocumentUploaded = (document: Document, documentType: string) => {
    try {
      // Add metadata to document
      document.metadata = {
        ...document.metadata,
        documentType,
        userType,
        userId: user?.id,
      };

      // Upload document
      uploadDocument(document);

      // Show success toast
      showToast(
        'success',
        'Document Uploaded',
        'Your document has been uploaded and is pending verification',
        { visibilityTime: 3000 }
      );

      // Reset current document
      setCurrentDocumentId(null);
    } catch (error) {
      console.error('Error uploading document:', error);
      showToast(
        'error',
        'Upload Failed',
        'Failed to upload document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  // Handle submit verification
  const handleSubmitVerification = async () => {
    // Check if all required documents are uploaded
    const allRequiredUploaded = requiredDocuments
      .filter(doc => doc.required)
      .every(doc => getDocumentStatus(doc.name) !== 'not_uploaded');

    if (!allRequiredUploaded) {
      showToast(
        'error',
        'Missing Documents',
        'Please upload all required documents before submitting',
        { visibilityTime: 3000 }
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Import the application flow actions
      const { createApplicationFlow } = await import('@/store/applicationFlowSlice');
      const { store } = await import('@/store/store');

      // Get current quote from quote store
      const currentQuote = useQuoteStore.getState().currentQuote;

      if (currentQuote) {
        // Create application flow
        const result = await store.dispatch(createApplicationFlow({
          quoteId: currentQuote.id,
          clientInfo: currentQuote.clientInfo,
          type: currentQuote.type,
          premium: currentQuote.premium || 0,
          currency: currentQuote.currency || 'P',
          coverAmount: currentQuote.coverAmount || 0,
        }));

        // Start automated flow if creation was successful
        if (createApplicationFlow.fulfilled.match(result)) {
          const { default: applicationFlowService } = await import('@/services/applicationFlowService');
          await applicationFlowService.startAutomatedFlow(result.payload.id);
        }
      }

      setIsSubmitting(false);
      showToast(
        'success',
        'Documents Submitted',
        'Your documents are under review. You will be notified once verified.',
        { visibilityTime: 4000 }
      );

      // Navigate to application tracker
      router.push('/applications');
    } catch (error) {
      console.error('Error submitting verification:', error);
      setIsSubmitting(false);
      showToast(
        'error',
        'Submission Failed',
        'Failed to submit documents. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Document Verification</Text>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={[styles.infoCard, { backgroundColor: colors.primary[50] }]}
            entering={FadeInDown.delay(100).springify()}
          >
            <AlertCircle
              size={24}
              color={colors.primary[500]}
              style={styles.infoIcon}
            />
            <Text style={[styles.infoText, { color: colors.text }]}>
              Please upload the required documents for {userType === 'individual' ? 'personal' : 'business'} verification.
              All documents must be clear and legible.
            </Text>
          </Animated.View>

          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Required Documents for {userType === 'individual' ? 'Individual' : 'Business'}
          </Text>

          {requiredDocuments.map((doc, index) => {
            const status = getDocumentStatus(doc.name);
            const isUploading = currentDocumentId === doc.id;

            return (
              <Animated.View
                key={doc.id}
                style={[
                  styles.documentCard,
                  { backgroundColor: colors.card }
                ]}
                entering={FadeInDown.delay(200 + index * 100).springify()}
              >
                <View style={styles.documentHeader}>
                  <View style={styles.documentInfo}>
                    <doc.icon size={24} color={colors.primary[500]} />
                    <View style={styles.documentTitles}>
                      <Text style={[styles.documentTitle, { color: colors.text }]}>
                        {doc.name} {doc.required && <Text style={{ color: colors.error[500] }}>*</Text>}
                      </Text>
                      <Text style={[styles.documentDescription, { color: colors.textSecondary }]}>
                        {doc.description}
                      </Text>
                    </View>
                  </View>

                  <View style={[
                    styles.statusBadge,
                    status === 'verified' && { backgroundColor: colors.success[100] },
                    status === 'in_verification' && { backgroundColor: colors.warning[100] },
                    status === 'pending' && { backgroundColor: colors.primary[100] },
                    status === 'not_uploaded' && { backgroundColor: colors.neutral[100] },
                  ]}>
                    <Text style={[
                      styles.statusText,
                      status === 'verified' && { color: colors.success[700] },
                      status === 'in_verification' && { color: colors.warning[700] },
                      status === 'pending' && { color: colors.primary[700] },
                      status === 'not_uploaded' && { color: colors.neutral[700] },
                    ]}>
                      {status === 'verified' && 'Verified'}
                      {status === 'in_verification' && 'In Verification'}
                      {status === 'pending' && 'Pending'}
                      {status === 'not_uploaded' && 'Not Uploaded'}
                    </Text>
                  </View>
                </View>

                {status !== 'verified' && (
                  <View style={styles.uploaderContainer}>
                    {isUploading ? (
                      <ActivityIndicator color={colors.primary[500]} />
                    ) : (
                      <TouchableOpacity
                        style={[
                          styles.uploadButton,
                          { backgroundColor: colors.primary[500] }
                        ]}
                        onPress={() => setCurrentDocumentId(doc.id)}
                      >
                        <Upload size={18} color={colors.white} />
                        <Text style={[styles.uploadButtonText, { color: colors.white }]}>
                          {status === 'not_uploaded' ? 'Upload Document' : 'Re-upload'}
                        </Text>
                      </TouchableOpacity>
                    )}

                    {currentDocumentId === doc.id && (
                      <View style={styles.documentUploader}>
                        <DocumentUploader
                          onDocumentUploaded={(document) => handleDocumentUploaded(document, doc.name)}
                          preselectedDocumentType={doc.name}
                        />
                      </View>
                    )}
                  </View>
                )}
              </Animated.View>
            );
          })}

          <View style={styles.submitContainer}>
            <Button
              title="Submit for Verification"
              variant="primary"
              onPress={handleSubmitVerification}
              loading={isSubmitting}
              disabled={isSubmitting}
              icon={<Check size={20} color={colors.white} />}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <BottomNavBar />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 100,
  },
  infoCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoIcon: {
    marginRight: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  documentCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  documentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  documentTitles: {
    marginLeft: 12,
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  uploaderContainer: {
    marginTop: 8,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
  },
  uploadButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  documentUploader: {
    marginTop: 16,
  },
  submitContainer: {
    marginTop: 24,
    marginBottom: 40,
  },
});
