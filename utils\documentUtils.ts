import { Document } from '@/components/documents/types';
import Toast from 'react-native-toast-message';
import { Platform, InteractionManager } from 'react-native';
import * as FileSystem from 'expo-file-system';

/**
 * Process a document upload without causing app rebundling
 * @param document The document to process
 * @param callback Function to call with the processed document
 */
export const processDocumentUpload = (
  document: Document,
  callback: (document: Document) => void
) => {
  // Log the document being processed
  console.log('[DocumentUtils] Processing document upload:', document.name);

  // Use a more reliable approach to prevent re-renders
  // First, create a copy of the document to avoid reference issues
  const documentCopy = { ...document };

  // Use InteractionManager to ensure we don't block the UI thread
  // This is more reliable than setTimeout or requestAnimationFrame alone
  if (Platform.OS === 'ios') {
    // On iOS, use a combination of requestAnimationFrame and InteractionManager
    requestAnimationFrame(() => {
      InteractionManager.runAfterInteractions(() => {
        // Process the document in the callback
        callback(documentCopy);

        // Show success toast after a small delay to ensure UI is ready
        setTimeout(() => {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Document uploaded successfully and is pending verification',
            visibilityTime: 4000,
            topOffset: 60,
          });
        }, 100);
      });
    });
  } else {
    // On Android, use InteractionManager with a longer delay
    InteractionManager.runAfterInteractions(() => {
      // Process the document in the callback
      callback(documentCopy);

      // Show success toast after a small delay to ensure UI is ready
      setTimeout(() => {
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Document uploaded successfully and is pending verification',
          visibilityTime: 4000,
          topOffset: 60,
        });
      }, 100);
    });
  }
};

/**
 * Get a unique filename for a document
 * @param document The document to generate a filename for
 * @returns A unique filename with appropriate extension
 */
export const getUniqueFilename = (document: Document): string => {
  // Base filename from document name or a fallback
  const baseFilename = document.fileName ||
    `${document.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${Date.now()}`;

  // Determine file extension based on document type
  let fileExtension = '';
  if (document.fileType === 'pdf') fileExtension = '.pdf';
  else if (document.fileType === 'image') fileExtension = '.jpg';
  else if (document.fileType === 'doc') fileExtension = '.docx';
  else if (document.fileType === 'excel') fileExtension = '.xlsx';
  else if (document.fileType === 'powerpoint') fileExtension = '.pptx';
  else if (document.fileType === 'text') fileExtension = '.txt';
  else if (document.fileType === 'rtf') fileExtension = '.rtf';
  else fileExtension = '.bin';

  // Return filename with extension if it doesn't already have one
  return baseFilename.includes('.') ? baseFilename : `${baseFilename}${fileExtension}`;
};
