import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import {
  PaymentHistoryItem,
  PaymentFilters
} from '@/types/payment.types';
import { apiService } from '@/services/api';
import { PaymentCreate, PaymentPublic, PaymentUpdate, PaymentStatus } from '@/types/backend';

// Simplified payment store - only handles document upload for proof of payment verification

export type PaymentProof = {
  id: string;
  applicationId: string;
  reference: string;
  amount: number;
  currency: string;
  date: string;
  uploadDate: string;
  documentId: string;
  status: 'pending' | 'verified' | 'rejected';
  verificationDate?: string;
  rejectionReason?: string;
};

// Simplified payment state - only handles document upload for proof of payment
interface PaymentState {
  paymentProofs: PaymentProof[];
  paymentHistory: PaymentHistoryItem[];
  isLoading: boolean;
  error: string | null;

  // Payment Proof Actions - simplified for document upload only
  fetchPaymentProofs: () => Promise<void>;
  addPaymentProof: (proof: Omit<PaymentProof, 'id' | 'status' | 'uploadDate'>) => Promise<PaymentProof>;
  updatePaymentProofStatus: (id: string, status: PaymentProof['status'], details?: any) => Promise<void>;
  getPaymentProofsByApplication: (applicationId: string) => PaymentProof[];

  // Payment History Actions - simplified
  fetchPaymentHistory: () => Promise<void>;
  getPaymentById: (id: string) => PaymentHistoryItem | undefined;
  getPaymentsByFilter: (filters: PaymentFilters) => PaymentHistoryItem[];
}

// Simplified - removed payment method storage functionality

// Helper function to load payment proofs from storage
const loadPaymentProofsFromStorage = async (): Promise<PaymentProof[] | null> => {
  try {
    const proofsJson = await AsyncStorage.getItem('paymentProofs');
    if (proofsJson) {
      return JSON.parse(proofsJson);
    }
    return null;
  } catch (error) {
    console.error('Error loading payment proofs from storage:', error);
    return null;
  }
};

// Helper function to save payment proofs to storage
const savePaymentProofsToStorage = async (proofs: PaymentProof[]): Promise<void> => {
  try {
    await AsyncStorage.setItem('paymentProofs', JSON.stringify(proofs));
  } catch (error) {
    console.error('Error saving payment proofs to storage:', error);
  }
};

// Helper function to load payment history from storage
const loadPaymentHistoryFromStorage = async (): Promise<PaymentHistoryItem[] | null> => {
  try {
    const historyJson = await AsyncStorage.getItem('paymentHistory');
    if (historyJson) {
      return JSON.parse(historyJson);
    }
    return null;
  } catch (error) {
    console.error('Error loading payment history from storage:', error);
    return null;
  }
};

// Simplified - removed payment history saving functionality

// Simplified - removed receipt functionality

// Create the simplified store - only handles payment proofs (document uploads)
const usePaymentStore = create<PaymentState>((set, get) => ({
  paymentProofs: [],
  paymentHistory: [],
  isLoading: false,
  error: null,

  // Fetch payment proofs
  fetchPaymentProofs: async () => {
    set({ isLoading: true, error: null });
    try {
      // First, try to load payment proofs from AsyncStorage
      const storedProofs = await loadPaymentProofsFromStorage();

      // If we have stored proofs, use them
      if (storedProofs && storedProofs.length > 0) {
        console.log('Using payment proofs from AsyncStorage:', storedProofs.length);
        set({ paymentProofs: storedProofs, isLoading: false });
        return;
      }

      // If no stored proofs, use empty array
      set({ paymentProofs: [], isLoading: false });
    } catch (error) {
      console.error('Error fetching payment proofs:', error);
      set({
        error: 'Failed to fetch payment proofs. Please try again.',
        isLoading: false
      });
    }
  },

  // Add a new payment proof
  addPaymentProof: async (proof: Omit<PaymentProof, 'id' | 'status' | 'uploadDate'>) => {
    set({ isLoading: true, error: null });
    try {
      // Generate ID
      const id = `proof-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      // Create new payment proof
      const newProof: PaymentProof = {
        ...proof,
        id,
        status: 'pending',
        uploadDate: new Date().toISOString().split('T')[0]
      };

      // Add the new proof
      const newProofs = [...get().paymentProofs, newProof];

      // Update state
      set({ paymentProofs: newProofs, isLoading: false });

      // Save to AsyncStorage
      await savePaymentProofsToStorage(newProofs);

      // Show success toast
      showToast(
        'success',
        'Payment Proof Uploaded',
        'Your payment proof has been uploaded successfully',
        { visibilityTime: 3000 }
      );

      return newProof;
    } catch (error) {
      console.error('Error adding payment proof:', error);
      set({
        error: 'Failed to add payment proof. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update payment proof status
  updatePaymentProofStatus: async (id: string, status: PaymentProof['status'], details?: any) => {
    set({ isLoading: true, error: null });
    try {
      // Find the proof
      const proof = get().paymentProofs.find(p => p.id === id);

      if (!proof) {
        throw new Error(`Payment proof with ID ${id} not found`);
      }

      // Update the proof
      const updatedProof = {
        ...proof,
        status,
        ...(status === 'verified' ? { verificationDate: new Date().toISOString().split('T')[0] } : {}),
        ...(status === 'rejected' && details?.reason ? { rejectionReason: details.reason } : {})
      };

      // Update all proofs
      const updatedProofs = get().paymentProofs.map(p =>
        p.id === id ? updatedProof : p
      );

      // Update state
      set({ paymentProofs: updatedProofs, isLoading: false });

      // Save to AsyncStorage
      await savePaymentProofsToStorage(updatedProofs);

      // Show toast based on status
      if (status === 'verified') {
        showToast(
          'success',
          'Payment Verified',
          'Your payment has been verified successfully',
          { visibilityTime: 3000 }
        );
      } else if (status === 'rejected') {
        showToast(
          'error',
          'Payment Rejected',
          details?.reason || 'Your payment proof has been rejected',
          { visibilityTime: 4000 }
        );
      }

      return;
    } catch (error) {
      console.error('Error updating payment proof status:', error);
      set({
        error: 'Failed to update payment proof status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Get payment proofs by application ID
  getPaymentProofsByApplication: (applicationId: string) => {
    return get().paymentProofs.filter(p => p.applicationId === applicationId);
  },

  // Fetch payment history
  fetchPaymentHistory: async () => {
    set({ isLoading: true, error: null });
    try {
      // Try to fetch from backend first
      try {
        console.log('[PaymentStore] Fetching payment history from backend');
        const backendPayments = await apiService.payments.getPayments();

        // Convert backend payments to local format
        const convertedPayments: PaymentHistoryItem[] = backendPayments.map((payment: PaymentPublic) => ({
          id: payment.id,
          reference: `PAY-${payment.id.slice(-8)}`,
          amount: payment.amount,
          currency: payment.currency,
          date: payment.created_at.split('T')[0],
          status: payment.status === PaymentStatus.SUCCESS ? 'completed' :
                 payment.status === PaymentStatus.FAILED ? 'failed' : 'pending',
          method: 'eft',
          description: `Payment for policy ${payment.policy_id || 'N/A'}`,
          applicationId: payment.policy_id,
          proofOfPaymentId: undefined, // Will be set if document exists
        }));

        console.log('[PaymentStore] Payment history fetched from backend:', convertedPayments.length);
        set({ paymentHistory: convertedPayments, isLoading: false });
        return;
      } catch (backendError) {
        console.log('[PaymentStore] Backend fetch failed, falling back to local storage');
      }

      // Fallback to local storage
      const storedHistory = await loadPaymentHistoryFromStorage();

      // If we have stored history, use it
      if (storedHistory && storedHistory.length > 0) {
        console.log('Using payment history from AsyncStorage:', storedHistory.length);
        set({ paymentHistory: storedHistory, isLoading: false });
        return;
      }

      // If no stored history, use empty array
      set({ paymentHistory: [], isLoading: false });
    } catch (error) {
      console.error('Error fetching payment history:', error);
      set({
        error: 'Failed to fetch payment history. Please try again.',
        isLoading: false
      });
    }
  },

  // Create a new payment (backend integration)
  createPayment: async (paymentData: PaymentCreate): Promise<PaymentPublic> => {
    set({ isLoading: true, error: null });
    try {
      console.log('[PaymentStore] Creating payment:', paymentData);
      const payment = await apiService.payments.createPayment(paymentData);
      console.log('[PaymentStore] Payment created successfully:', payment.id);

      // Refresh payment history
      get().fetchPaymentHistory();

      set({ isLoading: false });
      return payment;
    } catch (error) {
      console.error('[PaymentStore] Error creating payment:', error);
      set({
        error: 'Failed to create payment. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Update payment status (backend integration)
  updatePaymentStatus: async (paymentId: string, status: PaymentStatus): Promise<void> => {
    set({ isLoading: true, error: null });
    try {
      console.log('[PaymentStore] Updating payment status:', paymentId, status);
      await apiService.payments.updatePayment(paymentId, { status });
      console.log('[PaymentStore] Payment status updated successfully');

      // Refresh payment history
      get().fetchPaymentHistory();

      set({ isLoading: false });
    } catch (error) {
      console.error('[PaymentStore] Error updating payment status:', error);
      set({
        error: 'Failed to update payment status. Please try again.',
        isLoading: false
      });
      throw error;
    }
  },

  // Get payment by ID
  getPaymentById: (id: string) => {
    return get().paymentHistory.find(p => p.id === id);
  },

  // Get payments by filter
  getPaymentsByFilter: (filters: PaymentFilters) => {
    let filteredPayments = [...get().paymentHistory];

    // Filter by status
    if (filters.status && filters.status.length > 0) {
      filteredPayments = filteredPayments.filter(p => filters.status?.includes(p.status));
    }

    // Filter by method
    if (filters.method && filters.method.length > 0) {
      filteredPayments = filteredPayments.filter(p => filters.method?.includes(p.method));
    }

    // Filter by type
    if (filters.type && filters.type.length > 0) {
      filteredPayments = filteredPayments.filter(p => filters.type?.includes(p.type));
    }

    // Filter by category
    if (filters.category && filters.category.length > 0) {
      filteredPayments = filteredPayments.filter(p => filters.category?.includes(p.category));
    }

    // Filter by date range
    if (filters.dateRange) {
      if (filters.dateRange.start) {
        const startDate = new Date(filters.dateRange.start);
        filteredPayments = filteredPayments.filter(p => new Date(p.date) >= startDate);
      }
      if (filters.dateRange.end) {
        const endDate = new Date(filters.dateRange.end);
        filteredPayments = filteredPayments.filter(p => new Date(p.date) <= endDate);
      }
    }

    // Filter by search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filteredPayments = filteredPayments.filter(p =>
        p.reference.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query) ||
        p.policyNumber?.toLowerCase().includes(query) ||
        p.claimReference?.toLowerCase().includes(query) ||
        p.applicationReference?.toLowerCase().includes(query)
      );
    }

    return filteredPayments;
  },

  // Simplified - removed receipt functionality
}));

export default usePaymentStore;
