import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function GoogleCallbackScreen() {
  const { code, error } = useLocalSearchParams<{ code?: string; error?: string }>();
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing } = createTheme(isDarkMode);

  useEffect(() => {
    // This screen is just a placeholder for the OAuth redirect
    // The actual OAuth handling is done in the auth slice using WebBrowser
    // We'll redirect back to login with appropriate messages
    
    if (error) {
      console.log('Google OAuth error:', error);
      router.replace('/(auth)/login');
    } else if (code) {
      console.log('Google OAuth code received:', code);
      // The code will be handled by the WebBrowser session
      router.replace('/(auth)/login');
    } else {
      // No code or error, redirect to login
      router.replace('/(auth)/login');
    }
  }, [code, error]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      alignItems: 'center',
      justifyContent: 'center',
    },
    content: {
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      marginTop: spacing.lg,
      marginBottom: spacing.md,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.content}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={styles.title}>Processing Google Sign-in</Text>
        <Text style={styles.subtitle}>
          Please wait while we complete your Google sign-in...
        </Text>
      </View>
    </SafeAreaView>
  );
}
