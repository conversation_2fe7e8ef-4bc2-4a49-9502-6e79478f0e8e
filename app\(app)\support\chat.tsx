import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  MessageCircle,
  Plus,
  Clock,
  User,
  Headphones,
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import {
  fetchChatSessions,
  createSupportSession,
  setCurrentSession,
} from '@/store/chatSlice';
import { ChatSessionPublic } from '@/types/backend';
import { chatService } from '@/services/chatService';

export default function ChatScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  const { sessions, isLoading, error } = useAppSelector((state) => state.chat);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(fetchChatSessions());
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchChatSessions());
    setRefreshing(false);
  };

  const handleCreateNewChat = async () => {
    try {
      const result = await dispatch(createSupportSession('New Support Request'));
      if (createSupportSession.fulfilled.match(result)) {
        router.push(`/support/chat/${result.payload.id}`);
      }
    } catch (error) {
      console.error('Error creating new chat:', error);
    }
  };

  const handleChatPress = (session: ChatSessionPublic) => {
    dispatch(setCurrentSession(session));
    router.push(`/support/chat/${session.id}`);
  };

  const renderChatItem = ({ item }: { item: ChatSessionPublic }) => {
    const lastMessage = chatService.getLastMessage(item);
    const unreadCount = chatService.getUnreadCount(item);
    const lastMessageTime = lastMessage ? chatService.formatMessageTime(lastMessage.created_at) : '';

    return (
      <Animated.View entering={FadeInDown.delay(100).springify()}>
        <TouchableOpacity
          style={[styles.chatItem, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => handleChatPress(item)}
        >
          <View style={styles.chatItemLeft}>
            <View style={[styles.avatarContainer, { backgroundColor: colors.primary[100] }]}>
              <Headphones size={24} color={colors.primary[500]} />
            </View>
            <View style={styles.chatItemContent}>
              <Text style={[styles.chatTitle, { color: colors.text }]} numberOfLines={1}>
                {item.title}
              </Text>
              {lastMessage && (
                <Text style={[styles.lastMessage, { color: colors.textSecondary }]} numberOfLines={1}>
                  {lastMessage.content}
                </Text>
              )}
            </View>
          </View>
          <View style={styles.chatItemRight}>
            {lastMessageTime && (
              <Text style={[styles.timeText, { color: colors.textSecondary }]}>
                {lastMessageTime}
              </Text>
            )}
            {unreadCount > 0 && (
              <View style={[styles.unreadBadge, { backgroundColor: colors.primary[500] }]}>
                <Text style={[styles.unreadText, { color: colors.white }]}>
                  {unreadCount > 99 ? '99+' : unreadCount}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderEmptyState = () => (
    <Animated.View 
      style={styles.emptyContainer}
      entering={FadeInDown.delay(200).springify()}
    >
      <MessageCircle size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No conversations yet
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Start a conversation with our support team to get help with your insurance needs.
      </Text>
      <TouchableOpacity
        style={[styles.startChatButton, { backgroundColor: colors.primary[500] }]}
        onPress={handleCreateNewChat}
      >
        <Plus size={20} color={colors.white} />
        <Text style={[styles.startChatText, { color: colors.white }]}>
          Start New Chat
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: spacing.sm,
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    newChatButton: {
      padding: spacing.sm,
    },
    content: {
      flex: 1,
    },
    chatItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
    },
    chatItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    avatarContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    chatItemContent: {
      flex: 1,
    },
    chatTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginBottom: spacing.xs,
    },
    lastMessage: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
    },
    chatItemRight: {
      alignItems: 'flex-end',
    },
    timeText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      marginBottom: spacing.xs,
    },
    unreadBadge: {
      minWidth: 20,
      height: 20,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: spacing.xs,
    },
    unreadText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xs,
    },
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: spacing.xl,
    },
    emptyTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    emptySubtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      textAlign: 'center',
      marginBottom: spacing.xl,
    },
    startChatButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: borders.radius.lg,
    },
    startChatText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginLeft: spacing.sm,
    },
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Support Chat</Text>
        <TouchableOpacity style={styles.newChatButton} onPress={handleCreateNewChat}>
          <Plus size={24} color={colors.primary[500]} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {isLoading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
          </View>
        ) : (
          <FlatList
            data={sessions}
            renderItem={renderChatItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ flexGrow: 1 }}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[colors.primary[500]]}
                tintColor={colors.primary[500]}
              />
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
}
