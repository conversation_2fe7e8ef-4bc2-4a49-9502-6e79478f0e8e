import { apiService } from './api';
import {
  ClaimCreate,
  ClaimUpdate,
  ClaimPublic,
  Status,
  DocumentPublic,
  DocumentType
} from '@/types/backend';

export class ClaimsService {
  // Create a new claim
  async createClaim(claimData: ClaimCreate): Promise<ClaimPublic> {
    try {
      console.log('[ClaimsService] Creating new claim:', claimData);
      const response = await apiService.claims.createClaim(claimData);
      console.log('[ClaimsService] Claim created successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ClaimsService] Error creating claim:', error);
      throw error;
    }
  }

  // Get all claims for the current user
  async getClaims(filters?: { status?: Status; policy_id?: string }): Promise<ClaimPublic[]> {
    try {
      console.log('[ClaimsService] Fetching claims with filters:', filters);
      const response = await apiService.claims.getClaims(filters);
      console.log('[ClaimsService] Claims fetched successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[ClaimsService] Error fetching claims:', error);
      throw error;
    }
  }

  // Get a specific claim by ID
  async getClaimById(claimId: string): Promise<ClaimPublic> {
    try {
      console.log('[ClaimsService] Fetching claim:', claimId);
      const response = await apiService.claims.getClaimById(claimId);
      console.log('[ClaimsService] Claim fetched successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ClaimsService] Error fetching claim:', error);
      throw error;
    }
  }

  // Update a claim
  async updateClaim(claimId: string, claimData: ClaimUpdate): Promise<ClaimPublic> {
    try {
      console.log('[ClaimsService] Updating claim:', claimId, claimData);
      const response = await apiService.claims.updateClaim(claimId, claimData);
      console.log('[ClaimsService] Claim updated successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ClaimsService] Error updating claim:', error);
      throw error;
    }
  }

  // Delete a claim
  async deleteClaim(claimId: string): Promise<void> {
    try {
      console.log('[ClaimsService] Deleting claim:', claimId);
      await apiService.claims.deleteClaim(claimId);
      console.log('[ClaimsService] Claim deleted successfully');
    } catch (error) {
      console.error('[ClaimsService] Error deleting claim:', error);
      throw error;
    }
  }

  // Upload claim document
  async uploadClaimDocument(claimId: string, file: File, documentType: DocumentType): Promise<DocumentPublic> {
    try {
      console.log('[ClaimsService] Uploading claim document:', claimId, documentType);
      const response = await apiService.documents.uploadDocument(file, {
        document_type: documentType,
        claim_id: claimId,
      });
      console.log('[ClaimsService] Claim document uploaded successfully:', response.id);
      return response;
    } catch (error) {
      console.error('[ClaimsService] Error uploading claim document:', error);
      throw error;
    }
  }

  // Get claim documents
  async getClaimDocuments(claimId: string): Promise<DocumentPublic[]> {
    try {
      console.log('[ClaimsService] Fetching claim documents:', claimId);
      const response = await apiService.documents.getDocuments({ claim_id: claimId });
      console.log('[ClaimsService] Claim documents fetched successfully:', response.length);
      return response;
    } catch (error) {
      console.error('[ClaimsService] Error fetching claim documents:', error);
      throw error;
    }
  }

  // Get claim status display text
  getStatusDisplayText(status: Status): string {
    switch (status) {
      case Status.PENDING:
        return 'Under Review';
      case Status.APPROVED:
        return 'Approved';
      case Status.REJECTED:
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  // Get claim status color
  getStatusColor(status: Status): string {
    switch (status) {
      case Status.PENDING:
        return '#F59E0B'; // Warning yellow
      case Status.APPROVED:
        return '#10B981'; // Success green
      case Status.REJECTED:
        return '#EF4444'; // Error red
      default:
        return '#6B7280'; // Gray
    }
  }

  // Format claim amount
  formatClaimAmount(amount?: number): string {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-BW', {
      style: 'currency',
      currency: 'BWP',
      minimumFractionDigits: 2,
    }).format(amount);
  }

  // Format incident date
  formatIncidentDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-BW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  // Get required documents for claim type
  getRequiredDocuments(claimType: string): DocumentType[] {
    const baseDocuments: DocumentType[] = [DocumentType.COPY_OF_VERIFIABLE_ID];
    
    switch (claimType.toLowerCase()) {
      case 'motor':
      case 'vehicle':
        return [
          ...baseDocuments,
          DocumentType.DRIVER_LICENSE,
          DocumentType.VEHICLE_REGISTRATION_BOOK,
        ];
      case 'property':
      case 'home':
        return [
          ...baseDocuments,
          DocumentType.TITLE_DEED,
          DocumentType.PROOF_OF_ADDRESS,
        ];
      case 'life':
        return [
          ...baseDocuments,
          DocumentType.PROOF_OF_INCOME,
        ];
      default:
        return baseDocuments;
    }
  }

  // Check if claim can be edited
  canEditClaim(claim: ClaimPublic): boolean {
    return claim.status === Status.PENDING;
  }

  // Check if claim can be deleted
  canDeleteClaim(claim: ClaimPublic): boolean {
    return claim.status === Status.PENDING;
  }

  // Get claim progress percentage
  getClaimProgress(claim: ClaimPublic): number {
    switch (claim.status) {
      case Status.PENDING:
        return 50;
      case Status.APPROVED:
        return 100;
      case Status.REJECTED:
        return 100;
      default:
        return 0;
    }
  }

  // Get next steps for claim
  getNextSteps(claim: ClaimPublic): string[] {
    switch (claim.status) {
      case Status.PENDING:
        return [
          'Your claim is being reviewed by our team',
          'You may be contacted for additional information',
          'Processing typically takes 5-10 business days',
        ];
      case Status.APPROVED:
        return [
          'Your claim has been approved',
          'Payment will be processed within 3-5 business days',
          'You will receive a confirmation email',
        ];
      case Status.REJECTED:
        return [
          'Your claim has been rejected',
          'Review the rejection reason provided',
          'You may appeal this decision or submit a new claim',
        ];
      default:
        return [];
    }
  }

  // Validate claim data
  validateClaimData(claimData: Partial<ClaimCreate>): string[] {
    const errors: string[] = [];

    if (!claimData.policy_id) {
      errors.push('Policy is required');
    }

    if (!claimData.claim_type) {
      errors.push('Claim type is required');
    }

    if (!claimData.description || claimData.description.trim().length < 10) {
      errors.push('Description must be at least 10 characters');
    }

    if (!claimData.incident_date) {
      errors.push('Incident date is required');
    } else {
      const incidentDate = new Date(claimData.incident_date);
      const today = new Date();
      if (incidentDate > today) {
        errors.push('Incident date cannot be in the future');
      }
    }

    if (claimData.claim_amount && claimData.claim_amount <= 0) {
      errors.push('Claim amount must be greater than 0');
    }

    return errors;
  }
}

export const claimsService = new ClaimsService();
