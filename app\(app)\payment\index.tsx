import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, AlertCircle } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import Animated, { FadeIn } from 'react-native-reanimated';
import useApplicationStore from '@/store/applicationStore';
import DocumentUploader from '@/components/documents/DocumentUploader';
import { Document } from '@/components/documents/types';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function PaymentsScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const applicationId = params.applicationId as string;

  const { getApplicationById, uploadPaymentProof } = useApplicationStore();

  const [application, setApplication] = useState<any>(null);
  const [uploadStatus, setUploadStatus] = useState<'pending' | 'uploading' | 'success' | 'error'>('pending');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const loadApplication = async () => {
      if (applicationId) {
        setIsLoading(true);
        try {
          const app = await getApplicationById(applicationId);
          setApplication(app);
        } catch (error) {
          console.error('Error loading application:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadApplication();
  }, [applicationId, getApplicationById]);

  // Handle document uploaded
  const handleDocumentUploaded = async (document: Document) => {
    if (!application) return;

    setUploadStatus('uploading');

    try {
      await uploadPaymentProof(application.id, document.id);

      // Update local application state
      const updatedApp = await getApplicationById(application.id);
      setApplication(updatedApp);

      setUploadStatus('success');

      // Navigate back to application detail after 2 seconds
      setTimeout(() => {
        router.push(`/applications/${application.id}`);
      }, 2000);
    } catch (error) {
      console.error('Error uploading payment proof:', error);
      setUploadStatus('error');
    }
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  // Render content - simplified to only show document upload
  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={styles.loadingText}>Loading application...</Text>
        </View>
      );
    }

    if (!application) {
      return (
        <View style={styles.emptyContainer}>
          <AlertCircle size={48} color={colors.textSecondary} />
          <Text style={styles.emptyText}>No application selected</Text>
        </View>
      );
    }

    return (
      <View>
        <View style={styles.referenceContainer}>
          <Text style={styles.referenceLabel}>Payment Reference</Text>
          <Text style={styles.referenceValue}>{application.reference}</Text>
        </View>

        <View style={styles.instructionContainer}>
          <Text style={styles.instructionTitle}>Upload Proof of Payment</Text>
          <Text style={styles.instructionText}>
            Please upload your proof of payment document. This can be a bank statement,
            receipt, or any document that shows the payment was made.
          </Text>
        </View>

        <DocumentUploader
          onDocumentUploaded={handleDocumentUploaded}
          preselectedDocumentType="Payment Proof - Premium"
        />

        {uploadStatus !== 'pending' && (
          <View style={[
            styles.statusContainer,
            {
              backgroundColor: uploadStatus === 'success'
                ? '#e8f5e8'
                : uploadStatus === 'error'
                  ? '#ffebee'
                  : '#e3f2fd'
            }
          ]}>
            <Text style={[
              styles.statusText,
              {
                color: uploadStatus === 'success'
                  ? '#2e7d32'
                  : uploadStatus === 'error'
                    ? '#d32f2f'
                    : '#1976d2'
              }
            ]}>
              {uploadStatus === 'uploading' && 'Uploading...'}
              {uploadStatus === 'success' && 'Upload successful!'}
              {uploadStatus === 'error' && 'Upload failed. Please try again.'}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#e0e0e0',
      backgroundColor: '#ffffff',
    },
    backButton: {
      marginRight: 8,
    },
    title: {
      fontSize: 20,
      fontWeight: '600',
      color: '#000000',
      flex: 1,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    referenceContainer: {
      backgroundColor: '#e3f2fd',
      borderRadius: 8,
      padding: 16,
      marginBottom: 16,
    },
    referenceLabel: {
      fontSize: 12,
      color: '#1976d2',
      marginBottom: 4,
    },
    referenceValue: {
      fontSize: 18,
      color: '#1976d2',
      fontWeight: 'bold',
    },
    instructionContainer: {
      backgroundColor: '#f5f5f5',
      borderRadius: 8,
      padding: 16,
      marginBottom: 16,
    },
    instructionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: '#000000',
      marginBottom: 8,
    },
    instructionText: {
      fontSize: 14,
      color: '#666666',
      lineHeight: 20,
    },
    statusContainer: {
      padding: 16,
      borderRadius: 8,
      marginTop: 16,
      marginBottom: 16,
    },
    statusText: {
      fontSize: 16,
      textAlign: 'center',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyText: {
      fontSize: 16,
      color: '#666666',
      marginTop: 16,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    loadingText: {
      fontSize: 16,
      color: '#666666',
      marginTop: 16,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Payment</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeIn.duration(300)}>
          {renderContent()}
        </Animated.View>
      </ScrollView>

      <BottomNavBar />
    </SafeAreaView>
  );
}
