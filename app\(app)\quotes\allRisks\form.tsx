import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { DollarSign, Info, FileText, Package, Plus, Trash2 } from 'lucide-react-native';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import QuoteDocumentsSection from '@/components/quotes/QuoteDocumentsSection';
import useQuoteStore from '@/store/quoteStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { QuoteDocument } from '@/types/quote.types';
import { calculateAllRisksPremium } from '@/utils/quoteCalculations';

// Define the SpecifiedItem interface
interface SpecifiedItem {
  id: string;
  name: string;
  description: string;
  value: number;
}

export default function AllRisksQuoteForm() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  // Get params
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    setCurrentQuote,
    currentQuote,
    isLoading: isQuoteLoading
  } = useQuoteStore();

  // Form state
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);
  const [isStorageLoading, setIsStorageLoading] = useState(true);
  const [storedQuoteId, setStoredQuoteId] = useState<string | null>(null);
  const [documents, setDocuments] = useState<QuoteDocument[]>([]);
  const [activeTab, setActiveTab] = useState<'details' | 'documents'>('details');

  // State for specified item being edited
  const [currentItem, setCurrentItem] = useState<SpecifiedItem>({
    id: '',
    name: '',
    description: '',
    value: 0
  });
  const [isAddingItem, setIsAddingItem] = useState(false);

  // First useEffect to check AsyncStorage for stored quote data
  useEffect(() => {
    const checkAsyncStorage = async () => {
      try {
        // Try to get the quoteId from AsyncStorage
        const storedId = await AsyncStorage.getItem('currentQuoteId');

        console.log('AllRisksQuoteForm - Retrieved from AsyncStorage:', storedId);

        if (storedId) setStoredQuoteId(storedId);

        setIsStorageLoading(false);
      } catch (error) {
        console.error('AllRisksQuoteForm - AsyncStorage error:', error);
        setIsStorageLoading(false);
      }
    };

    checkAsyncStorage();
  }, []);

  // Load quote data
  useEffect(() => {
    // Skip if still loading AsyncStorage data
    if (isStorageLoading) return;

    console.log('AllRisksQuoteForm - Params received:', { quoteId });
    console.log('AllRisksQuoteForm - Stored data:', { storedQuoteId });

    // Use quoteId from params or AsyncStorage
    const quoteIdToUse = quoteId || storedQuoteId;

    if (!quoteIdToUse) {
      console.error('AllRisksQuoteForm - No quoteId found');
      showToast(
        'error',
        'Error',
        'Missing quote ID information',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
      return;
    }

    console.log('AllRisksQuoteForm - Looking up quote with ID:', quoteIdToUse);
    const quote = getQuoteById(quoteIdToUse);

    if (quote) {
      console.log('AllRisksQuoteForm - Quote found:', quote.id, 'Type:', quote.type);
      setCurrentQuote(quote);

      // Initialize form data based on existing quote data
      if (quote.additionalInfo) {
        setFormData(quote.additionalInfo);
      } else {
        // Set default form data
        setFormData({
          unspecifiedItemsValue: 0,
          limitPerItem: 0,
          specifiedItems: [],
        });
      }

      // Initialize documents if they exist
      if (quote.documents && quote.documents.length > 0) {
        setDocuments(quote.documents);
      } else {
        // No need for required documents for All Risks insurance based on requirements
        setDocuments([]);
      }
    } else {
      showToast(
        'error',
        'Error',
        'Quote not found',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
    }
  }, [quoteId, storedQuoteId, isStorageLoading, getQuoteById, setCurrentQuote]);

  // Validate form when data or documents change
  useEffect(() => {
    validateForm();
  }, [formData, documents]);

  // Update form data
  const updateFormField = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form - memoize to prevent unnecessary re-renders
  const validateForm = React.useCallback(() => {
    const newErrors: Record<string, string> = {};

    if ((!formData.unspecifiedItemsValue || formData.unspecifiedItemsValue <= 0) &&
        (!formData.specifiedItems || formData.specifiedItems.length === 0)) {
      newErrors.unspecifiedItemsValue = 'Either unspecified items value or specified items are required';
    }

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      newErrors.documents = 'All required documents must be uploaded';
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  }, [formData, documents]);

  // Handle adding a specified item
  const handleAddItem = () => {
    if (!currentItem.name || !currentItem.value || currentItem.value <= 0) {
      showToast(
        'error',
        'Invalid Item',
        'Please provide a name and value for the item',
        { visibilityTime: 3000 }
      );
      return;
    }

    const newItem = {
      ...currentItem,
      id: Date.now().toString(), // Generate a unique ID
    };

    // Add the new item to the specifiedItems array
    const updatedItems = [...(formData.specifiedItems || []), newItem];
    updateFormField('specifiedItems', updatedItems);

    // Reset the current item
    setCurrentItem({
      id: '',
      name: '',
      description: '',
      value: 0
    });
    setIsAddingItem(false);

    showToast(
      'success',
      'Item Added',
      `${newItem.name} has been added to your specified items`,
      { visibilityTime: 2000 }
    );
  };

  // Handle removing a specified item
  const handleRemoveItem = (itemId: string) => {
    const updatedItems = (formData.specifiedItems || []).filter(
      (item: SpecifiedItem) => item.id !== itemId
    );
    updateFormField('specifiedItems', updatedItems);

    showToast(
      'success',
      'Item Removed',
      'The item has been removed from your specified items',
      { visibilityTime: 2000 }
    );
  };

  // Handle back button
  const handleBack = () => {
    // Save the current form data before navigating back
    if (currentQuote && formData) {
      const saveBeforeBack = async () => {
        try {
          console.log('Saving draft before navigating back:', {
            quoteId: currentQuote.id,
            formData
          });

          // Update the quote with the current form data and documents
          await updateQuote({
            additionalInfo: formData,
            documents: documents,
            // Make sure the status is still draft
            status: 'draft',
            // Update the timestamp to ensure it's at the top of the list
            updatedAt: new Date().toISOString().split('T')[0]
          });

          // Get the fetchQuotes function from the store to refresh the quotes list
          // Use getState() to access store methods without hooks
          const storeState = useQuoteStore.getState();
          const fetchQuotes = storeState.fetchQuotes;

          // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
          await fetchQuotes();

          // Show toast notification
          showToast(
            'success',
            'Draft Saved',
            'Your quote draft has been saved',
            { visibilityTime: 2000 }
          );

          // Clear AsyncStorage to prevent issues with future navigation
          try {
            await AsyncStorage.removeItem('currentQuoteId');
            await AsyncStorage.removeItem('currentQuoteType');
            console.log('AsyncStorage cleared');
          } catch (storageError) {
            console.error('Error clearing AsyncStorage:', storageError);
          }

          // Use setTimeout to ensure navigation happens after component is mounted
          setTimeout(() => {
            try {
              // Navigate back to the quotes list
              // Use router.push instead of replace to ensure a fresh load
              router.push('/(app)/quotes' as any);
            } catch (navError) {
              console.error('Navigation error:', navError);
              // Show toast notification
              showToast(
                'error',
                'Navigation Error',
                'Failed to navigate back. Please try again.',
                { visibilityTime: 4000 }
              );
            }
          }, 100);
        } catch (error) {
          console.error('Error saving draft before navigating back:', error);

          // Still navigate back even if save fails
          setTimeout(() => {
            try {
              router.push('/(app)/quotes' as any);
            } catch (navError) {
              console.error('Navigation error:', navError);
            }
          }, 100);
        }
      };

      saveBeforeBack();
    } else {
      // If no current quote, just navigate back
      setTimeout(() => {
        try {
          router.push('/(app)/quotes' as any);
        } catch (navError) {
          console.error('Navigation error:', navError);
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate back. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!currentQuote) return;

    try {
      // Update the quote with the current form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        // Make sure the status is still draft
        status: 'draft',
        // Update the timestamp to ensure it's at the top of the list
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const storeState = useQuoteStore.getState();
      const fetchQuotes = storeState.fetchQuotes;

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Show a toast notification for better user feedback
      showToast(
        'success',
        'Draft Saved',
        'Your quote draft has been saved',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle next
  const handleNext = async () => {
    if (!currentQuote) return;

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      showToast(
        'error',
        'Required Documents',
        'Please upload all required documents before proceeding',
        { visibilityTime: 4000 }
      );
      setActiveTab('documents');
      return;
    }

    try {
      // Calculate premium based on unspecified items value and specified items
      const unspecifiedItemsValue = formData.unspecifiedItemsValue || 0;
      const specifiedItems = formData.specifiedItems || [];

      // Calculate total value of specified items
      const specifiedItemsValue = specifiedItems.reduce(
        (total: number, item: SpecifiedItem) => total + (item.value || 0),
        0
      );

      // Calculate premium using our utility function (assuming it exists)
      const premium = calculateAllRisksPremium(unspecifiedItemsValue, specifiedItemsValue);

      // Calculate total cover amount
      const totalCoverAmount = unspecifiedItemsValue + specifiedItemsValue;

      // Save form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        premium: premium,
        currency: 'P', // Set Botswana currency
        coverAmount: totalCoverAmount,
        // Update the timestamp
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const storeState = useQuoteStore.getState();
      const fetchQuotes = storeState.fetchQuotes;

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Clear AsyncStorage to prevent issues with future navigation
      try {
        await AsyncStorage.removeItem('currentQuoteId');
        await AsyncStorage.removeItem('currentQuoteType');
        console.log('AsyncStorage cleared before navigating to summary');
      } catch (storageError) {
        console.error('Error clearing AsyncStorage:', storageError);
      }

      // Use setTimeout to ensure navigation happens after component is mounted
      setTimeout(() => {
        try {
          // Navigate to the summary page
          router.push({
            pathname: '/(app)/quotes/[type]/summary',
            params: {
              type: 'allRisks',
              quoteId: currentQuote.id
            }
          });
        } catch (navError) {
          console.error('Navigation error:', navError);
          // Show toast notification
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate to summary page. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  // Render all risks insurance form fields
  const renderFormFields = () => {
    return (
      <View>
        <DynamicFormField
          label="Unspecified Items (General Apparel and Effects)"
          type="number"
          value={formData.unspecifiedItemsValue?.toString()}
          onChange={(value) => updateFormField('unspecifiedItemsValue', parseFloat(value) || 0)}
          placeholder="Enter total sum insured"
          error={errors.unspecifiedItemsValue}
          keyboardType="numeric"
          icon={<DollarSign size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Limit per Item"
          type="number"
          value={formData.limitPerItem?.toString()}
          onChange={(value) => updateFormField('limitPerItem', parseFloat(value) || 0)}
          placeholder="Enter limit per item"
          keyboardType="numeric"
          icon={<DollarSign size={20} color={colors.textSecondary} />}
        />

        <Text style={styles.sectionTitle}>Specified Items</Text>
        <Text style={styles.sectionDescription}>
          For high-value items that need individual coverage, please add them below.
          You will be asked to upload proof of purchase and photos later.
        </Text>

        {/* List of specified items */}
        {formData.specifiedItems && formData.specifiedItems.length > 0 ? (
          <View style={styles.itemsContainer}>
            {formData.specifiedItems.map((item: SpecifiedItem) => (
              <View key={item.id} style={styles.itemCard}>
                <View style={styles.itemDetails}>
                  <Text style={styles.itemName}>{item.name}</Text>
                  <Text style={styles.itemDescription}>{item.description}</Text>
                  <Text style={styles.itemValue}>P {item.value.toLocaleString()}</Text>
                </View>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => handleRemoveItem(item.id)}
                >
                  <Trash2 size={20} color={colors.error[500]} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyItemsContainer}>
            <Text style={styles.emptyItemsText}>No specified items added yet</Text>
          </View>
        )}

        {/* Add item form */}
        {isAddingItem ? (
          <View style={styles.addItemForm}>
            <DynamicFormField
              label="Item Name"
              type="text"
              value={currentItem.name}
              onChange={(value) => setCurrentItem({ ...currentItem, name: value })}
              placeholder="e.g., Diamond Ring"
              required
            />

            <DynamicFormField
              label="Item Description"
              type="textarea"
              value={currentItem.description}
              onChange={(value) => setCurrentItem({ ...currentItem, description: value })}
              placeholder="e.g., 18k white gold with 1 carat diamond"
            />

            <DynamicFormField
              label="Item Value"
              type="number"
              value={currentItem.value?.toString()}
              onChange={(value) => setCurrentItem({ ...currentItem, value: parseFloat(value) || 0 })}
              placeholder="e.g., 15000"
              keyboardType="numeric"
              required
              icon={<DollarSign size={20} color={colors.textSecondary} />}
            />

            <View style={styles.addItemButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => {
                  setCurrentItem({ id: '', name: '', description: '', value: 0 });
                  setIsAddingItem(false);
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.addButton]}
                onPress={handleAddItem}
              >
                <Text style={styles.addButtonText}>Add Item</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.addItemButton}
            onPress={() => setIsAddingItem(true)}
          >
            <Plus size={20} color={colors.white} />
            <Text style={styles.addItemButtonText}>Add Specified Item</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Handle document updates
  const handleDocumentsUpdated = (updatedDocuments: QuoteDocument[]) => {
    setDocuments(updatedDocuments);
  };

  // Define styles
  const styles = StyleSheet.create({
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    loadingText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginTop: spacing.md,
      textAlign: 'center',
    },
    tabsContainer: {
      flexDirection: 'row',
      marginBottom: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    tab: {
      flex: 1,
      paddingVertical: spacing.sm,
      alignItems: 'center',
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: colors.primary[500],
    },
    tabText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    activeTabText: {
      color: colors.primary[500],
    },
    contentContainer: {
      flex: 1,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    sectionDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.md,
    },
    itemsContainer: {
      marginBottom: spacing.md,
    },
    itemCard: {
      flexDirection: 'row',
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: spacing.md,
      marginBottom: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    itemDetails: {
      flex: 1,
    },
    itemName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: 2,
    },
    itemDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: 4,
    },
    itemValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.primary[500],
    },
    removeButton: {
      justifyContent: 'center',
      paddingLeft: spacing.md,
    },
    emptyItemsContainer: {
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: spacing.lg,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: spacing.md,
    },
    emptyItemsText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    addItemButton: {
      flexDirection: 'row',
      backgroundColor: colors.primary[500],
      borderRadius: 8,
      padding: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: spacing.sm,
    },
    addItemButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    addItemForm: {
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: spacing.md,
      marginTop: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    addItemButtons: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.md,
    },
    button: {
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: 8,
      marginLeft: spacing.sm,
    },
    cancelButton: {
      backgroundColor: colors.neutral[200],
    },
    cancelButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    addButton: {
      backgroundColor: colors.primary[500],
    },
    addButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
  });

  // Show loading indicator while checking AsyncStorage
  if (isStorageLoading || isQuoteLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading quote data...</Text>
      </View>
    );
  }

  return (
    <QuoteFormContainer
      title="All Risks Insurance Details"
      subtitle="Please provide details for your all risks insurance quote"
      steps={steps}
      currentStep={1}
      completedSteps={[0]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isQuoteLoading}
      nextDisabled={!formValid}
    >
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'details' && styles.activeTab
          ]}
          onPress={() => setActiveTab('details')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'details' && styles.activeTabText
          ]}>
            Items Details
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'documents' && styles.activeTab
          ]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'documents' && styles.activeTabText
          ]}>
            Required Documents
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        {/* Render both components but only show the active one */}
        <View style={{ display: activeTab === 'details' ? 'flex' : 'none' }}>
          {renderFormFields()}
        </View>

        <View style={{ display: activeTab === 'documents' ? 'flex' : 'none' }}>
          <QuoteDocumentsSection
            quoteType="allRisks"
            documents={documents}
            onDocumentsUpdated={handleDocumentsUpdated}
          />
        </View>
      </View>
    </QuoteFormContainer>
  );
}
