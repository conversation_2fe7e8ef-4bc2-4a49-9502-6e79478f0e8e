import { store } from '@/store/store';
import { updateFlowStage, ApplicationFlowStage } from '@/store/applicationFlowSlice';
import { addNotification } from '@/store/notificationSlice';
import notificationService from './notificationService';

/**
 * Service to handle automated application flow progression and notifications
 */
class ApplicationFlowService {
  private timers: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Start the automated flow for a given application
   */
  async startAutomatedFlow(flowId: string): Promise<void> {
    console.log('[ApplicationFlowService] Starting automated flow for:', flowId);
    
    // Start document verification process
    this.scheduleStageUpdate(flowId, 'documents_under_review', 'documents_verified', {
      delay: this.getRandomDelay(10000, 20000), // 10-20 seconds
      notificationTitle: 'Documents Verified ✅',
      notificationMessage: 'Your documents have been verified and approved.',
    });
  }

  /**
   * Schedule a stage update with notification
   */
  private scheduleStageUpdate(
    flowId: string,
    currentStage: ApplicationFlowStage,
    nextStage: ApplicationFlowStage,
    options: {
      delay: number;
      notificationTitle: string;
      notificationMessage: string;
      onComplete?: () => void;
    }
  ): void {
    const timerId = `${flowId}-${currentStage}-${nextStage}`;
    
    // Clear any existing timer
    if (this.timers.has(timerId)) {
      clearTimeout(this.timers.get(timerId)!);
    }

    const timer = setTimeout(async () => {
      try {
        // Update the flow stage
        await store.dispatch(updateFlowStage({
          flowId,
          stage: nextStage,
          completed: true,
          progress: 100,
        }));

        // Send notification
        await store.dispatch(addNotification({
          title: options.notificationTitle,
          message: options.notificationMessage,
          type: 'success',
          read: false,
          category: 'application',
          priority: 'medium',
          actionRoute: `/applications/flow/${flowId}`,
          actionLabel: 'View Progress',
          actionData: { flowId, stage: nextStage },
        }));

        // Send platform notification
        await notificationService.sendLocalNotification(
          options.notificationTitle,
          options.notificationMessage,
          { flowId, stage: nextStage }
        );

        // Schedule next stage if applicable
        this.scheduleNextStage(flowId, nextStage);

        // Execute completion callback
        if (options.onComplete) {
          options.onComplete();
        }

        // Clean up timer
        this.timers.delete(timerId);
      } catch (error) {
        console.error('[ApplicationFlowService] Error updating stage:', error);
        this.timers.delete(timerId);
      }
    }, options.delay);

    this.timers.set(timerId, timer);
  }

  /**
   * Schedule the next stage in the workflow
   */
  private scheduleNextStage(flowId: string, currentStage: ApplicationFlowStage): void {
    switch (currentStage) {
      case 'documents_verified':
        // Enable quote generation
        this.scheduleStageUpdate(flowId, 'documents_verified', 'quote_available', {
          delay: 1000, // Immediate
          notificationTitle: 'Quote Available 📋',
          notificationMessage: 'Your quote is now available for review and acceptance.',
        });
        break;

      case 'quote_available':
        // Auto-generate quote (in real app, this would wait for user action)
        this.scheduleStageUpdate(flowId, 'quote_available', 'quote_generated', {
          delay: 2000, // 2 seconds
          notificationTitle: 'Quote Generated 📋',
          notificationMessage: 'Your insurance quote has been generated and is ready for review.',
        });
        break;

      case 'quote_accepted':
        // Bind policy
        this.scheduleStageUpdate(flowId, 'quote_accepted', 'policy_bound', {
          delay: 3000, // 3 seconds
          notificationTitle: 'Policy Bound 🎉',
          notificationMessage: 'Your policy has been bound and coverage is now active.',
        });
        break;

      case 'policy_bound':
        // Generate invoice
        this.scheduleStageUpdate(flowId, 'policy_bound', 'invoice_generated', {
          delay: 2000, // 2 seconds
          notificationTitle: 'Invoice Generated 💰',
          notificationMessage: 'Your payment invoice has been generated.',
        });
        break;

      case 'invoice_generated':
        // Deliver invoice
        this.scheduleStageUpdate(flowId, 'invoice_generated', 'invoice_delivered', {
          delay: 1000, // 1 second
          notificationTitle: 'Invoice Delivered 📧',
          notificationMessage: 'Your payment invoice has been delivered. Please proceed with payment.',
        });
        break;

      case 'payment_uploaded':
        // Verify payment
        this.scheduleStageUpdate(flowId, 'payment_uploaded', 'payment_under_review', {
          delay: 1000, // 1 second
          notificationTitle: 'Payment Under Review 🔍',
          notificationMessage: 'Your payment is being verified.',
        });
        break;

      case 'payment_under_review':
        // Complete payment verification
        this.scheduleStageUpdate(flowId, 'payment_under_review', 'payment_verified', {
          delay: this.getRandomDelay(10000, 20000), // 10-20 seconds
          notificationTitle: 'Payment Verified ✅',
          notificationMessage: 'Your payment has been verified and processed.',
        });
        break;

      case 'payment_verified':
        // Reconcile payment
        this.scheduleStageUpdate(flowId, 'payment_verified', 'payment_reconciled', {
          delay: 2000, // 2 seconds
          notificationTitle: 'Payment Reconciled 💳',
          notificationMessage: 'Your payment has been reconciled and processed.',
        });
        break;

      case 'payment_reconciled':
        // Issue policy
        this.scheduleStageUpdate(flowId, 'payment_reconciled', 'policy_issued', {
          delay: 3000, // 3 seconds
          notificationTitle: 'Policy Issued 📄',
          notificationMessage: 'Your policy documents have been issued.',
        });
        break;

      case 'policy_issued':
        // Review policy
        this.scheduleStageUpdate(flowId, 'policy_issued', 'policy_under_review', {
          delay: 5000, // 5 seconds
          notificationTitle: 'Policy Under Review 🔍',
          notificationMessage: 'Your policy is undergoing final review.',
        });
        break;

      case 'policy_under_review':
        // Dispatch policy
        this.scheduleStageUpdate(flowId, 'policy_under_review', 'policy_dispatched', {
          delay: this.getRandomDelay(10000, 20000), // 10-20 seconds
          notificationTitle: 'Policy Dispatched 🎉',
          notificationMessage: 'Your policy has been dispatched and is now available for download.',
        });
        break;

      default:
        // No further stages
        break;
    }
  }

  /**
   * Handle user actions that trigger stage updates
   */
  async handleUserAction(flowId: string, action: string, data?: any): Promise<void> {
    console.log('[ApplicationFlowService] Handling user action:', action, 'for flow:', flowId);

    switch (action) {
      case 'accept_quote':
        await store.dispatch(updateFlowStage({
          flowId,
          stage: 'quote_accepted',
          completed: true,
          progress: 100,
        }));
        this.scheduleNextStage(flowId, 'quote_accepted');
        break;

      case 'upload_payment':
        await store.dispatch(updateFlowStage({
          flowId,
          stage: 'payment_uploaded',
          completed: true,
          progress: 100,
          notes: `Payment proof uploaded: ${data?.reference || 'N/A'}`,
        }));
        this.scheduleNextStage(flowId, 'payment_uploaded');
        break;

      default:
        console.warn('[ApplicationFlowService] Unknown user action:', action);
        break;
    }
  }

  /**
   * Get a random delay between min and max milliseconds
   */
  private getRandomDelay(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Cancel all timers for a specific flow
   */
  cancelFlow(flowId: string): void {
    console.log('[ApplicationFlowService] Cancelling flow:', flowId);
    
    for (const [timerId, timer] of this.timers.entries()) {
      if (timerId.startsWith(flowId)) {
        clearTimeout(timer);
        this.timers.delete(timerId);
      }
    }
  }

  /**
   * Cancel all active timers
   */
  cancelAllFlows(): void {
    console.log('[ApplicationFlowService] Cancelling all flows');
    
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();
  }

  /**
   * Get active timers count
   */
  getActiveTimersCount(): number {
    return this.timers.size;
  }
}

// Export singleton instance
export const applicationFlowService = new ApplicationFlowService();
export default applicationFlowService;
