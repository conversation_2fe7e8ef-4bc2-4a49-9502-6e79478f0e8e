import { View, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import Animated, { useAnimatedStyle, withTiming, interpolateColor } from 'react-native-reanimated';
import { useTheme } from '@/context/ThemeContext';

export function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const { isDarkMode } = useTheme();
  const { colors, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(colors);
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = options.title !== undefined ? options.title : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        const animatedIconStyle = useAnimatedStyle(() => {
          return {
            opacity: withTiming(isFocused ? 1 : 0.7, { duration: 200 }),
            transform: [{
              scale: withTiming(isFocused ? 1 : 0.9, { duration: 200 })
            }],
          };
        });

        const animatedLabelStyle = useAnimatedStyle(() => {
          return {
            opacity: withTiming(isFocused ? 1 : 0.7, { duration: 200 }),
            color: interpolateColor(
              withTiming(isFocused ? 1 : 0, { duration: 200 }),
              [0, 1],
              [colors.textSecondary, colors.primary[500]]
            ),
          };
        });

        return (
          <TouchableOpacity
            key={index}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={styles.tabButton}
          >
            <Animated.View style={[styles.iconContainer, animatedIconStyle]}>
              {options.tabBarIcon &&
                options.tabBarIcon({
                  focused: isFocused,
                  color: isFocused ? colors.primary[500] : colors.textSecondary,
                  size: 24
                })
              }
            </Animated.View>

            <Animated.Text style={[styles.label, animatedLabelStyle]}>
              {label}
            </Animated.Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

// Define styles with a function to access theme values
const getStyles = (colors: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderTopWidth: 0,
    height: Platform.OS === 'ios' ? 88 : 60,
    paddingBottom: Platform.OS === 'ios' ? 28 : 8,
    paddingTop: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});