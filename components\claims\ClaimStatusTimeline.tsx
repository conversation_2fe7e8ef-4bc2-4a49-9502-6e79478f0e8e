import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ClaimTimelineEvent } from '@/types/claim.types';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  Search,
  DollarSign,
  ChevronRight,
  FileQuestion,
  XCircle
} from 'lucide-react-native';

interface ClaimStatusTimelineProps {
  events: ClaimTimelineEvent[];
  onActionPress?: (action: string) => void;
}

const ClaimStatusTimeline: React.FC<ClaimStatusTimelineProps> = ({
  events,
  onActionPress
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const styles = StyleSheet.create({
    container: {
      marginVertical: spacing.md,
    },
    eventContainer: {
      flexDirection: 'row',
      marginBottom: spacing.md,
    },
    timelineBar: {
      width: 2,
      backgroundColor: colors.neutral[300],
      marginHorizontal: spacing.md,
      marginTop: 30,
      flex: 0,
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    contentContainer: {
      flex: 1,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    description: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    date: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginBottom: spacing.sm,
    },
    details: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      backgroundColor: colors.neutral[100],
      padding: spacing.sm,
      borderRadius: borders.radius.md,
      marginBottom: spacing.sm,
    },
    actionsContainer: {
      marginTop: spacing.xs,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
      alignSelf: 'flex-start',
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
      marginRight: spacing.xs,
    },
  });

  // Get icon for timeline event
  const getEventIcon = (event: ClaimTimelineEvent) => {
    const iconName = event.icon || '';
    const iconSize = 16;
    
    let iconColor;
    let backgroundColor;
    
    switch (event.status) {
      case 'completed':
        iconColor = colors.white;
        backgroundColor = colors.success[500];
        break;
      case 'current':
        iconColor = colors.white;
        backgroundColor = colors.primary[500];
        break;
      case 'upcoming':
        iconColor = colors.neutral[400];
        backgroundColor = colors.neutral[200];
        break;
      default:
        iconColor = colors.neutral[400];
        backgroundColor = colors.neutral[200];
    }
    
    let icon;
    switch (iconName) {
      case 'check-circle':
        icon = <CheckCircle size={iconSize} color={iconColor} />;
        break;
      case 'file-text':
        icon = <FileText size={iconSize} color={iconColor} />;
        break;
      case 'upload':
        icon = <FileText size={iconSize} color={iconColor} />;
        break;
      case 'clipboard':
        icon = <Search size={iconSize} color={iconColor} />;
        break;
      case 'search':
        icon = <Search size={iconSize} color={iconColor} />;
        break;
      case 'credit-card':
        icon = <DollarSign size={iconSize} color={iconColor} />;
        break;
      case 'alert-circle':
        icon = <AlertCircle size={iconSize} color={iconColor} />;
        break;
      case 'file-question':
        icon = <FileQuestion size={iconSize} color={iconColor} />;
        break;
      case 'x-circle':
        icon = <XCircle size={iconSize} color={iconColor} />;
        break;
      default:
        icon = <Clock size={iconSize} color={iconColor} />;
    }
    
    return (
      <View style={[styles.iconContainer, { backgroundColor }]}>
        {icon}
      </View>
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Sort events by their order in the timeline
  const sortedEvents = [...events].sort((a, b) => {
    // First by status (completed, current, upcoming)
    const statusOrder = { completed: 0, current: 1, upcoming: 2 };
    const statusDiff = statusOrder[a.status] - statusOrder[b.status];
    if (statusDiff !== 0) return statusDiff;
    
    // Then by date (if available)
    if (a.date && b.date) {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    }
    
    // Finally by ID (assuming IDs are sequential)
    return parseInt(a.id) - parseInt(b.id);
  });

  return (
    <View style={styles.container}>
      {sortedEvents.map((event, index) => {
        const isLast = index === sortedEvents.length - 1;
        
        return (
          <View key={event.id} style={styles.eventContainer}>
            {getEventIcon(event)}
            
            {!isLast && (
              <View style={[
                styles.timelineBar,
                { 
                  height: event.details || event.actions ? 120 : 60,
                  backgroundColor: event.status === 'upcoming' ? colors.neutral[200] : colors.neutral[300]
                }
              ]} />
            )}
            
            <View style={styles.contentContainer}>
              <Text style={[
                styles.title,
                event.status === 'upcoming' && { color: colors.textSecondary }
              ]}>
                {event.title}
              </Text>
              
              <Text style={styles.description}>
                {event.description}
              </Text>
              
              {event.date && (
                <Text style={styles.date}>
                  {formatDate(event.date)}
                </Text>
              )}
              
              {event.details && (
                <Text style={styles.details}>
                  {event.details}
                </Text>
              )}
              
              {event.status === 'current' && event.actions && event.actions.length > 0 && (
                <View style={styles.actionsContainer}>
                  {event.actions.map((action, actionIndex) => (
                    <TouchableOpacity
                      key={actionIndex}
                      style={styles.actionButton}
                      onPress={() => onActionPress && onActionPress(action.action)}
                    >
                      <Text style={styles.actionButtonText}>{action.label}</Text>
                      <ChevronRight size={16} color={colors.white} />
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>
        );
      })}
    </View>
  );
};

export default ClaimStatusTimeline;
