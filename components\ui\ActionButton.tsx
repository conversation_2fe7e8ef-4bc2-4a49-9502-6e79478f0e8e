import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

type ActionButtonProps = {
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  onPress: () => void;
};

export default function ActionButton({ title, icon: Icon, color, onPress }: ActionButtonProps) {
  const { isDarkMode } = useTheme();
  const { colors: themeColors, spacing, typography, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(spacing, typography, borders);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
        <Icon size={20} color={color} />
      </View>
      <Text style={[styles.title, { color: themeColors.text }]}>{title}</Text>
    </TouchableOpacity>
  );
}

// Define styles with a function to access theme values
const getStyles = (spacing, typography, borders) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 65,
    marginBottom: spacing.sm,
  },
  iconContainer: {
    width: 45,
    height: 45,
    borderRadius: borders.radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  title: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.xs,
    textAlign: 'center',
    maxWidth: 65,
  },
});