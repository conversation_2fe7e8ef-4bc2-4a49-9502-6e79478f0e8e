import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { Sun, Moon, Monitor, ChevronDown } from 'lucide-react-native';

export default function ThemeToggle() {
  const [isOpen, setIsOpen] = useState(false);
  const { theme, setTheme, isDarkMode } = useTheme();
  const currentTheme = createTheme(isDarkMode);
  const { colors, typography, spacing, borders } = currentTheme;

  const options = [
    { value: 'light', icon: Sun, label: 'Light' },
    { value: 'system', icon: Monitor, label: 'System' },
    { value: 'dark', icon: Moon, label: 'Dark' },
  ];

  // No need for useEffect here as we're using Modal component

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleSelect = (value: string) => {
    setTheme(value as 'light' | 'dark' | 'system');
    setIsOpen(false);
  };

  // Get current theme icon
  const getCurrentThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun size={20} color={colors.primary[500]} />;
      case 'dark':
        return <Moon size={20} color={colors.primary[500]} />;
      case 'system':
        return <Monitor size={20} color={colors.primary[500]} />;
      default:
        return <Monitor size={20} color={colors.primary[500]} />;
    }
  };

  return (
    <View style={styles.container}>
      {/* Selected option button */}
      <TouchableOpacity
        style={[
          styles.selectedOption,
          {
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }
        ]}
        onPress={toggleDropdown}
        activeOpacity={0.7}
      >
        <View style={styles.selectedOptionContent}>
          {getCurrentThemeIcon()}
          <Text
            style={[
              styles.selectedOptionText,
              {
                color: colors.text,
                fontFamily: typography.fonts.medium,
              }
            ]}
          >
            {theme.charAt(0).toUpperCase() + theme.slice(1)}
          </Text>
        </View>
        <ChevronDown
          size={16}
          color={colors.textSecondary}
          style={{
            transform: [{ rotate: isOpen ? '180deg' : '0deg' }],
            marginLeft: spacing.xs
          }}
        />
      </TouchableOpacity>

      {/* Use Modal for dropdown to ensure it's not clipped */}
      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsOpen(false)}
        >
          <View
            style={[
              styles.dropdown,
              {
                backgroundColor: colors.card,
                borderColor: colors.border,
                width: 180, // Same width as selected option
              }
            ]}
          >
            {options.map((option) => {
              const isSelected = theme === option.value;
              const Icon = option.icon;

              return (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.option,
                    isSelected && { backgroundColor: `${colors.primary[500]}15` },
                  ]}
                  onPress={() => handleSelect(option.value)}
                >
                  <Icon
                    size={20}
                    color={isSelected ? colors.primary[500] : colors.textSecondary}
                  />
                  <Text
                    style={[
                      styles.optionText,
                      {
                        color: isSelected ? colors.primary[500] : colors.text,
                        fontFamily: typography.fonts.medium,
                      },
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  selectedOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 120,
  },
  selectedOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedOptionText: {
    fontSize: 14,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  dropdown: {
    position: 'absolute',
    top: 100,
    right: 20,
    borderWidth: 1,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 8,
    minWidth: 140,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 10,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
