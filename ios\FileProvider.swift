import Foundation
import UIKit
import MobileCoreServices

@objc(FileProvider)
class FileProvider: NSObject {
  
  @objc
  func getUTIForMimeType(_ mimeType: String) -> String {
    switch mimeType {
    case "application/pdf":
      return kUTTypePDF as String
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      return "org.openxmlformats.wordprocessingml.document"
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      return "org.openxmlformats.spreadsheetml.sheet"
    case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
      return "org.openxmlformats.presentationml.presentation"
    case "text/plain":
      return kUTTypePlainText as String
    case "text/rtf":
      return kUTTypeRTF as String
    case "image/jpeg":
      return kUTTypeJPEG as String
    case "image/png":
      return kUTTypePNG as String
    case "image/gif":
      return kUTTypeGIF as String
    default:
      return kUTTypeData as String
    }
  }
  
  @objc
  func openFile(_ filePath: String, mimeType: String, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    DispatchQueue.main.async {
      let fileURL = URL(fileURLWithPath: filePath)
      let documentInteractionController = UIDocumentInteractionController(url: fileURL)
      documentInteractionController.uti = self.getUTIForMimeType(mimeType)
      
      let rootViewController = UIApplication.shared.keyWindow?.rootViewController
      if documentInteractionController.presentPreview(animated: true) {
        resolver(true)
      } else if documentInteractionController.presentOpenInMenu(from: CGRect.zero, in: rootViewController!.view, animated: true) {
        resolver(true)
      } else {
        rejecter("ERROR", "Could not open file", nil)
      }
    }
  }
}
