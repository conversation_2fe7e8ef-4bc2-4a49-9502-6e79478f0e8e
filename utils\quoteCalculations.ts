// Quote calculation utility functions

// Life assurance rates
const LIFE_ASSURANCE_RATES = {
  baseRate: 0.0035, // Base rate per 1000 of coverage
  smokerMultiplier: 1.5, // Multiplier for smokers
  ageFactors: {
    // Age brackets and their rate multipliers
    '18-30': 0.8,
    '31-40': 1.0,
    '41-50': 1.3,
    '51-60': 1.8,
    '61-70': 2.5,
    '71+': 3.5
  },
  termFactors: {
    // Term length factors
    '5': 0.9,
    '10': 1.0,
    '15': 1.1,
    '20': 1.2,
    '25': 1.3,
    '30': 1.4
  },
  occupationRiskFactors: {
    // Occupation risk categories and their multipliers
    'low': 0.9,    // Office workers, teachers, etc.
    'medium': 1.0, // Default - most occupations
    'high': 1.3,   // Construction, manufacturing, etc.
    'very_high': 1.8 // Mining, offshore oil, etc.
  },
  healthConditionFactors: {
    // Health condition risk factors
    'none': 1.0,
    'minor': 1.2,  // Minor conditions like controlled hypertension
    'moderate': 1.5, // Moderate conditions like type 2 diabetes
    'severe': 2.0,  // Severe conditions like heart disease
    'critical': 3.0  // Critical conditions like cancer
  },
  minimumPremium: 1000
};

// Motor insurance rates
const MOTOR_RATES = {
  local: {
    'Toyota': 0.03,
    'Mercedes-Benz': 0.0335,
    'BMW': 0.0335,
    'Volkswagen': 0.0335,
    'Audi': 0.0335,
    'default': 0.035
  },
  greyImport: {
    'Toyota': 0.05,
    'default': 0.065
  },
  minimumPremium: 3000
};

// Pleasure craft rates
const PLEASURE_CRAFT_RATES = {
  'Standard Hull': 0.0425,
  'Non-Standard Hull': 0.085,
  'Jet Ski': 0.10
};

// Bundled product rates (excluding Motor and Pleasure Craft)
const BUNDLED_PRODUCT_RATES = {
  tier1: {
    houseContents: 100000,
    allRisk: 50000,
    cyber: 'Tera-family',
    premium: 2875
  },
  tier2: {
    houseContents: 250000,
    allRisk: 100000,
    cyber: 'Tera-family',
    premium: 5875
  },
  tier3: {
    houseContents: 500000,
    allRisk: 250000,
    cyber: 'Tera-family',
    premium: 13375
  }
};

// House contents, all risk, and cyber rates
const HOUSE_CONTENTS_RATES = {
  tier1: {
    sumInsured: 100000,
    houseContentsRate: 0.0075,
    allRiskRate: 0.04,
    cyber: 'Tera-family',
    premium: 750
  },
  tier2: {
    sumInsured: 250000,
    houseContentsRate: 0.007,
    allRiskRate: 0.04,
    cyber: 'Tera-family',
    premium: 1750
  },
  tier3: {
    sumInsured: 500000,
    houseContentsRate: 0.0065,
    allRiskRate: 0.04,
    cyber: 'Tera-family',
    premium: 3250
  }
};

// Homeowners rates
const HOMEOWNERS_RATES = {
  tier1: {
    maxValue: 1000000,
    rate: 0.00075,
    premium: 750
  },
  tier2: {
    maxValue: 2500000,
    rate: 0.0007,
    premium: 1750
  },
  tier3: {
    maxValue: 5000000,
    rate: 0.00065,
    premium: 3250
  },
  tier4: {
    maxValue: 10000000,
    rate: 0.0006,
    premium: 6000
  },
  thatched: {
    rate: 0.0065
  }
};

// Extensions and additional features
const EXTENSIONS = {
  motor: {
    towingAndStorage: 5000,
    lossOfKeys: 5000,
    carHire: {
      perDay: 100,
      maxTotal: 500
    },
    freeValuation: true,
    fleetDiscount: {
      minVehicles: 4,
      discountRate: 0.05
    }
  }
};

/**
 * Calculate motor insurance premium
 * @param vehicleValue The value of the vehicle
 * @param brand The brand of the vehicle
 * @param isGreyImport Whether the vehicle is a grey import
 * @param numberOfVehicles Number of vehicles being insured (for fleet discount)
 * @returns The calculated premium
 */
export const calculateMotorPremium = (
  vehicleValue: number,
  brand: string,
  isGreyImport: boolean = false,
  numberOfVehicles: number = 1
): number => {
  // Determine which rate table to use
  const rateTable = isGreyImport ? MOTOR_RATES.greyImport : MOTOR_RATES.local;

  // Get the rate for the brand, or use default if not found
  const rate = rateTable[brand as keyof typeof rateTable] || rateTable.default;

  // Calculate base premium
  let premium = vehicleValue * rate;

  // Apply fleet discount if applicable
  if (numberOfVehicles >= EXTENSIONS.motor.fleetDiscount.minVehicles) {
    premium = premium * (1 - EXTENSIONS.motor.fleetDiscount.discountRate);
  }

  // Ensure minimum premium
  premium = Math.max(premium, MOTOR_RATES.minimumPremium);

  // Round to 2 decimal places
  return Math.round(premium * 100) / 100;
};

/**
 * Calculate pleasure craft premium
 * @param craftValue The value of the craft
 * @param craftType The type of craft
 * @returns The calculated premium
 */
export const calculatePleasureCraftPremium = (
  craftValue: number,
  craftType: string
): number => {
  // Get the rate for the craft type, or use Standard Hull as default
  const rate = PLEASURE_CRAFT_RATES[craftType as keyof typeof PLEASURE_CRAFT_RATES] || PLEASURE_CRAFT_RATES['Standard Hull'];

  // Calculate premium
  const premium = craftValue * rate;

  // Round to 2 decimal places
  return Math.round(premium * 100) / 100;
};

/**
 * Calculate house contents premium
 * @param contentsValue The value of the house contents
 * @returns The calculated premium and rate tier
 */
export const calculateHouseContentsPremium = (
  contentsValue: number
): { premium: number; tier: string } => {
  let tier;
  let premium;

  if (contentsValue <= HOUSE_CONTENTS_RATES.tier1.sumInsured) {
    tier = 'tier1';
    premium = contentsValue * HOUSE_CONTENTS_RATES.tier1.houseContentsRate;
  } else if (contentsValue <= HOUSE_CONTENTS_RATES.tier2.sumInsured) {
    tier = 'tier2';
    premium = contentsValue * HOUSE_CONTENTS_RATES.tier2.houseContentsRate;
  } else {
    tier = 'tier3';
    premium = contentsValue * HOUSE_CONTENTS_RATES.tier3.houseContentsRate;
  }

  // Round to 2 decimal places
  return {
    premium: Math.round(premium * 100) / 100,
    tier
  };
};

/**
 * Calculate homeowners premium
 * @param buildingValue The value of the building
 * @param isThatched Whether the building has a thatched roof
 * @returns The calculated premium
 */
export const calculateHomeownersPremium = (
  buildingValue: number,
  isThatched: boolean = false
): number => {
  let rate;

  if (isThatched) {
    rate = HOMEOWNERS_RATES.thatched.rate;
  } else if (buildingValue <= HOMEOWNERS_RATES.tier1.maxValue) {
    rate = HOMEOWNERS_RATES.tier1.rate;
  } else if (buildingValue <= HOMEOWNERS_RATES.tier2.maxValue) {
    rate = HOMEOWNERS_RATES.tier2.rate;
  } else if (buildingValue <= HOMEOWNERS_RATES.tier3.maxValue) {
    rate = HOMEOWNERS_RATES.tier3.rate;
  } else {
    rate = HOMEOWNERS_RATES.tier4.rate;
  }

  // Calculate premium
  const premium = buildingValue * rate;

  // Round to 2 decimal places
  return Math.round(premium * 100) / 100;
};

/**
 * Simple function to format a number with 2 decimal places
 * @param amount The number to format
 * @returns Formatted number string with 2 decimal places
 */
const formatNumberWithDecimals = (amount: number): string => {
  // Convert to fixed 2 decimal places
  const fixedAmount = amount.toFixed(2);

  // Split into whole and decimal parts
  const parts = fixedAmount.split('.');
  const wholePart = parts[0];
  const decimalPart = parts[1] || '00';

  // Add commas to the whole part
  const wholeWithCommas = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Combine with decimal part
  return `${wholeWithCommas}.${decimalPart}`;
};

/**
 * Format currency in Botswana Pula
 * @param amount The amount to format
 * @returns Formatted currency string
 */
export const formatPula = (amount: number): string => {
  return `P ${formatNumberWithDecimals(amount)}`;
};

/**
 * Format currency with custom currency code
 * @param amount The amount to format
 * @param currency The currency code
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number | undefined, currency: string = 'P'): string => {
  if (amount === undefined) return 'N/A';

  // Format the number with our custom formatter
  const formattedNumber = formatNumberWithDecimals(amount);

  // Add the currency symbol/code
  return `${currency} ${formattedNumber}`;
};

/**
 * Calculate life assurance premium
 * @param coverageAmount The coverage amount
 * @param dateOfBirth Date of birth in YYYY-MM-DD format
 * @param smoker Whether the insured person is a smoker
 * @param term Term length in years
 * @param occupationRisk Occupation risk level ('low', 'medium', 'high', 'very_high')
 * @param healthConditions Array of health conditions or a single health risk level
 * @returns The calculated premium and breakdown
 */
export const calculateLifeAssurancePremium = (
  coverageAmount: number,
  dateOfBirth: string,
  smoker: boolean = false,
  term: number = 10,
  occupationRisk: string = 'medium',
  healthConditions: string[] | string = 'none'
): { premium: number; breakdown: Record<string, number> } => {
  // Calculate age from date of birth
  const birthDate = new Date(dateOfBirth);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  // Determine age factor
  let ageFactor;
  if (age <= 30) {
    ageFactor = LIFE_ASSURANCE_RATES.ageFactors['18-30'];
  } else if (age <= 40) {
    ageFactor = LIFE_ASSURANCE_RATES.ageFactors['31-40'];
  } else if (age <= 50) {
    ageFactor = LIFE_ASSURANCE_RATES.ageFactors['41-50'];
  } else if (age <= 60) {
    ageFactor = LIFE_ASSURANCE_RATES.ageFactors['51-60'];
  } else if (age <= 70) {
    ageFactor = LIFE_ASSURANCE_RATES.ageFactors['61-70'];
  } else {
    ageFactor = LIFE_ASSURANCE_RATES.ageFactors['71+'];
  }

  // Determine term factor
  const termKey = term.toString() as keyof typeof LIFE_ASSURANCE_RATES.termFactors;
  const termFactor = LIFE_ASSURANCE_RATES.termFactors[termKey] || LIFE_ASSURANCE_RATES.termFactors['10'];

  // Determine occupation risk factor
  const occupationKey = occupationRisk as keyof typeof LIFE_ASSURANCE_RATES.occupationRiskFactors;
  const occupationFactor = LIFE_ASSURANCE_RATES.occupationRiskFactors[occupationKey] || LIFE_ASSURANCE_RATES.occupationRiskFactors.medium;

  // Determine health condition factor
  let healthFactor = 1.0;
  if (typeof healthConditions === 'string') {
    // If a single health risk level is provided
    const healthKey = healthConditions as keyof typeof LIFE_ASSURANCE_RATES.healthConditionFactors;
    healthFactor = LIFE_ASSURANCE_RATES.healthConditionFactors[healthKey] || LIFE_ASSURANCE_RATES.healthConditionFactors.none;
  } else if (Array.isArray(healthConditions) && healthConditions.length > 0) {
    // If an array of health conditions is provided, use the highest risk factor
    let highestRisk = 'none';
    const riskLevels = ['none', 'minor', 'moderate', 'severe', 'critical'];

    for (const condition of healthConditions) {
      const conditionRisk = condition.toLowerCase().includes('critical') ? 'critical' :
                           condition.toLowerCase().includes('severe') ? 'severe' :
                           condition.toLowerCase().includes('moderate') ? 'moderate' :
                           condition.toLowerCase().includes('minor') ? 'minor' : 'none';

      const currentRiskIndex = riskLevels.indexOf(highestRisk);
      const newRiskIndex = riskLevels.indexOf(conditionRisk);

      if (newRiskIndex > currentRiskIndex) {
        highestRisk = conditionRisk;
      }
    }

    const healthKey = highestRisk as keyof typeof LIFE_ASSURANCE_RATES.healthConditionFactors;
    healthFactor = LIFE_ASSURANCE_RATES.healthConditionFactors[healthKey];
  }

  // Calculate base premium
  const basePremium = (coverageAmount / 1000) * LIFE_ASSURANCE_RATES.baseRate;

  // Apply factors one by one to track the breakdown
  const ageAdjustment = basePremium * ageFactor - basePremium;
  let currentPremium = basePremium + ageAdjustment;

  const termAdjustment = currentPremium * termFactor - currentPremium;
  currentPremium += termAdjustment;

  const occupationAdjustment = currentPremium * occupationFactor - currentPremium;
  currentPremium += occupationAdjustment;

  const healthAdjustment = currentPremium * healthFactor - currentPremium;
  currentPremium += healthAdjustment;

  // Apply smoker multiplier if applicable
  let smokerAdjustment = 0;
  if (smoker) {
    smokerAdjustment = currentPremium * LIFE_ASSURANCE_RATES.smokerMultiplier - currentPremium;
    currentPremium += smokerAdjustment;
  }

  // Ensure minimum premium
  const finalPremium = Math.max(currentPremium, LIFE_ASSURANCE_RATES.minimumPremium);

  // Create premium breakdown
  const breakdown = {
    basePremium: Math.round(basePremium * 100) / 100,
    ageAdjustment: Math.round(ageAdjustment * 100) / 100,
    termAdjustment: Math.round(termAdjustment * 100) / 100,
    occupationAdjustment: Math.round(occupationAdjustment * 100) / 100,
    healthAdjustment: Math.round(healthAdjustment * 100) / 100,
    smokerAdjustment: Math.round(smokerAdjustment * 100) / 100,
    totalPremium: Math.round(finalPremium * 100) / 100
  };

  // Round to 2 decimal places
  return {
    premium: Math.round(finalPremium * 100) / 100,
    breakdown
  };
};
