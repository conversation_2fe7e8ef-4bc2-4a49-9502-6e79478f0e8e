import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator,
  TextInput
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { 
  Claim, 
  ClaimStatus, 
  CLAIM_TYPE_DISPLAY_NAMES 
} from '@/types/claim.types';
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  FileText, 
  DollarSign,
  ChevronRight,
  Search,
  Filter,
  SortDesc,
  SortAsc
} from 'lucide-react-native';
import { formatCurrency } from '@/utils/quoteCalculations';
import { format, parseISO } from 'date-fns';
import Animated, { 
  FadeInDown,
  FadeIn
} from 'react-native-reanimated';
import { router } from 'expo-router';

interface ClaimHistoryViewProps {
  claims: Claim[];
  isLoading: boolean;
  onFilterChange?: (filters: ClaimFilters) => void;
}

interface ClaimFilters {
  status?: ClaimStatus[];
  type?: string[];
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  searchQuery?: string;
}

const ClaimHistoryView: React.FC<ClaimHistoryViewProps> = ({
  claims,
  isLoading,
  onFilterChange
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  
  // State
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [filters, setFilters] = useState<ClaimFilters>({});
  const [sortBy, setSortBy] = useState<'date' | 'amount'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>(claims);
  
  // Update filtered claims when claims or filters change
  useEffect(() => {
    let result = [...claims];
    
    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(claim => 
        claim.reference.toLowerCase().includes(query) ||
        claim.policyNumber.toLowerCase().includes(query) ||
        CLAIM_TYPE_DISPLAY_NAMES[claim.type].toLowerCase().includes(query)
      );
    }
    
    // Apply status filters
    if (filters.status && filters.status.length > 0) {
      result = result.filter(claim => filters.status?.includes(claim.status));
    }
    
    // Apply type filters
    if (filters.type && filters.type.length > 0) {
      result = result.filter(claim => filters.type?.includes(claim.type));
    }
    
    // Apply date range filters
    if (filters.dateRange) {
      if (filters.dateRange.start) {
        result = result.filter(claim => {
          const claimDate = new Date(claim.date);
          return claimDate >= filters.dateRange!.start!;
        });
      }
      
      if (filters.dateRange.end) {
        result = result.filter(claim => {
          const claimDate = new Date(claim.date);
          return claimDate <= filters.dateRange!.end!;
        });
      }
    }
    
    // Apply sorting
    result.sort((a, b) => {
      if (sortBy === 'date') {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      } else {
        return sortOrder === 'asc' 
          ? a.claimAmount - b.claimAmount 
          : b.claimAmount - a.claimAmount;
      }
    });
    
    setFilteredClaims(result);
    
    // Notify parent component of filter changes
    if (onFilterChange) {
      onFilterChange(filters);
    }
  }, [claims, searchQuery, filters, sortBy, sortOrder]);
  
  // Get status color
  const getStatusColor = (status: ClaimStatus): string => {
    switch (status) {
      case 'draft':
        return colors.textSecondary;
      case 'submitted':
        return colors.info[500];
      case 'under_review':
        return colors.warning[500];
      case 'additional_info':
        return colors.warning[700];
      case 'approved':
        return colors.success[500];
      case 'partially_approved':
        return colors.success[300];
      case 'rejected':
        return colors.error[500];
      case 'paid':
        return colors.success[700];
      case 'closed':
        return colors.textSecondary;
      default:
        return colors.textSecondary;
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: ClaimStatus) => {
    const color = getStatusColor(status);
    
    switch (status) {
      case 'draft':
        return <FileText size={20} color={color} />;
      case 'submitted':
        return <Clock size={20} color={color} />;
      case 'under_review':
        return <AlertTriangle size={20} color={color} />;
      case 'additional_info':
        return <AlertTriangle size={20} color={color} />;
      case 'approved':
        return <CheckCircle size={20} color={color} />;
      case 'partially_approved':
        return <CheckCircle size={20} color={color} />;
      case 'rejected':
        return <XCircle size={20} color={color} />;
      case 'paid':
        return <DollarSign size={20} color={color} />;
      case 'closed':
        return <CheckCircle size={20} color={color} />;
      default:
        return <Clock size={20} color={color} />;
    }
  };
  
  // Get status display name
  const getStatusDisplayName = (status: ClaimStatus): string => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'submitted':
        return 'Submitted';
      case 'under_review':
        return 'Under Review';
      case 'additional_info':
        return 'Additional Info Required';
      case 'approved':
        return 'Approved';
      case 'partially_approved':
        return 'Partially Approved';
      case 'rejected':
        return 'Rejected';
      case 'paid':
        return 'Paid';
      case 'closed':
        return 'Closed';
      default:
        return status.replace('_', ' ');
    }
  };
  
  // Format date
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), 'dd MMM yyyy');
    } catch (error) {
      return dateString;
    }
  };
  
  // Toggle sort order
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };
  
  // Toggle sort field
  const toggleSortField = () => {
    setSortBy(sortBy === 'date' ? 'amount' : 'date');
  };
  
  // Reset filters
  const resetFilters = () => {
    setFilters({});
    setSearchQuery('');
  };
  
  // Toggle status filter
  const toggleStatusFilter = (status: ClaimStatus) => {
    setFilters(prev => {
      const currentStatuses = prev.status || [];
      
      if (currentStatuses.includes(status)) {
        return {
          ...prev,
          status: currentStatuses.filter(s => s !== status)
        };
      } else {
        return {
          ...prev,
          status: [...currentStatuses, status]
        };
      }
    });
  };
  
  // Render claim item
  const renderClaimItem = ({ item, index }: { item: Claim; index: number }) => (
    <Animated.View
      entering={FadeInDown.delay(index * 100).duration(300)}
    >
      <TouchableOpacity
        style={styles.claimItem}
        onPress={() => router.push({
          pathname: '/claims/[id]',
          params: { id: item.id }
        })}
        activeOpacity={0.7}
      >
        <View style={styles.claimHeader}>
          <View style={styles.claimTypeContainer}>
            <Text style={styles.claimType}>
              {CLAIM_TYPE_DISPLAY_NAMES[item.type]}
            </Text>
            <Text style={styles.claimReference}>
              Ref: {item.reference}
            </Text>
          </View>
          <View style={styles.claimAmountContainer}>
            <Text style={styles.claimAmount}>
              {formatCurrency(item.claimAmount, item.currency)}
            </Text>
          </View>
        </View>
        
        <View style={styles.claimDetails}>
          <View style={styles.claimDetail}>
            <Text style={styles.claimDetailLabel}>Policy:</Text>
            <Text style={styles.claimDetailValue}>{item.policyNumber}</Text>
          </View>
          <View style={styles.claimDetail}>
            <Text style={styles.claimDetailLabel}>Date:</Text>
            <Text style={styles.claimDetailValue}>{formatDate(item.date)}</Text>
          </View>
          <View style={styles.claimDetail}>
            <Text style={styles.claimDetailLabel}>Incident:</Text>
            <Text style={styles.claimDetailValue}>{formatDate(item.incidentDate)}</Text>
          </View>
        </View>
        
        <View style={styles.claimFooter}>
          <View style={styles.statusContainer}>
            {getStatusIcon(item.status)}
            <Text 
              style={[
                styles.statusText,
                { color: getStatusColor(item.status) }
              ]}
            >
              {getStatusDisplayName(item.status)}
            </Text>
          </View>
          <ChevronRight size={20} color={colors.textSecondary} />
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
  
  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <FileText size={48} color={colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Claims Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Try adjusting your filters or search query'
          : 'You haven\'t filed any claims yet'}
      </Text>
      {(searchQuery || Object.keys(filters).length > 0) && (
        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetFilters}
        >
          <Text style={styles.resetButtonText}>Reset Filters</Text>
        </TouchableOpacity>
      )}
    </View>
  );
  
  return (
    <View style={styles.container}>
      {/* Search and Filter Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search claims..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <TouchableOpacity
          style={[
            styles.filterButton,
            showFilters && styles.filterButtonActive
          ]}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} color={showFilters ? colors.white : colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Filters */}
      {showFilters && (
        <Animated.View 
          style={styles.filtersContainer}
          entering={FadeIn.duration(300)}
        >
          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Status</Text>
            <View style={styles.filterOptions}>
              {['submitted', 'under_review', 'approved', 'rejected', 'paid'].map((status) => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.filterOption,
                    filters.status?.includes(status as ClaimStatus) && {
                      backgroundColor: getStatusColor(status as ClaimStatus) + '20',
                      borderColor: getStatusColor(status as ClaimStatus)
                    }
                  ]}
                  onPress={() => toggleStatusFilter(status as ClaimStatus)}
                >
                  <Text 
                    style={[
                      styles.filterOptionText,
                      filters.status?.includes(status as ClaimStatus) && {
                        color: getStatusColor(status as ClaimStatus)
                      }
                    ]}
                  >
                    {getStatusDisplayName(status as ClaimStatus)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.filterActions}>
            <TouchableOpacity
              style={styles.sortButton}
              onPress={toggleSortField}
            >
              <Text style={styles.sortButtonText}>
                Sort by: {sortBy === 'date' ? 'Date' : 'Amount'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.sortOrderButton}
              onPress={toggleSortOrder}
            >
              {sortOrder === 'asc' ? (
                <SortAsc size={20} color={colors.text} />
              ) : (
                <SortDesc size={20} color={colors.text} />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.resetFiltersButton}
              onPress={resetFilters}
            >
              <Text style={styles.resetFiltersText}>Reset</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}
      
      {/* Claims List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      ) : (
        <FlatList
          data={filteredClaims}
          renderItem={renderClaimItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    padding: 8,
    fontSize: 16,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#6200ee',
  },
  filtersContainer: {
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterOptionText: {
    fontSize: 12,
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortButtonText: {
    fontSize: 14,
    color: '#6200ee',
  },
  sortOrderButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  resetFiltersButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
  },
  resetFiltersText: {
    fontSize: 14,
    color: '#6200ee',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  claimItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  claimHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  claimTypeContainer: {
    flex: 1,
  },
  claimType: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  claimReference: {
    fontSize: 12,
    color: '#757575',
  },
  claimAmountContainer: {
    alignItems: 'flex-end',
  },
  claimAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  claimDetails: {
    marginBottom: 12,
  },
  claimDetail: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  claimDetailLabel: {
    fontSize: 14,
    color: '#757575',
    width: 70,
  },
  claimDetailValue: {
    fontSize: 14,
    flex: 1,
  },
  claimFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 16,
  },
  resetButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#6200ee',
    borderRadius: 20,
  },
  resetButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default ClaimHistoryView;
