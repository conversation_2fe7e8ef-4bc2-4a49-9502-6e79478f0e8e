import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  AppState,
  AppStateStatus,
  Alert,
  InteractionManager
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Upload, Camera, Image as ImageIcon, File } from 'lucide-react-native';
import { Document, DocumentCategory } from './types';
import { UploadManager, UploadResult } from '@/utils/UploadManager';
import DocumentTypeSelector from './DocumentTypeSelector';
import Toast from 'react-native-toast-message';
import { useDocumentUpload } from '@/context/DocumentUploadContext';

interface PureDocumentUploaderProps {
  onDocumentUploaded: (document: Document) => void;
  preselectedDocumentType?: string;
}

interface PureDocumentUploaderState {
  isUploading: boolean;
  selectedDocumentType: string | null;
  selectedDocumentCategory: DocumentCategory | null;
}

/**
 * A pure component implementation of DocumentUploader that extends PureComponent
 * to prevent unnecessary re-renders
 */
class PureDocumentUploaderClass extends React.PureComponent<
  PureDocumentUploaderProps & {
    theme: ReturnType<typeof createTheme>;
    uploadDocument: (document: Document) => Document;
  },
  PureDocumentUploaderState
> {
  // Use instance variables instead of refs
  private isUploadingFlag: boolean = false;
  private appState: AppStateStatus = AppState.currentState;
  private appStateSubscription: any = null;

  constructor(props: PureDocumentUploaderProps & {
    theme: ReturnType<typeof createTheme>;
    uploadDocument: (document: Document) => Document;
  }) {
    super(props);

    this.state = {
      isUploading: false,
      selectedDocumentType: props.preselectedDocumentType || null,
      selectedDocumentCategory: null
    };

    // Bind methods
    this.handleDocumentTypeSelect = this.handleDocumentTypeSelect.bind(this);
    this.pickImage = this.pickImage.bind(this);
    this.pickDocument = this.pickDocument.bind(this);
    this.selectUploadMethod = this.selectUploadMethod.bind(this);
    this.processUploadedDocument = this.processUploadedDocument.bind(this);
  }

  componentDidMount() {
    // Set up app state change listener
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  componentWillUnmount() {
    // Clean up app state listener
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
  }

  handleAppStateChange = (nextAppState: AppStateStatus) => {
    // When app comes back to foreground, reset uploading state
    if (
      this.appState.match(/inactive|background/) &&
      nextAppState === 'active' &&
      this.isUploadingFlag
    ) {
      this.isUploadingFlag = false;
      this.setState({ isUploading: false });
    }

    this.appState = nextAppState;
  };

  handleDocumentTypeSelect(documentType: string, documentCategory: DocumentCategory) {
    this.setState({
      selectedDocumentType: documentType,
      selectedDocumentCategory: documentCategory
    });
  }

  pickImage(useCamera: boolean) {
    // Prevent multiple uploads
    if (this.state.isUploading || this.isUploadingFlag || UploadManager.isUploading) return;

    // Set local uploading state
    this.isUploadingFlag = true;
    this.setState({ isUploading: true });

    // Use the UploadManager to handle the image picking process
    UploadManager.pickImage(
      useCamera,
      this.state.selectedDocumentType!,
      this.state.selectedDocumentCategory!,
      (result: UploadResult) => {
        // Handle the result
        if (result.success && result.document) {
          // Process the uploaded document
          this.processUploadedDocument(result.document);
        } else {
          // Handle error
          console.error('[PureDocumentUploader] Error picking image:', result.error);

          // Only show toast if there's an actual error (not user cancellation)
          if (result.error && result.error !== 'User canceled') {
            Toast.show({
              type: 'error',
              text1: 'Upload Error',
              text2: result.error || 'Failed to upload document. Please try again.',
              visibilityTime: 3000,
            });
          }

          // Reset state
          this.isUploadingFlag = false;
          this.setState({ isUploading: false });
        }
      }
    );


  }

  pickDocument() {
    // Prevent multiple uploads
    if (this.state.isUploading || this.isUploadingFlag || UploadManager.isUploading) return;

    // Set local uploading state
    this.isUploadingFlag = true;
    this.setState({ isUploading: true });

    // Use the UploadManager to handle the document picking process
    UploadManager.pickDocument(
      this.state.selectedDocumentType!,
      this.state.selectedDocumentCategory!,
      (result: UploadResult) => {
        // Handle the result
        if (result.success && result.document) {
          // Process the uploaded document
          this.processUploadedDocument(result.document);
        } else {
          // Handle error
          console.error('[PureDocumentUploader] Error picking document:', result.error);

          // Only show toast if there's an actual error (not user cancellation)
          if (result.error && result.error !== 'User canceled') {
            Toast.show({
              type: 'error',
              text1: 'Upload Error',
              text2: result.error || 'Failed to upload document. Please try again.',
              visibilityTime: 3000,
            });
          }

          // Reset state
          this.isUploadingFlag = false;
          this.setState({ isUploading: false });
        }
      }
    );
  }

  selectUploadMethod() {
    // Prevent multiple uploads or selection when already uploading
    if (!this.state.selectedDocumentType || this.state.isUploading || this.isUploadingFlag) return;

    // Show options for Android
    if (Platform.OS === 'android') {
      // Use a simple alert for now
      Alert.alert(
        'Upload Document',
        'Choose upload method',
        [
          {
            text: 'Camera',
            onPress: () => {
              // We'll wrap this in a setTimeout to prevent UI issues
              setTimeout(() => {
                this.pickImage(true);
              }, 300);
            },
          },
          {
            text: 'Gallery',
            onPress: () => {
              setTimeout(() => {
                this.pickImage(false);
              }, 300);
            },
          },
          {
            text: 'Document',
            onPress: () => {
              setTimeout(() => {
                this.pickDocument();
              }, 300);
            },
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ],
        { cancelable: true }
      );
    }
  }

  processUploadedDocument(newDocument: Document) {
    console.log('[PureDocumentUploader] Processing uploaded document:', newDocument.id);

    try {
      // Reset uploading flag immediately to prevent further uploads
      this.isUploadingFlag = false;

      // Use InteractionManager to ensure we don't block the UI thread
      InteractionManager.runAfterInteractions(() => {
        try {
          // Use the context to upload the document
          const uploadedDoc = this.props.uploadDocument(newDocument);
          console.log('[PureDocumentUploader] Document uploaded to context:', uploadedDoc.id);

          // Call the onDocumentUploaded callback if provided
          if (this.props.onDocumentUploaded) {
            console.log('[PureDocumentUploader] Calling onDocumentUploaded callback');
            this.props.onDocumentUploaded(uploadedDoc);
          }

          // Reset the document type and uploading state in a single setState call
          // Use requestAnimationFrame to batch with other UI updates
          requestAnimationFrame(() => {
            this.setState({
              isUploading: false,
              selectedDocumentType: null,
              selectedDocumentCategory: null
            });
          });
        } catch (innerError) {
          console.error('[PureDocumentUploader] Error in processUploadedDocument:', innerError);

          // Show error toast
          Toast.show({
            type: 'error',
            text1: 'Upload Error',
            text2: 'Failed to process document. Please try again.',
            visibilityTime: 4000,
            topOffset: 60,
          });

          // Reset uploading state
          requestAnimationFrame(() => {
            this.setState({ isUploading: false });
          });
        }
      });
    } catch (error) {
      console.error('[PureDocumentUploader] Error in processUploadedDocument outer try/catch:', error);

      // Show error toast
      Toast.show({
        type: 'error',
        text1: 'Upload Error',
        text2: 'Failed to process document. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
      });

      // Reset uploading state
      this.isUploadingFlag = false;
      requestAnimationFrame(() => {
        this.setState({ isUploading: false });
      });
    }
  }

  render() {
    const { theme } = this.props;
    const { colors, spacing, typography, borders } = theme;
    const { isUploading, selectedDocumentType } = this.state;

    // Render upload options based on platform
    const renderUploadOptions = () => {
      if (Platform.OS === 'ios') {
        return (
          <View style={styles.uploadOptionsContainer}>
            <TouchableOpacity
              style={styles.uploadOption}
              onPress={() => {
                // Use setTimeout to prevent UI issues
                setTimeout(() => {
                  this.pickImage(true);
                }, 300);
              }}
              disabled={!selectedDocumentType || isUploading || this.isUploadingFlag}
            >
              <Camera
                size={24}
                color={colors.primary[500]}
                style={styles.uploadOptionIcon}
              />
              <Text style={styles.uploadOptionText}>Camera</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.uploadOption}
              onPress={() => {
                // Use setTimeout to prevent UI issues
                setTimeout(() => {
                  this.pickImage(false);
                }, 300);
              }}
              disabled={!selectedDocumentType || isUploading || this.isUploadingFlag}
            >
              <ImageIcon
                size={24}
                color={colors.primary[500]}
                style={styles.uploadOptionIcon}
              />
              <Text style={styles.uploadOptionText}>Gallery</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.uploadOption}
              onPress={() => {
                // Use setTimeout to prevent UI issues
                setTimeout(() => {
                  this.pickDocument();
                }, 300);
              }}
              disabled={!selectedDocumentType || isUploading || this.isUploadingFlag}
            >
              <File
                size={24}
                color={colors.primary[500]}
                style={styles.uploadOptionIcon}
              />
              <Text style={styles.uploadOptionText}>Document</Text>
            </TouchableOpacity>
          </View>
        );
      } else {
        return (
          <TouchableOpacity
            style={[
              styles.uploadButton,
              (!selectedDocumentType || isUploading || this.isUploadingFlag) && { opacity: 0.7 }
            ]}
            onPress={() => {
              // Use setTimeout to prevent UI issues
              setTimeout(() => {
                this.selectUploadMethod();
              }, 300);
            }}
            disabled={!selectedDocumentType || isUploading || this.isUploadingFlag}
          >
            {isUploading ? (
              <ActivityIndicator color={colors.white} />
            ) : (
              <>
                <Upload size={20} color={colors.white} />
                <Text style={styles.uploadButtonText}>Upload Document</Text>
              </>
            )}
          </TouchableOpacity>
        );
      }
    };

    // Styles
    const styles = StyleSheet.create({
      container: {
        width: '100%',
      },
      uploadOptionsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: spacing.md,
      },
      uploadOption: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.card,
        borderRadius: borders.radius.md,
        padding: spacing.md,
        marginHorizontal: spacing.xs,
        opacity: (!selectedDocumentType || isUploading) ? 0.7 : 1,
      },
      uploadOptionIcon: {
        marginBottom: spacing.xs,
      },
      uploadOptionText: {
        fontFamily: typography.fonts.medium,
        fontSize: typography.sizes.sm,
        color: colors.text,
      },
      uploadButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.primary[500],
        borderRadius: borders.radius.md,
        padding: spacing.md,
        marginTop: spacing.md,
      },
      uploadButtonText: {
        fontFamily: typography.fonts.medium,
        fontSize: typography.sizes.md,
        color: colors.white,
        marginLeft: spacing.sm,
      },
    });

    return (
      <View style={styles.container}>
        <DocumentTypeSelector onSelect={this.handleDocumentTypeSelect} />
        {renderUploadOptions()}
      </View>
    );
  }
}

// Wrapper component to provide theme and context
const PureDocumentUploader: React.FC<PureDocumentUploaderProps> = React.memo((props) => {
  const { isDarkMode } = useTheme();
  const theme = React.useMemo(() => createTheme(isDarkMode), [isDarkMode]);
  const { uploadDocument } = useDocumentUpload();

  return (
    <PureDocumentUploaderClass
      {...props}
      theme={theme}
      uploadDocument={uploadDocument}
    />
  );
});

export default PureDocumentUploader;
