import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  Check, Clock, AlertCircle, ChevronRight, FileText,
  CreditCard, FileCheck, ClipboardList, Shield,
  AlertTriangle, CheckCircle2
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Application, ApplicationStatus } from '@/store/applicationStore';
import { formatCurrency } from '@/utils/quoteCalculations';

interface ApplicationStatusCardProps {
  application: Application;
  onPress: (application: Application) => void;
  index?: number;
}

const ApplicationStatusCard: React.FC<ApplicationStatusCardProps> = ({
  application,
  onPress,
  index = 0,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders, shadows } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.cardBackground,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...shadows.medium,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: 100,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    infoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    progressContainer: {
      marginVertical: spacing.sm,
    },
    progressBackground: {
      height: 8,
      backgroundColor: colors.neutral[200],
      borderRadius: 4,
    },
    progressBar: {
      height: 8,
      borderRadius: 4,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacing.sm,
    },
    pendingActions: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.warning[500],
    },
    viewDetailsButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    viewDetailsText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginRight: spacing.xs,
    },
  });

  // Get status color and icon
  const getStatusInfo = (status: ApplicationStatus) => {
    switch (status) {
      case 'quote_accepted':
        return {
          color: colors.info[500],
          backgroundColor: `${colors.info[500]}20`,
          icon: <CheckCircle2 size={16} color={colors.info[500]} />,
          text: 'Quote Accepted',
          progressPercentage: 10,
        };
      case 'submitted':
        return {
          color: colors.info[500],
          backgroundColor: `${colors.info[500]}20`,
          icon: <FileText size={16} color={colors.info[500]} />,
          text: 'Submitted',
          progressPercentage: 20,
        };
      case 'payment_pending':
        return {
          color: colors.warning[500],
          backgroundColor: `${colors.warning[500]}20`,
          icon: <CreditCard size={16} color={colors.warning[500]} />,
          text: 'Payment Pending',
          progressPercentage: 30,
        };
      case 'payment_verified':
        return {
          color: colors.primary[500],
          backgroundColor: `${colors.primary[500]}20`,
          icon: <CreditCard size={16} color={colors.primary[500]} />,
          text: 'Payment Verified',
          progressPercentage: 40,
        };
      case 'underwriting':
        return {
          color: colors.primary[500],
          backgroundColor: `${colors.primary[500]}20`,
          icon: <ClipboardList size={16} color={colors.primary[500]} />,
          text: 'Underwriting',
          progressPercentage: 60,
        };
      case 'additional_info':
        return {
          color: colors.warning[500],
          backgroundColor: `${colors.warning[500]}20`,
          icon: <AlertTriangle size={16} color={colors.warning[500]} />,
          text: 'Additional Info Needed',
          progressPercentage: 50,
        };
      case 'approved':
        return {
          color: colors.success[500],
          backgroundColor: `${colors.success[500]}20`,
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Approved',
          progressPercentage: 80,
        };
      case 'approved_with_terms':
        return {
          color: colors.success[500],
          backgroundColor: `${colors.success[500]}20`,
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Approved with Terms',
          progressPercentage: 80,
        };
      case 'policy_issued':
        return {
          color: colors.success[500],
          backgroundColor: `${colors.success[500]}20`,
          icon: <Shield size={16} color={colors.success[500]} />,
          text: 'Policy Issued',
          progressPercentage: 100,
        };
      case 'rejected':
        return {
          color: colors.error[500],
          backgroundColor: `${colors.error[500]}20`,
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          text: 'Rejected',
          progressPercentage: 100,
        };
      default:
        return {
          color: colors.textSecondary,
          backgroundColor: colors.neutral[200],
          icon: <Clock size={16} color={colors.textSecondary} />,
          text: status.replace(/_/g, ' '),
          progressPercentage: 0,
        };
    }
  };

  const statusInfo = getStatusInfo(application.status);

  // Calculate progress percentage
  const progressPercentage = statusInfo.progressPercentage || 0;

  // Count pending actions
  const pendingActions = application.requiredActions?.filter(action => !action.completed).length || 0;

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Animated.View
      entering={FadeInDown.delay(100 + index * 100).springify()}
    >
      <TouchableOpacity
        style={styles.container}
        onPress={() => onPress(application)}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <Text style={styles.title}>{application.type} Insurance</Text>
          <View
            style={[
              styles.statusContainer,
              { backgroundColor: statusInfo.backgroundColor },
            ]}
          >
            {statusInfo.icon}
            <Text
              style={[styles.statusText, { color: statusInfo.color }]}
            >
              {statusInfo.text}
            </Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Reference</Text>
          <Text style={styles.infoValue}>{application.reference}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Submitted</Text>
          <Text style={styles.infoValue}>{formatDate(application.date)}</Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Premium</Text>
          <Text style={styles.infoValue}>{formatCurrency(application.premium, application.currency)}</Text>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBackground}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${progressPercentage}%`,
                  backgroundColor: statusInfo.color,
                },
              ]}
            />
          </View>
        </View>

        <View style={styles.footer}>
          {pendingActions > 0 ? (
            <Text style={styles.pendingActions}>
              {pendingActions} action{pendingActions > 1 ? 's' : ''} required
            </Text>
          ) : (
            <View />
          )}
          <View style={styles.viewDetailsButton}>
            <Text style={styles.viewDetailsText}>View Details</Text>
            <ChevronRight size={16} color={colors.primary[500]} />
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default ApplicationStatusCard;
