import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { BaseToast, ErrorToast, ToastConfig } from 'react-native-toast-message';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { CheckCircle, AlertCircle, Info, X } from 'lucide-react-native';

export const useToastConfig = (): ToastConfig => {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  const styles = StyleSheet.create({
    container: {
      width: '90%',
      borderLeftWidth: 5,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderRadius: borders.radius.md,
      minHeight: 70,
      maxHeight: 120,
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: colors.black,
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.3,
      shadowRadius: 5,
      elevation: 6,
      marginTop: 10,
    },
    successContainer: {
      backgroundColor: isDarkMode ? colors.success[900] : colors.success[50],
      borderLeftColor: colors.success[500],
    },
    errorContainer: {
      backgroundColor: isDarkMode ? colors.error[900] : colors.error[50],
      borderLeftColor: colors.error[500],
    },
    infoContainer: {
      backgroundColor: isDarkMode ? colors.primary[900] : colors.primary[50],
      borderLeftColor: colors.primary[500],
    },
    iconContainer: {
      marginRight: spacing.md,
    },
    textContainer: {
      flex: 1,
      paddingRight: spacing.md,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: 4,
    },
    message: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    closeButton: {
      padding: 4,
    },
  });

  return {
    success: (props) => (
      <View style={[styles.container, styles.successContainer]}>
        <View style={styles.iconContainer}>
          <CheckCircle size={24} color={colors.success[500]} />
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: isDarkMode ? colors.white : colors.text }]}>
            {props.text1}
          </Text>
          {props.text2 && (
            <Text style={[styles.message, { color: isDarkMode ? colors.neutral[300] : colors.textSecondary }]}>
              {props.text2}
            </Text>
          )}
        </View>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => props.onPress?.()}
        >
          <X size={20} color={isDarkMode ? colors.neutral[400] : colors.textSecondary} />
        </TouchableOpacity>
      </View>
    ),
    error: (props) => (
      <View style={[styles.container, styles.errorContainer]}>
        <View style={styles.iconContainer}>
          <AlertCircle size={24} color={colors.error[500]} />
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: isDarkMode ? colors.white : colors.text }]}>
            {props.text1}
          </Text>
          {props.text2 && (
            <Text style={[styles.message, { color: isDarkMode ? colors.neutral[300] : colors.textSecondary }]}>
              {props.text2}
            </Text>
          )}
        </View>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => props.onPress?.()}
        >
          <X size={20} color={isDarkMode ? colors.neutral[400] : colors.textSecondary} />
        </TouchableOpacity>
      </View>
    ),
    info: (props) => (
      <View style={[styles.container, styles.infoContainer]}>
        <View style={styles.iconContainer}>
          <Info size={24} color={colors.primary[500]} />
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: isDarkMode ? colors.white : colors.text }]}>
            {props.text1}
          </Text>
          {props.text2 && (
            <Text style={[styles.message, { color: isDarkMode ? colors.neutral[300] : colors.textSecondary }]}>
              {props.text2}
            </Text>
          )}
        </View>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => props.onPress?.()}
        >
          <X size={20} color={isDarkMode ? colors.neutral[400] : colors.textSecondary} />
        </TouchableOpacity>
      </View>
    ),
  };
};
