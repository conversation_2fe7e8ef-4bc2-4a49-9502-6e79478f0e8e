import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { FileText, PlusCircle, History, Share2, Filter, Search } from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import TabNavigation from '@/components/navigation/TabNavigation';
import BottomNavBar from '@/components/navigation/BottomNavBar';
import ClaimCard from '@/components/claims/ClaimCard';
import useClaimStore from '@/store/claimStore';
import { Claim, ClaimDocument, ClaimStatus } from '@/types/claim.types';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { showToast } from '@/utils/toast';
import SearchInput from '@/components/common/SearchInput';

export default function ClaimsScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Get claims from store
  const { claims, isLoading, error, fetchClaims } = useClaimStore();

  // State
  const [activeTab, setActiveTab] = useState<'all' | 'draft' | 'active' | 'closed'>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Tabs configuration
  const tabs = [
    { id: 'all', label: 'All Claims', icon: FileText },
    { id: 'draft', label: 'Drafts', icon: FileText },
    { id: 'active', label: 'Active', icon: History },
    { id: 'closed', label: 'Closed', icon: History },
  ];

  // Fetch claims on mount
  useEffect(() => {
    fetchClaims();
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchClaims();
    setRefreshing(false);
  };

  // Filter claims based on active tab and search query
  const filteredClaims = claims.filter(claim => {
    // Filter by tab
    let matchesTab = true;
    if (activeTab === 'draft') {
      matchesTab = claim.status === 'draft';
    } else if (activeTab === 'closed') {
      matchesTab = claim.status === 'closed' || claim.status === 'rejected';
    } else if (activeTab === 'active') {
      matchesTab = !['draft', 'closed', 'rejected'].includes(claim.status);
    }

    // Filter by search query
    let matchesSearch = true;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      matchesSearch =
        claim.reference.toLowerCase().includes(query) ||
        claim.type.toLowerCase().includes(query) ||
        claim.description.toLowerCase().includes(query);
    }

    return matchesTab && matchesSearch;
  });

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as 'all' | 'draft' | 'active' | 'closed');
  };

  // Handle claim selection
  const handleClaimSelect = (claim: Claim) => {
    router.push({
      pathname: '/claims/[id]',
      params: { id: claim.id }
    });
  };

  // Handle new claim button press
  const handleNewClaim = () => {
    router.push('/claims/new');
  };

  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xl,
      color: colors.text,
    },
    content: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    scrollViewContent: {
      padding: spacing.md,
      paddingBottom: spacing.xl * 2,
    },
    searchContainer: {
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    newClaimButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.lg,
    },
    newClaimButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    errorText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.error[500],
      textAlign: 'center',
      marginTop: spacing.md,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    emptyText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
      marginHorizontal: spacing.lg,
    },
    filterButton: {
      width: 44,
      height: 44,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.background2,
      marginLeft: spacing.sm,
    },
  });

  // Render content based on loading/error state
  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <FileText size={48} color={colors.error[500]} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      );
    }

    if (filteredClaims.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <FileText size={48} color={colors.textSecondary} />
          <Text style={styles.emptyText}>
            {activeTab === 'all'
              ? "You don't have any claims yet. Start by creating a new claim."
              : `No ${activeTab} claims found.`}
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
      >
        <View style={styles.searchContainer}>
          <SearchInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search claims..."
            containerStyle={{ marginBottom: spacing.md }}
          />
        </View>

        <TouchableOpacity
          style={styles.newClaimButton}
          onPress={handleNewClaim}
          activeOpacity={0.7}
        >
          <PlusCircle size={20} color={colors.white} />
          <Text style={styles.newClaimButtonText}>New Claim</Text>
        </TouchableOpacity>

        {filteredClaims.map((claim, index) => (
          <Animated.View
            key={claim.id}
            entering={FadeInDown.delay(index * 100).duration(300)}
          >
            <ClaimCard
              claim={claim}
              onPress={handleClaimSelect}
              index={index}
            />
          </Animated.View>
        ))}
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Claims Management</Text>
      </View>

      <View style={styles.content}>
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />

        {renderContent()}
      </View>

      <BottomNavBar currentRoute="claims" />
    </SafeAreaView>
  );
}
