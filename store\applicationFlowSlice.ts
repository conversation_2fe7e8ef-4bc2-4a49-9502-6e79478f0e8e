import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { addNotification } from './notificationSlice';

// Define application flow stages
export type ApplicationFlowStage =
  | 'documents_submitted'
  | 'documents_under_review'
  | 'documents_verified'
  | 'quote_available'
  | 'quote_generated'
  | 'quote_accepted'
  | 'policy_bound'
  | 'invoice_generated'
  | 'invoice_delivered'
  | 'payment_uploaded'
  | 'payment_under_review'
  | 'payment_verified'
  | 'payment_reconciled'
  | 'policy_issued'
  | 'policy_under_review'
  | 'policy_dispatched';

// Define application flow item
export interface ApplicationFlowItem {
  id: string;
  quoteId: string;
  applicationId?: string;
  currentStage: ApplicationFlowStage;
  stages: {
    [key in ApplicationFlowStage]?: {
      completed: boolean;
      completedAt?: string;
      progress: number;
      estimatedCompletion?: string;
      notes?: string;
    };
  };
  createdAt: string;
  updatedAt: string;
  clientInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  type: string;
  premium: number;
  currency: string;
  coverAmount: number;
  policyNumber?: string;
  invoiceNumber?: string;
  paymentReference?: string;
}

// Define application flow state
interface ApplicationFlowState {
  flows: ApplicationFlowItem[];
  currentFlow: ApplicationFlowItem | null;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: ApplicationFlowState = {
  flows: [],
  currentFlow: null,
  isLoading: false,
  error: null,
};

// Helper function to calculate overall progress
const calculateProgress = (stages: ApplicationFlowItem['stages']): number => {
  const totalStages = 16; // Total number of stages
  const completedStages = Object.values(stages).filter(stage => stage?.completed).length;
  return Math.round((completedStages / totalStages) * 100);
};

// Helper function to get stage display info
export const getStageDisplayInfo = (stage: ApplicationFlowStage) => {
  const stageInfo: Record<ApplicationFlowStage, { title: string; description: string; order: number }> = {
    documents_submitted: { title: 'Documents Submitted', description: 'Required documents uploaded', order: 1 },
    documents_under_review: { title: 'Documents Under Review', description: 'Documents being verified', order: 2 },
    documents_verified: { title: 'Documents Verified', description: 'All documents approved', order: 3 },
    quote_available: { title: 'Quote Available', description: 'Quote ready for generation', order: 4 },
    quote_generated: { title: 'Quote Generated', description: 'Quote created and sent', order: 5 },
    quote_accepted: { title: 'Quote Accepted', description: 'Quote approved by client', order: 6 },
    policy_bound: { title: 'Policy Bound', description: 'Policy formally issued', order: 7 },
    invoice_generated: { title: 'Invoice Generated', description: 'Payment invoice created', order: 8 },
    invoice_delivered: { title: 'Invoice Delivered', description: 'Invoice sent to client', order: 9 },
    payment_uploaded: { title: 'Payment Uploaded', description: 'Proof of payment received', order: 10 },
    payment_under_review: { title: 'Payment Under Review', description: 'Payment being verified', order: 11 },
    payment_verified: { title: 'Payment Verified', description: 'Payment confirmed', order: 12 },
    payment_reconciled: { title: 'Payment Reconciled', description: 'Payment processed', order: 13 },
    policy_issued: { title: 'Policy Issued', description: 'Policy documents created', order: 14 },
    policy_under_review: { title: 'Policy Under Review', description: 'Final policy review', order: 15 },
    policy_dispatched: { title: 'Policy Dispatched', description: 'Policy delivered to client', order: 16 },
  };
  return stageInfo[stage];
};

// Async thunks
export const fetchApplicationFlows = createAsyncThunk(
  'applicationFlow/fetchFlows',
  async (_, { rejectWithValue }) => {
    try {
      console.log('[ApplicationFlowSlice] Fetching application flows');
      const storedFlows = await AsyncStorage.getItem('applicationFlows');
      const flows = storedFlows ? JSON.parse(storedFlows) : [];
      return flows;
    } catch (error) {
      console.error('[ApplicationFlowSlice] Error fetching flows:', error);
      return rejectWithValue('Failed to fetch application flows');
    }
  }
);

export const createApplicationFlow = createAsyncThunk(
  'applicationFlow/createFlow',
  async (flowData: {
    quoteId: string;
    clientInfo: ApplicationFlowItem['clientInfo'];
    type: string;
    premium: number;
    currency: string;
    coverAmount: number;
  }, { dispatch, rejectWithValue }) => {
    try {
      console.log('[ApplicationFlowSlice] Creating application flow');

      const newFlow: ApplicationFlowItem = {
        id: `flow-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        quoteId: flowData.quoteId,
        currentStage: 'documents_submitted',
        stages: {
          documents_submitted: {
            completed: true,
            completedAt: new Date().toISOString(),
            progress: 100,
          },
          documents_under_review: {
            completed: false,
            progress: 0,
            estimatedCompletion: new Date(Date.now() + 20000).toISOString(), // 20 seconds for demo
          },
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        clientInfo: flowData.clientInfo,
        type: flowData.type,
        premium: flowData.premium,
        currency: flowData.currency,
        coverAmount: flowData.coverAmount,
      };

      // Save to AsyncStorage
      const storedFlows = await AsyncStorage.getItem('applicationFlows');
      const flows = storedFlows ? JSON.parse(storedFlows) : [];
      flows.unshift(newFlow);
      await AsyncStorage.setItem('applicationFlows', JSON.stringify(flows));

      // Send notification
      dispatch(addNotification({
        title: 'Application Started',
        message: `Your ${flowData.type} application has been created and documents are under review.`,
        type: 'application',
        read: false,
        category: 'application',
        priority: 'medium',
        actionRoute: `/applications/flow/${newFlow.id}`,
        actionLabel: 'View Progress',
        actionData: { flowId: newFlow.id },
      }));

      return newFlow;
    } catch (error) {
      console.error('[ApplicationFlowSlice] Error creating flow:', error);
      return rejectWithValue('Failed to create application flow');
    }
  }
);

export const updateFlowStage = createAsyncThunk(
  'applicationFlow/updateStage',
  async (data: {
    flowId: string;
    stage: ApplicationFlowStage;
    completed?: boolean;
    progress?: number;
    notes?: string;
  }, { getState, dispatch, rejectWithValue }) => {
    try {
      console.log('[ApplicationFlowSlice] Updating flow stage:', data.stage);

      const state = getState() as { applicationFlow: ApplicationFlowState };
      const flow = state.applicationFlow.flows.find(f => f.id === data.flowId);

      if (!flow) {
        throw new Error('Flow not found');
      }

      const updatedFlow = {
        ...flow,
        currentStage: data.stage,
        updatedAt: new Date().toISOString(),
        stages: {
          ...flow.stages,
          [data.stage]: {
            ...flow.stages[data.stage],
            completed: data.completed ?? flow.stages[data.stage]?.completed ?? false,
            progress: data.progress ?? flow.stages[data.stage]?.progress ?? 0,
            completedAt: data.completed ? new Date().toISOString() : flow.stages[data.stage]?.completedAt,
            notes: data.notes ?? flow.stages[data.stage]?.notes,
          },
        },
      };

      // Save to AsyncStorage
      const storedFlows = await AsyncStorage.getItem('applicationFlows');
      const flows = storedFlows ? JSON.parse(storedFlows) : [];
      const flowIndex = flows.findIndex((f: ApplicationFlowItem) => f.id === data.flowId);
      if (flowIndex !== -1) {
        flows[flowIndex] = updatedFlow;
        await AsyncStorage.setItem('applicationFlows', JSON.stringify(flows));
      }

      // Generate policy number if policy is issued
      if (data.stage === 'policy_issued' && data.completed && !updatedFlow.policyNumber) {
        updatedFlow.policyNumber = `POL-${Date.now().toString().slice(-8)}-${Math.random().toString(36).substring(2, 5).toUpperCase()}`;
      }

      // Send notification for stage completion
      if (data.completed) {
        const stageInfo = getStageDisplayInfo(data.stage);
        dispatch(addNotification({
          title: `${stageInfo.title} ✅`,
          message: `${stageInfo.description} has been completed for your ${flow.type} application.`,
          type: 'success',
          read: false,
          category: 'application',
          priority: 'medium',
          actionRoute: `/applications/flow/${data.flowId}`,
          actionLabel: 'View Progress',
          actionData: { flowId: data.flowId, stage: data.stage },
        }));
      }

      return updatedFlow;
    } catch (error) {
      console.error('[ApplicationFlowSlice] Error updating stage:', error);
      return rejectWithValue('Failed to update flow stage');
    }
  }
);

export const deleteApplicationFlow = createAsyncThunk(
  'applicationFlow/deleteFlow',
  async (flowId: string, { getState, dispatch, rejectWithValue }) => {
    try {
      console.log('[ApplicationFlowSlice] Deleting application flow:', flowId);

      const state = getState() as { applicationFlow: ApplicationFlowState };
      const flow = state.applicationFlow.flows.find(f => f.id === flowId);

      if (!flow) {
        throw new Error('Flow not found');
      }

      // Cancel any active timers for this flow
      try {
        const { default: applicationFlowService } = await import('@/services/applicationFlowService');
        applicationFlowService.cancelFlow(flowId);
      } catch (error) {
        console.warn('[ApplicationFlowSlice] Error cancelling flow timers:', error);
      }

      // Delete associated documents
      try {
        const { default: useDocumentStore } = await import('@/store/documentStore');
        const documentStore = useDocumentStore.getState();

        // Find and delete documents associated with this flow
        // Documents might be associated by various identifiers
        const associatedDocuments = documentStore.documents.filter(doc =>
          doc.metadata?.quoteId === flow.quoteId ||
          doc.metadata?.applicationId === flow.id ||
          doc.name?.includes(flow.id) ||
          doc.name?.includes(flow.quoteId)
        );

        for (const doc of associatedDocuments) {
          documentStore.deleteDocument(doc.id);
        }

        console.log(`[ApplicationFlowSlice] Deleted ${associatedDocuments.length} associated documents`);
      } catch (error) {
        console.warn('[ApplicationFlowSlice] Error deleting associated documents:', error);
      }

      // Delete from AsyncStorage
      const storedFlows = await AsyncStorage.getItem('applicationFlows');
      const flows = storedFlows ? JSON.parse(storedFlows) : [];
      const updatedFlows = flows.filter((f: ApplicationFlowItem) => f.id !== flowId);
      await AsyncStorage.setItem('applicationFlows', JSON.stringify(updatedFlows));

      // Send notification about deletion
      dispatch(addNotification({
        title: 'Application Deleted',
        message: `Your ${flow.type} application and all associated documents have been deleted.`,
        type: 'info',
        read: false,
        category: 'application',
        priority: 'low',
      }));

      return flowId;
    } catch (error) {
      console.error('[ApplicationFlowSlice] Error deleting flow:', error);
      return rejectWithValue('Failed to delete application flow');
    }
  }
);

// Create the slice
const applicationFlowSlice = createSlice({
  name: 'applicationFlow',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentFlow: (state, action: PayloadAction<string>) => {
      state.currentFlow = state.flows.find(f => f.id === action.payload) || null;
    },
    clearCurrentFlow: (state) => {
      state.currentFlow = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch flows
      .addCase(fetchApplicationFlows.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchApplicationFlows.fulfilled, (state, action) => {
        state.isLoading = false;
        state.flows = action.payload;
      })
      .addCase(fetchApplicationFlows.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Create flow
      .addCase(createApplicationFlow.fulfilled, (state, action) => {
        state.flows.unshift(action.payload);
        state.currentFlow = action.payload;
      })

      // Update stage
      .addCase(updateFlowStage.fulfilled, (state, action) => {
        const index = state.flows.findIndex(f => f.id === action.payload.id);
        if (index !== -1) {
          state.flows[index] = action.payload;
          if (state.currentFlow?.id === action.payload.id) {
            state.currentFlow = action.payload;
          }
        }
      })

      // Delete flow
      .addCase(deleteApplicationFlow.fulfilled, (state, action) => {
        const flowId = action.payload;
        state.flows = state.flows.filter(f => f.id !== flowId);
        if (state.currentFlow?.id === flowId) {
          state.currentFlow = null;
        }
      });
  },
});

export const { clearError, setCurrentFlow, clearCurrentFlow } = applicationFlowSlice.actions;
export default applicationFlowSlice.reducer;
