import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { FileText } from 'lucide-react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface NoDocumentsStateProps {
  message?: string;
  icon?: React.ReactNode;
}

const NoDocumentsState: React.FC<NoDocumentsStateProps> = ({
  message = "You haven't uploaded any documents yet",
  icon,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  console.log('[NoDocumentsState] Rendering empty state');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    icon: {
      marginBottom: spacing.md,
    },
    message: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    },
  });

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeInDown.delay(200).springify()}
    >
      {icon || (
        <FileText 
          size={48} 
          color={colors.textSecondary} 
          style={styles.icon} 
        />
      )}
      <Text style={styles.message}>{message}</Text>
    </Animated.View>
  );
};

export default NoDocumentsState;
