import React from 'react';
import { Stack } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function DocumentsLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  console.log('[DocumentsLayout] Rendering documents layout');

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: colors.background },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="verification" />
      <Stack.Screen name="policies" />
    </Stack>
  );
}
