import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { router } from 'expo-router';
import Animated, { FadeInDown, FadeIn } from 'react-native-reanimated';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  FileText,
  CreditCard,
  Shield,
  ArrowRight,
  Trash2,
  MoreVertical,
} from 'lucide-react-native';

import { RootState, AppDispatch } from '@/store/store';
import {
  fetchApplicationFlows,
  deleteApplicationFlow,
  ApplicationFlowItem,
  ApplicationFlowStage,
  getStageDisplayInfo,
} from '@/store/applicationFlowSlice';
import { useTheme } from '@/context/ThemeContext';

interface EnhancedApplicationTrackerProps {
  onApplicationPress?: (flowId: string) => void;
}

const EnhancedApplicationTracker: React.FC<EnhancedApplicationTrackerProps> = ({
  onApplicationPress,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { colors } = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  const { flows, isLoading, error } = useSelector(
    (state: RootState) => state.applicationFlow
  );

  useEffect(() => {
    dispatch(fetchApplicationFlows());
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchApplicationFlows());
    setRefreshing(false);
  };

  const handleApplicationPress = (flow: ApplicationFlowItem) => {
    if (onApplicationPress) {
      onApplicationPress(flow.id);
    } else {
      router.push(`/applications/flow/${flow.id}`);
    }
  };

  const handleDeleteApplication = (flow: ApplicationFlowItem) => {
    Alert.alert(
      'Delete Application',
      `Are you sure you want to delete your ${flow.type} application? This will permanently remove the application and all associated documents.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            dispatch(deleteApplicationFlow(flow.id));
          },
        },
      ]
    );
  };

  const calculateProgress = (flow: ApplicationFlowItem): number => {
    const totalStages = 16;
    const completedStages = Object.values(flow.stages).filter(
      stage => stage?.completed
    ).length;
    return Math.round((completedStages / totalStages) * 100);
  };

  const getCurrentStageInfo = (flow: ApplicationFlowItem) => {
    return getStageDisplayInfo(flow.currentStage);
  };

  const getStageIcon = (stage: ApplicationFlowStage) => {
    const iconProps = { size: 20, color: colors.primary[500] };

    if (stage.includes('document')) {
      return <FileText {...iconProps} />;
    } else if (stage.includes('payment')) {
      return <CreditCard {...iconProps} />;
    } else if (stage.includes('policy')) {
      return <Shield {...iconProps} />;
    } else {
      return <Clock {...iconProps} />;
    }
  };

  const getStatusColor = (flow: ApplicationFlowItem) => {
    const progress = calculateProgress(flow);
    if (progress === 100) return colors.success[500];
    if (progress >= 75) return colors.warning[500];
    if (progress >= 50) return colors.info[500];
    return colors.primary[500];
  };

  const renderApplicationCard = (flow: ApplicationFlowItem, index: number) => {
    const progress = calculateProgress(flow);
    const currentStageInfo = getCurrentStageInfo(flow);
    const statusColor = getStatusColor(flow);

    return (
      <Animated.View
        key={flow.id}
        entering={FadeInDown.delay(index * 100).springify()}
        style={[styles.applicationCard, { backgroundColor: colors.surface }]}
      >
        <TouchableOpacity
          style={styles.cardContent}
          onPress={() => handleApplicationPress(flow)}
          activeOpacity={0.7}
        >
          {/* Header */}
          <View style={styles.cardHeader}>
            <View style={styles.applicationInfo}>
              <Text style={[styles.applicationType, { color: colors.text }]}>
                {flow.type} Insurance
              </Text>
              <Text style={[styles.applicationId, { color: colors.textSecondary }]}>
                ID: {flow.id.slice(-8).toUpperCase()}
              </Text>
            </View>
            <View style={styles.headerActions}>
              <View style={styles.progressContainer}>
                <Text style={[styles.progressText, { color: statusColor }]}>
                  {progress}%
                </Text>
              </View>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteApplication(flow)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Trash2 size={18} color={colors.error[500]} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Progress Bar */}
          <View style={[styles.progressBarContainer, { backgroundColor: colors.border }]}>
            <Animated.View
              style={[
                styles.progressBar,
                { backgroundColor: statusColor, width: `${progress}%` },
              ]}
              entering={FadeIn.delay(index * 100 + 200)}
            />
          </View>

          {/* Current Stage */}
          <View style={styles.currentStage}>
            <View style={styles.stageIcon}>
              {getStageIcon(flow.currentStage)}
            </View>
            <View style={styles.stageInfo}>
              <Text style={[styles.stageTitle, { color: colors.text }]}>
                {currentStageInfo.title}
              </Text>
              <Text style={[styles.stageDescription, { color: colors.textSecondary }]}>
                {currentStageInfo.description}
              </Text>
            </View>
            <ArrowRight size={20} color={colors.textSecondary} />
          </View>

          {/* Client Info */}
          <View style={styles.clientInfo}>
            <Text style={[styles.clientName, { color: colors.text }]}>
              {flow.clientInfo.firstName} {flow.clientInfo.lastName}
            </Text>
            <Text style={[styles.premiumAmount, { color: colors.primary[500] }]}>
              {flow.currency} {flow.premium.toLocaleString()}
            </Text>
          </View>

          {/* Timeline Preview */}
          <View style={styles.timelinePreview}>
            <Text style={[styles.timelineTitle, { color: colors.textSecondary }]}>
              Recent Activity
            </Text>
            <View style={styles.timelineItems}>
              {Object.entries(flow.stages)
                .filter(([_, stage]) => stage?.completed)
                .slice(-2)
                .map(([stageName, stage], idx) => {
                  const stageInfo = getStageDisplayInfo(stageName as ApplicationFlowStage);
                  return (
                    <View key={stageName} style={styles.timelineItem}>
                      <CheckCircle size={12} color={colors.success[500]} />
                      <Text style={[styles.timelineItemText, { color: colors.textSecondary }]}>
                        {stageInfo.title}
                      </Text>
                    </View>
                  );
                })}
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderEmptyState = () => (
    <Animated.View
      entering={FadeIn}
      style={[styles.emptyState, { backgroundColor: colors.surface }]}
    >
      <FileText size={48} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        No Applications Yet
      </Text>
      <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
        Your insurance applications will appear here once you start the process.
      </Text>
      <TouchableOpacity
        style={[styles.startButton, { backgroundColor: colors.primary[500] }]}
        onPress={() => router.push('/quotes')}
      >
        <Text style={[styles.startButtonText, { color: colors.white }]}>
          Get a Quote
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: colors.surface }]}>
        <AlertCircle size={48} color={colors.error[500]} />
        <Text style={[styles.errorText, { color: colors.error[500] }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary[500] }]}
          onPress={() => dispatch(fetchApplicationFlows())}
        >
          <Text style={[styles.retryButtonText, { color: colors.white }]}>
            Retry
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[colors.primary[500]]}
          tintColor={colors.primary[500]}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {flows.length === 0 ? (
        renderEmptyState()
      ) : (
        <View style={styles.applicationsContainer}>
          {flows.map((flow, index) => renderApplicationCard(flow, index))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  applicationsContainer: {
    padding: 16,
    gap: 16,
  },
  applicationCard: {
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardContent: {
    gap: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  applicationInfo: {
    flex: 1,
  },
  applicationType: {
    fontSize: 16,
    fontWeight: '600',
  },
  applicationId: {
    fontSize: 12,
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  deleteButton: {
    padding: 4,
    borderRadius: 4,
  },
  progressBarContainer: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  currentStage: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stageIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stageInfo: {
    flex: 1,
  },
  stageTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  stageDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  clientInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  clientName: {
    fontSize: 14,
    fontWeight: '500',
  },
  premiumAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  timelinePreview: {
    marginTop: 8,
  },
  timelineTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 6,
  },
  timelineItems: {
    gap: 4,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timelineItemText: {
    fontSize: 11,
  },
  emptyState: {
    margin: 16,
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    gap: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  startButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  startButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  errorContainer: {
    margin: 16,
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default EnhancedApplicationTracker;
