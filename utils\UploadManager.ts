/**
 * UploadManager.ts
 *
 * A singleton utility to manage document uploads outside of React's rendering cycle.
 * This helps prevent re-renders and provides a more stable upload experience.
 */

import { Document, DocumentCategory, DocumentFileType } from '@/components/documents/types';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import Toast from 'react-native-toast-message';

// Upload states
export type UploadState = 'idle' | 'preparing' | 'selecting' | 'processing' | 'uploading' | 'success' | 'error';

// Upload result
export interface UploadResult {
  success: boolean;
  document?: Document;
  error?: string;
}

// Upload manager class
class UploadManagerClass {
  // Private state
  private _isUploading: boolean = false;
  private _currentState: UploadState = 'idle';
  private _lockTimeout: NodeJS.Timeout | null = null;
  private _uploadCallbacks: Array<(result: UploadResult) => void> = [];

  // Get current upload state
  get currentState(): UploadState {
    return this._currentState;
  }

  // Check if currently uploading
  get isUploading(): boolean {
    return this._isUploading;
  }

  // Set the current state
  private setState(state: UploadState) {
    console.log(`[UploadManager] State changed: ${this._currentState} -> ${state}`);
    this._currentState = state;
  }

  // Lock the upload process to prevent multiple simultaneous uploads
  private lock(): boolean {
    if (this._isUploading) {
      console.log('[UploadManager] Upload already in progress, ignoring request');
      return false;
    }

    console.log('[UploadManager] Locking upload process');
    this._isUploading = true;

    // Set a safety timeout to release the lock after 30 seconds
    // This prevents the system from getting stuck if something goes wrong
    this._lockTimeout = setTimeout(() => {
      console.log('[UploadManager] Safety timeout triggered, releasing lock');
      this.unlock();
      this.notifyCallbacks({
        success: false,
        error: 'Upload timed out'
      });
    }, 30000);

    return true;
  }

  // Unlock the upload process
  private unlock() {
    console.log('[UploadManager] Unlocking upload process');
    this._isUploading = false;
    this.setState('idle');

    // Clear the safety timeout
    if (this._lockTimeout) {
      clearTimeout(this._lockTimeout);
      this._lockTimeout = null;
    }
  }

  // Notify all registered callbacks
  private notifyCallbacks(result: UploadResult) {
    // Make a copy of the callbacks array before iterating
    const callbacks = [...this._uploadCallbacks];

    // Clear the callbacks array
    this._uploadCallbacks = [];

    // Notify all callbacks
    callbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('[UploadManager] Error in callback:', error);
      }
    });
  }

  // Register a callback for the current upload
  public registerCallback(callback: (result: UploadResult) => void) {
    this._uploadCallbacks.push(callback);
  }

  // Pick an image from camera or gallery
  public async pickImage(
    useCamera: boolean,
    documentType: string,
    documentCategory: DocumentCategory,
    callback: (result: UploadResult) => void
  ): Promise<void> {
    // Register the callback
    this.registerCallback(callback);

    // Try to lock the upload process
    if (!this.lock()) {
      return;
    }

    try {
      this.setState('preparing');

      // Request permissions
      let permissionResult;
      if (useCamera) {
        permissionResult = await ImagePicker.requestCameraPermissionsAsync();
        if (permissionResult.granted === false) {
          Toast.show({
            type: 'error',
            text1: 'Permission Required',
            text2: 'Camera permission is needed to take photos',
            visibilityTime: 3000,
          });
          this.unlock();
          this.notifyCallbacks({
            success: false,
            error: 'Camera permission denied'
          });
          return;
        }
      } else {
        permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (permissionResult.granted === false) {
          Toast.show({
            type: 'error',
            text1: 'Permission Required',
            text2: 'Media library permission is needed to select photos',
            visibilityTime: 3000,
          });
          this.unlock();
          this.notifyCallbacks({
            success: false,
            error: 'Media library permission denied'
          });
          return;
        }
      }

      this.setState('selecting');

      // Launch camera or image picker
      const result = useCamera
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 0.8,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: 'images',
            allowsEditing: true,
            quality: 0.8,
          });

      if (result.canceled) {
        this.unlock();
        this.notifyCallbacks({
          success: false,
          error: 'User canceled'
        });
        return;
      }

      this.setState('processing');

      // Get the URI from the result
      const uri = result.assets[0].uri;
      const documentFileType: DocumentFileType = 'image';

      // Create a new document object
      const newDocument: Document = {
        id: Date.now().toString(),
        name: documentType,
        type: documentCategory,
        status: 'pending',
        date: new Date().toISOString().split('T')[0],
        uri: uri,
        fileUrl: uri,
        fileName: result.assets[0].fileName || `image_${Date.now()}.jpg`,
        fileType: documentFileType,
        isImage: true,
        fileSize: result.assets[0].fileSize,
        metadata: { timestamp: Date.now() }
      };

      this.setState('success');
      this.notifyCallbacks({
        success: true,
        document: newDocument
      });
    } catch (error) {
      console.error('[UploadManager] Error picking image:', error);
      this.setState('error');
      this.notifyCallbacks({
        success: false,
        error: 'Failed to pick image'
      });
    } finally {
      this.unlock();
    }
  }

  // Pick a document file
  public async pickDocument(
    documentType: string,
    documentCategory: DocumentCategory,
    callback: (result: UploadResult) => void
  ): Promise<void> {
    // Register the callback
    this.registerCallback(callback);

    // Try to lock the upload process
    if (!this.lock()) {
      return;
    }

    try {
      this.setState('preparing');

      this.setState('selecting');

      // Launch document picker
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'image/*',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/rtf',
          'text/plain',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          '*/*'  // Allow all file types as fallback
        ],
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        this.unlock();
        this.notifyCallbacks({
          success: false,
          error: 'User canceled'
        });
        return;
      }

      this.setState('processing');

      // Determine file type
      let fileType: DocumentFileType = 'other';
      const mimeType = result.assets[0].mimeType?.toLowerCase() || '';

      if (mimeType.includes('pdf')) {
        fileType = 'pdf';
      } else if (mimeType.includes('image')) {
        fileType = 'image';
      } else if (mimeType.includes('word') || mimeType.includes('document')) {
        fileType = 'doc';
      } else if (mimeType.includes('excel') || mimeType.includes('sheet')) {
        fileType = 'excel';
      } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
        fileType = 'powerpoint';
      } else if (mimeType.includes('text/plain')) {
        fileType = 'text';
      } else if (mimeType.includes('rtf')) {
        fileType = 'rtf';
      }

      // Also check file extension as fallback
      const fileName = result.assets[0].name?.toLowerCase() || '';
      if (fileType === 'other') {
        if (fileName.endsWith('.pdf')) fileType = 'pdf';
        else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) fileType = 'doc';
        else if (fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) fileType = 'excel';
        else if (fileName.endsWith('.ppt') || fileName.endsWith('.pptx')) fileType = 'powerpoint';
        else if (fileName.endsWith('.txt')) fileType = 'text';
        else if (fileName.endsWith('.rtf')) fileType = 'rtf';
        else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') ||
                fileName.endsWith('.png') || fileName.endsWith('.gif')) fileType = 'image';
      }

      // Get the URI from the result
      const uri = result.assets[0].uri;

      // Create a new document object
      const newDocument: Document = {
        id: Date.now().toString(),
        name: documentType,
        type: documentCategory,
        status: 'pending',
        date: new Date().toISOString().split('T')[0],
        uri: uri,
        fileUrl: uri,
        fileName: result.assets[0].name,
        fileType: fileType,
        isImage: fileType === 'image',
        fileSize: result.assets[0].size,
        metadata: { timestamp: Date.now() }
      };

      this.setState('success');
      this.notifyCallbacks({
        success: true,
        document: newDocument
      });
    } catch (error) {
      console.error('[UploadManager] Error picking document:', error);
      this.setState('error');
      this.notifyCallbacks({
        success: false,
        error: 'Failed to pick document'
      });
    } finally {
      this.unlock();
    }
  }
}

// Create a singleton instance
export const UploadManager = new UploadManagerClass();
