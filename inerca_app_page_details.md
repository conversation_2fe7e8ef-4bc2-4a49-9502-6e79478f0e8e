# Inerca Holdings Mobile App - Page Details Document

## 1. Authentication & Onboarding Pages

### 1.1 Splash Screen
- App logo display
- Loading animation

### 1.2 Introduction/Welcome Screens
- Brief app overview
- Value proposition slides
- "Get Started" button

### 1.3 Registration Screen
- Full name input
- Email address input
- Phone number input
- Password creation
- OTP verification interface
- Terms and conditions acceptance

### 1.4 Login Screen
- Email/Phone input
- Password input
- "Forgot Password" option
- Biometric authentication option
- "Remember Me" toggle

### 1.5 Forgot Password Screen
- Email/Phone input for password reset
- OTP verification interface
- New password creation

## 2. Main Navigation & Dashboard

### 2.1 Home Dashboard
- Welcome message with user's name
- Quick action buttons (Get Quote, My Policies, Claims, etc.)
- Notification center
- Current policy summary cards
- Renewal reminders
- Promotional banners

### 2.2 Main Navigation Menu
- Home
- My Profile
- Get Quote
- My Policies
- Claims
- Documents
- Support/Live Chat
- Settings
- Logout

## 3. User Profile Section

### 3.1 Profile Information Screen
- Personal details (name, ID/passport, contact info)
- Profile picture upload option
- Address information
- Occupation details
- Edit profile button

### 3.2 Profile Edit Screen
- Editable fields for all profile information
- Save changes button

### 3.3 KYC Documents Repository
- List of submitted KYC documents
- Document status indicators
- Upload new document option

## 4. Insurance Quote Flows

### 4.1 Quote Type Selection Screen
- Insurance product tiles (Motor, Houseowners, Household Contents, All Risks, Life Assurance, etc.)
- Brief description of each product

### 4.2 General Client Information Form
- Personal details collection
- Payment preference selection
- Required documents checklist

### 4.3 Motor Insurance Quote Form
- Vehicle details input fields
- Usage type selection
- Required documents upload interface
- Brand selection (with differential rates as per specifications)

### 4.4 Houseowners Insurance Quote Form
- Property details input fields
- Construction type selection
- Occupancy status
- Required documents upload interface

### 4.5 Household Contents Insurance Quote Form
- Property address input
- Security features selection
- Contents inventory form
- Optional add-ons selection (Power Surge, Accidental Breakage)
- Required documents upload interface

### 4.6 All Risks Insurance Quote Form
- Unspecified items value input
- Specified items listing interface
- Item value inputs

### 4.7 Life Assurance/Funeral Cover Form
- Insured details input
- Coverage options selection
- Beneficiary details input
- Required documents upload interface

### 4.8 Scheme Insurance Form
- Member details input
- Employer information
- Employee number
- Supporting form selection

### 4.9 Quote Summary Screen
- Premium calculation display
- Coverage details summary
- Terms and conditions
- Edit quote option
- Proceed to application button

### 4.10 Quote PDF Viewer
- Generated quote PDF display
- Download/share options
- Proceed to application button

## 5. Document Management Pages

### 5.1 Document Upload Hub
- Document type selection
- Camera/gallery access for capturing documents
- PDF/file upload option
- Document submission status
- Missing documents indicator

### 5.2 Document Verification Status
- List of required documents
- Status indicators (Pending, Verified, Rejected)
- Reupload option for rejected documents
- Admin notes/feedback display

### 5.3 Policy Documents Repository
- List of received policy documents
- PDF viewer for policies
- Download/share options
- Policy wording access

## 6. Payment Processing

### 6.1 Payment Method Selection
- Payment options as per quote selection
- Annual (EFT) details
- Monthly (Direct Debit) setup

### 6.2 Proof of Payment Upload
- Camera/gallery access for capturing payment proof
- PDF/file upload option
- Submission confirmation
- Payment verification status

## 7. Policy Management

### 7.1 My Policies List
- Active policies cards/list
- Policy status indicators
- Quick access to policy details
- Renewal indicators
- Search/filter functionality

### 7.2 Policy Detail Screen
- Comprehensive policy information
- Coverage details
- Premium information
- Policy document access
- Renewal information
- Claim initiation button

### 7.3 Invoice Viewer
- Invoice PDF display
- Payment status
- Download/share options

## 8. Claims Management

### 8.1 Claims Dashboard
- Active claims list
- Claim status indicators
- New claim button

### 8.2 Claim Submission Form
- Policy selection
- Claim type selection
- Incident details input
- Supporting documents upload
- Submit claim button

### 8.3 Claim Tracking Screen
- Claim reference number
- Current status display
- Timeline of claim processing
- Required actions highlight
- Additional document upload option
- Communication history

## 9. Support & Communication

### 9.1 FAQ Screen
- Categorized frequently asked questions
- Search functionality
- Expandable answers

### 9.2 Live Chat Interface
- Chat message thread
- Message input
- Attachment options
- Agent status indicator
- Chat history

### 9.3 Contact Support Screen
- Contact methods (phone, email)
- Support request form
- Office locations/hours

### 9.4 Notification Center
- All app notifications list
- Read/unread indicators
- Action buttons where applicable
- Clear/mark as read options

## 10. Application Tracker

### 10.1 Application Status Dashboard
- List of all applications
- Status indicators
- Progress bars
- Pending actions highlights

### 10.2 Application Detail Screen
- Comprehensive application information
- Status timeline
- Document verification status
- Payment status
- Underwriting notes (if shared)
- Action required indicators

## 11. Settings

### 11.1 App Settings Screen
- Notification preferences
- Language selection (English, future Setswana)
- Biometric authentication toggle
- App version information
- Terms & Privacy links
- Logout option

## 12. Admin Dashboard (Web Portal)

### 12.1 Admin Login
- Secure login for Inerca staff
- Role-based authentication

### 12.2 Admin Dashboard Home
- Overview statistics
- Recent activities
- Pending tasks
- Quick access to key functions

### 12.3 Client Management
- Client listing with search/filter
- Client profile viewing
- Document verification interface
- Status management

### 12.4 Quotation Management
- Quote requests listing
- Quote modification interface
- Quote approval workflow
- Quote sharing controls

### 12.5 Document Verification Interface
- Document review screen
- Accept/reject controls
- Feedback input for rejected documents
- Internal notes section

### 12.6 Policy Management
- Policy issuance interface
- Document upload to client
- Invoice generation
- Policy wording attachment

### 12.7 Payment Verification
- Payment proof review interface
- Payment confirmation controls
- Payment status update

### 12.8 Notification Management
- Notification creation interface
- Triggered notification setup
- Notification templates

### 12.9 Analytics Dashboard
- Signup metrics
- Quote-to-policy conversion rates
- Client engagement visualization
- Policy renewal tracking
- Claims register
- Pending claims report
- General reports generation

## 13. Additional Pages

### 13.1 Terms & Conditions
- Full legal terms text
- Acceptance recording

### 13.2 Privacy Policy
- Data privacy information
- User rights and controls

### 13.3 Rate Cards
- Display of applicable insurance rates
- Categorized by insurance type
- Updated from admin dashboard

### 13.4 Network Error Screen
- Connection issue notification
- Retry option
- Offline functionality explanation

### 13.5 Maintenance Mode Screen
- Scheduled maintenance notification
- Expected resolution time
- Alternative contact methods