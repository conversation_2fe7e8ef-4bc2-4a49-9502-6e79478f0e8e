import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft, FileText, ChevronRight } from 'lucide-react-native';
import { router } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { ClaimType, CLAIM_TYPE_DISPLAY_NAMES } from '@/types/claim.types';
import useClaimStore from '@/store/claimStore';
import { useAppSelector } from '@/store/hooks';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function NewClaimScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  
  // Get user data from auth store
  const user = useAppSelector((state) => state.auth.user);
  
  // Mock policies data (in a real app, this would come from an API)
  const [policies, setPolicies] = useState<Array<{ id: string; policyNumber: string; type: string }>>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // Fetch policies on mount
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setPolicies([
        { id: '1', policyNumber: 'POL-2023-0001', type: 'Motor Insurance' },
        { id: '2', policyNumber: 'POL-2023-0002', type: 'Household Insurance' },
        { id: '3', policyNumber: 'POL-2023-0003', type: 'Life Insurance' },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      backgroundColor: colors.card,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.sm,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    content: {
      flex: 1,
      padding: spacing.md,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    claimTypeCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    claimTypeIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: `${colors.primary[500]}20`,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    claimTypeInfo: {
      flex: 1,
    },
    claimTypeName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs / 2,
    },
    claimTypeDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noPoliciesContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    noPoliciesText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
    },
  });
  
  // Handle claim type selection
  const handleSelectClaimType = (type: ClaimType) => {
    router.push({
      pathname: '/claims/[type]/form',
      params: { type }
    });
  };
  
  // Render content based on loading state
  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      );
    }
    
    if (policies.length === 0) {
      return (
        <View style={styles.noPoliciesContainer}>
          <FileText size={48} color={colors.textSecondary} />
          <Text style={styles.noPoliciesText}>
            You don't have any active policies. You need an active policy to file a claim.
          </Text>
        </View>
      );
    }
    
    // Group claim types by category
    const claimTypeGroups = {
      motor: ['motor_accident', 'motor_theft', 'motor_damage'],
      property: ['property_damage', 'property_theft', 'household_damage'],
      other: ['all_risks', 'medical', 'life', 'other']
    };
    
    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>Select Claim Type</Text>
        
        <Text style={[styles.sectionTitle, { fontSize: typography.sizes.md, marginTop: spacing.md }]}>
          Motor Claims
        </Text>
        {claimTypeGroups.motor.map((type, index) => (
          <Animated.View key={type} entering={FadeInDown.delay(100 + index * 100).springify()}>
            <TouchableOpacity
              style={styles.claimTypeCard}
              onPress={() => handleSelectClaimType(type as ClaimType)}
              activeOpacity={0.7}
            >
              <View style={styles.claimTypeIcon}>
                <FileText size={20} color={colors.primary[500]} />
              </View>
              <View style={styles.claimTypeInfo}>
                <Text style={styles.claimTypeName}>
                  {CLAIM_TYPE_DISPLAY_NAMES[type as ClaimType]}
                </Text>
                <Text style={styles.claimTypeDescription}>
                  {getClaimTypeDescription(type as ClaimType)}
                </Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </Animated.View>
        ))}
        
        <Text style={[styles.sectionTitle, { fontSize: typography.sizes.md, marginTop: spacing.md }]}>
          Property Claims
        </Text>
        {claimTypeGroups.property.map((type, index) => (
          <Animated.View key={type} entering={FadeInDown.delay(300 + index * 100).springify()}>
            <TouchableOpacity
              style={styles.claimTypeCard}
              onPress={() => handleSelectClaimType(type as ClaimType)}
              activeOpacity={0.7}
            >
              <View style={styles.claimTypeIcon}>
                <FileText size={20} color={colors.primary[500]} />
              </View>
              <View style={styles.claimTypeInfo}>
                <Text style={styles.claimTypeName}>
                  {CLAIM_TYPE_DISPLAY_NAMES[type as ClaimType]}
                </Text>
                <Text style={styles.claimTypeDescription}>
                  {getClaimTypeDescription(type as ClaimType)}
                </Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </Animated.View>
        ))}
        
        <Text style={[styles.sectionTitle, { fontSize: typography.sizes.md, marginTop: spacing.md }]}>
          Other Claims
        </Text>
        {claimTypeGroups.other.map((type, index) => (
          <Animated.View key={type} entering={FadeInDown.delay(600 + index * 100).springify()}>
            <TouchableOpacity
              style={styles.claimTypeCard}
              onPress={() => handleSelectClaimType(type as ClaimType)}
              activeOpacity={0.7}
            >
              <View style={styles.claimTypeIcon}>
                <FileText size={20} color={colors.primary[500]} />
              </View>
              <View style={styles.claimTypeInfo}>
                <Text style={styles.claimTypeName}>
                  {CLAIM_TYPE_DISPLAY_NAMES[type as ClaimType]}
                </Text>
                <Text style={styles.claimTypeDescription}>
                  {getClaimTypeDescription(type as ClaimType)}
                </Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </Animated.View>
        ))}
      </ScrollView>
    );
  };
  
  // Helper function to get claim type description
  const getClaimTypeDescription = (type: ClaimType): string => {
    switch (type) {
      case 'motor_accident':
        return 'For vehicle accidents involving damage to your vehicle or third parties';
      case 'motor_theft':
        return 'For stolen vehicles or theft of parts from your vehicle';
      case 'motor_damage':
        return 'For damage to your vehicle not caused by an accident';
      case 'property_damage':
        return 'For damage to your insured property';
      case 'property_theft':
        return 'For theft of items from your insured property';
      case 'household_damage':
        return 'For damage to household contents';
      case 'all_risks':
        return 'For items covered under all risks insurance';
      case 'medical':
        return 'For medical expenses covered by your insurance';
      case 'life':
        return 'For life insurance claims';
      case 'other':
        return 'For other types of claims not listed above';
      default:
        return '';
    }
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>New Claim</Text>
      </View>
      
      <View style={styles.content}>
        {renderContent()}
      </View>
      
      <BottomNavBar currentRoute="claims" />
    </SafeAreaView>
  );
}
