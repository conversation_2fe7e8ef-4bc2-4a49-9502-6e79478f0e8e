import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ClaimInput, ClaimType, CLAIM_TYPE_DISPLAY_NAMES } from '@/types/claim.types';
import { Calendar, DollarSign, FileText, ChevronDown, ChevronUp } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { showToast } from '@/utils/toast';

interface ClaimFormProps {
  onSubmit: (claimInput: ClaimInput) => Promise<void>;
  policies: Array<{ id: string; policyNumber: string; type: string }>;
  initialType?: ClaimType;
}

const ClaimForm: React.FC<ClaimFormProps> = ({
  onSubmit,
  policies,
  initialType
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Form state
  const [policyId, setPolicyId] = useState<string>('');
  const [claimType, setClaimType] = useState<ClaimType>(initialType || 'motor_accident');
  const [incidentDate, setIncidentDate] = useState<Date>(new Date());
  const [description, setDescription] = useState<string>('');
  const [claimAmount, setClaimAmount] = useState<string>('');
  const [currency, setCurrency] = useState<string>('BWP');
  
  // UI state
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [showPolicyPicker, setShowPolicyPicker] = useState<boolean>(false);
  const [showTypePicker, setShowTypePicker] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    formContainer: {
      padding: spacing.md,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    formGroup: {
      marginBottom: spacing.lg,
    },
    label: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    input: {
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    inputError: {
      borderColor: colors.error[500],
    },
    errorText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.error[500],
      marginTop: spacing.xs,
    },
    pickerContainer: {
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      marginBottom: spacing.xs,
    },
    pickerTrigger: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.md,
    },
    pickerTriggerText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    pickerContent: {
      backgroundColor: colors.card,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      padding: spacing.sm,
    },
    pickerItem: {
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    pickerItemText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    pickerItemSelected: {
      backgroundColor: `${colors.primary[500]}20`,
    },
    pickerItemTextSelected: {
      color: colors.primary[500],
      fontFamily: typography.fonts.medium,
    },
    datePickerTrigger: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      padding: spacing.md,
    },
    datePickerText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
      flex: 1,
    },
    amountContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      padding: spacing.md,
    },
    currencyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginRight: spacing.sm,
    },
    amountInput: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      padding: 0,
    },
    submitButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      alignItems: 'center',
      marginTop: spacing.md,
    },
    submitButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
    disabledButton: {
      backgroundColor: colors.neutral[400],
    },
  });

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Handle date change
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setIncidentDate(selectedDate);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!policyId) {
      newErrors.policyId = 'Please select a policy';
    }

    if (!incidentDate) {
      newErrors.incidentDate = 'Please select an incident date';
    } else if (incidentDate > new Date()) {
      newErrors.incidentDate = 'Incident date cannot be in the future';
    }

    if (!description.trim()) {
      newErrors.description = 'Please provide a description';
    } else if (description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!claimAmount) {
      newErrors.claimAmount = 'Please enter a claim amount';
    } else if (isNaN(Number(claimAmount)) || Number(claimAmount) <= 0) {
      newErrors.claimAmount = 'Please enter a valid amount';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      showToast(
        'error',
        'Form Error',
        'Please correct the errors in the form',
        { visibilityTime: 3000 }
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedPolicy = policies.find(p => p.id === policyId);
      
      if (!selectedPolicy) {
        throw new Error('Selected policy not found');
      }

      const claimInput: ClaimInput = {
        policyId,
        policyNumber: selectedPolicy.policyNumber,
        type: claimType,
        incidentDate: incidentDate.toISOString().split('T')[0],
        description,
        claimAmount: Number(claimAmount),
        currency
      };

      await onSubmit(claimInput);
    } catch (error) {
      console.error('Error submitting claim:', error);
      showToast(
        'error',
        'Submission Error',
        'Failed to submit claim. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.formContainer}>
        <Text style={styles.sectionTitle}>New Claim</Text>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Select Policy</Text>
          <View style={[
            styles.pickerContainer,
            errors.policyId ? styles.inputError : {}
          ]}>
            <TouchableOpacity
              style={styles.pickerTrigger}
              onPress={() => setShowPolicyPicker(!showPolicyPicker)}
            >
              <Text style={styles.pickerTriggerText}>
                {policyId ? policies.find(p => p.id === policyId)?.policyNumber : 'Select a policy'}
              </Text>
              {showPolicyPicker ? (
                <ChevronUp size={20} color={colors.text} />
              ) : (
                <ChevronDown size={20} color={colors.text} />
              )}
            </TouchableOpacity>
            
            {showPolicyPicker && (
              <View style={styles.pickerContent}>
                {policies.map(policy => (
                  <TouchableOpacity
                    key={policy.id}
                    style={[
                      styles.pickerItem,
                      policyId === policy.id ? styles.pickerItemSelected : {}
                    ]}
                    onPress={() => {
                      setPolicyId(policy.id);
                      setShowPolicyPicker(false);
                    }}
                  >
                    <Text style={[
                      styles.pickerItemText,
                      policyId === policy.id ? styles.pickerItemTextSelected : {}
                    ]}>
                      {policy.policyNumber} - {policy.type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
          {errors.policyId && <Text style={styles.errorText}>{errors.policyId}</Text>}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Claim Type</Text>
          <View style={styles.pickerContainer}>
            <TouchableOpacity
              style={styles.pickerTrigger}
              onPress={() => setShowTypePicker(!showTypePicker)}
            >
              <Text style={styles.pickerTriggerText}>
                {CLAIM_TYPE_DISPLAY_NAMES[claimType]}
              </Text>
              {showTypePicker ? (
                <ChevronUp size={20} color={colors.text} />
              ) : (
                <ChevronDown size={20} color={colors.text} />
              )}
            </TouchableOpacity>
            
            {showTypePicker && (
              <View style={styles.pickerContent}>
                {Object.entries(CLAIM_TYPE_DISPLAY_NAMES).map(([type, displayName]) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.pickerItem,
                      claimType === type ? styles.pickerItemSelected : {}
                    ]}
                    onPress={() => {
                      setClaimType(type as ClaimType);
                      setShowTypePicker(false);
                    }}
                  >
                    <Text style={[
                      styles.pickerItemText,
                      claimType === type ? styles.pickerItemTextSelected : {}
                    ]}>
                      {displayName}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Incident Date</Text>
          <TouchableOpacity
            style={[
              styles.datePickerTrigger,
              errors.incidentDate ? styles.inputError : {}
            ]}
            onPress={() => setShowDatePicker(true)}
          >
            <Calendar size={20} color={colors.text} />
            <Text style={styles.datePickerText}>
              {formatDate(incidentDate)}
            </Text>
          </TouchableOpacity>
          {errors.incidentDate && <Text style={styles.errorText}>{errors.incidentDate}</Text>}
          
          {showDatePicker && (
            <DateTimePicker
              value={incidentDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              maximumDate={new Date()}
            />
          )}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[
              styles.input,
              errors.description ? styles.inputError : {}
            ]}
            placeholder="Describe what happened..."
            placeholderTextColor={colors.textSecondary}
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
          {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Claim Amount</Text>
          <View style={[
            styles.amountContainer,
            errors.claimAmount ? styles.inputError : {}
          ]}>
            <Text style={styles.currencyText}>{currency}</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0.00"
              placeholderTextColor={colors.textSecondary}
              value={claimAmount}
              onChangeText={setClaimAmount}
              keyboardType="numeric"
            />
            <DollarSign size={20} color={colors.text} />
          </View>
          {errors.claimAmount && <Text style={styles.errorText}>{errors.claimAmount}</Text>}
        </View>

        <TouchableOpacity
          style={[
            styles.submitButton,
            isSubmitting ? styles.disabledButton : {}
          ]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <Text style={styles.submitButtonText}>
            {isSubmitting ? 'Submitting...' : 'Create Claim'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default ClaimForm;
