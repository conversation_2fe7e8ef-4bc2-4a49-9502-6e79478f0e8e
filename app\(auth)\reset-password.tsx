import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { resetPassword } from '@/store/authSlice';
import { Lock, ArrowLeft, Eye, EyeOff, CheckCircle } from 'lucide-react-native';

export default function ResetPasswordScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  const { isLoading } = useAppSelector((state) => state.auth);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Clear errors
  const clearErrors = () => {
    setPasswordError('');
    setConfirmPasswordError('');
  };

  // Validate password
  const validatePassword = () => {
    if (!newPassword) {
      setPasswordError('Password is required');
      return false;
    }
    if (newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters');
      return false;
    }
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
      setPasswordError('Password must contain uppercase, lowercase, and number');
      return false;
    }
    return true;
  };

  // Validate confirm password
  const validateConfirmPassword = () => {
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      return false;
    }
    if (newPassword !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match');
      return false;
    }
    return true;
  };

  // Handle reset password
  const handleResetPassword = async () => {
    console.log('Reset password button pressed');
    clearErrors();

    // Validate inputs
    const isPasswordValid = validatePassword();
    const isConfirmPasswordValid = validateConfirmPassword();

    if (!isPasswordValid || !isConfirmPasswordValid) {
      console.log('Validation failed');
      return;
    }

    if (!token) {
      Alert.alert('Invalid Link', 'This reset link is invalid or has expired.');
      return;
    }

    try {
      const resultAction = await dispatch(resetPassword({
        token,
        new_password: newPassword,
        confirm_password: confirmPassword,
      }));

      if (resetPassword.fulfilled.match(resultAction)) {
        console.log('Password reset successful');
        setIsSubmitted(true);
      } else if (resetPassword.rejected.match(resultAction)) {
        const errorMessage = resultAction.payload as string || 'Failed to reset password';
        console.log('Password reset failed:', errorMessage);
        Alert.alert('Reset Failed', errorMessage);
      }
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert('Reset Error', 'An unexpected error occurred. Please try again.');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
    },
    backButton: {
      padding: spacing.sm,
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
    },
    content: {
      flex: 1,
      paddingHorizontal: spacing.lg,
      paddingTop: spacing.xl,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xxl,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      marginBottom: spacing.xl,
      lineHeight: 22,
    },
    inputContainer: {
      marginBottom: spacing.lg,
    },
    label: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      backgroundColor: colors.card,
      paddingHorizontal: spacing.md,
    },
    inputWrapperFocused: {
      borderColor: colors.primary[500],
    },
    inputWrapperError: {
      borderColor: colors.error,
    },
    input: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      paddingVertical: spacing.md,
    },
    eyeButton: {
      padding: spacing.xs,
    },
    errorText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.error,
      marginTop: spacing.xs,
    },
    resetButton: {
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.md,
      borderRadius: borders.radius.md,
      alignItems: 'center',
      marginTop: spacing.lg,
    },
    resetButtonText: {
      fontFamily: typography.fonts.semibold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
    successContainer: {
      alignItems: 'center',
      paddingTop: spacing.xl,
    },
    successIcon: {
      marginBottom: spacing.lg,
    },
    successTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.success,
      marginBottom: spacing.md,
    },
    successMessage: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: 22,
      marginBottom: spacing.xl,
    },
    backToLoginButton: {
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.xl,
      borderRadius: borders.radius.md,
    },
    backToLoginText: {
      fontFamily: typography.fonts.semibold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reset Password</Text>
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {!isSubmitted ? (
          <>
            <Text style={styles.title}>Create New Password</Text>
            <Text style={styles.subtitle}>
              Please enter your new password. Make sure it's strong and secure.
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>New Password *</Text>
              <View style={[
                styles.inputWrapper,
                passwordError ? styles.inputWrapperError : null,
              ]}>
                <Lock size={20} color={colors.textSecondary} style={{ marginRight: spacing.sm }} />
                <TextInput
                  style={styles.input}
                  value={newPassword}
                  onChangeText={(text) => {
                    setNewPassword(text);
                    if (passwordError) setPasswordError('');
                  }}
                  placeholder="Enter new password"
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry={!showNewPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff size={20} color={colors.textSecondary} />
                  ) : (
                    <Eye size={20} color={colors.textSecondary} />
                  )}
                </TouchableOpacity>
              </View>
              {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Confirm Password *</Text>
              <View style={[
                styles.inputWrapper,
                confirmPasswordError ? styles.inputWrapperError : null,
              ]}>
                <Lock size={20} color={colors.textSecondary} style={{ marginRight: spacing.sm }} />
                <TextInput
                  style={styles.input}
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text);
                    if (confirmPasswordError) setConfirmPasswordError('');
                  }}
                  placeholder="Confirm new password"
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff size={20} color={colors.textSecondary} />
                  ) : (
                    <Eye size={20} color={colors.textSecondary} />
                  )}
                </TouchableOpacity>
              </View>
              {confirmPasswordError ? <Text style={styles.errorText}>{confirmPasswordError}</Text> : null}
            </View>

            <TouchableOpacity
              style={styles.resetButton}
              onPress={handleResetPassword}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <Text style={styles.resetButtonText}>Reset Password</Text>
              )}
            </TouchableOpacity>
          </>
        ) : (
          <View style={styles.successContainer}>
            <View style={styles.successIcon}>
              <CheckCircle size={64} color={colors.success} />
            </View>
            <Text style={styles.successTitle}>Password Reset Successfully!</Text>
            <Text style={styles.successMessage}>
              Your password has been reset successfully. You can now log in with your new password.
            </Text>
            <TouchableOpacity
              style={styles.backToLoginButton}
              onPress={() => router.push('/(auth)/login')}
            >
              <Text style={styles.backToLoginText}>Back to Login</Text>
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
