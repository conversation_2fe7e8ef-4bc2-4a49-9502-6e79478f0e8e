import { useState, useRef } from 'react';
import { View, StyleSheet, Text, Pressable, Dimensions, FlatList } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { createTheme } from '@/constants/theme';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  interpolate,
  Extrapolation
} from 'react-native-reanimated';
import { ChevronRight, ArrowRight } from 'lucide-react-native';
import OnboardingCard from '@/components/onboarding/OnboardingCard';
import { useTheme } from '@/context/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppSelector } from '@/store/hooks';

// Move width initialization to the top level, before any usage
const { width } = Dimensions.get('window');

// Onboarding content
const onboardingData = [
  {
    id: '1',
    title: 'Welcome to Inerca',
    description: 'Your comprehensive insurance management platform. Take control of all your insurance policies in one place.',
    imageUrl: 'https://images.pexels.com/photos/3943716/pexels-photo-3943716.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: '2',
    title: 'Insurance Made Simple',
    description: 'Get quotes, manage policies, and track claims with ease. Everything you need is at your fingertips.',
    imageUrl: 'https://images.pexels.com/photos/7821486/pexels-photo-7821486.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  },
  {
    id: '3',
    title: 'Secure & Reliable',
    description: 'Your data is protected with enterprise-grade security. Access your information anytime, anywhere.',
    imageUrl: 'https://images.pexels.com/photos/4386338/pexels-photo-4386338.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'
  }
];

export default function OnboardingScreen() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useSharedValue(0);
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Get auth state from Zustand store
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated);

  const completeOnboarding = async () => {
    try {
      // Save onboarding completion status
      await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      console.log('Onboarding marked as completed');

      if (isAuthenticated) {
        // User is already authenticated, go to app
        router.replace('/(app)/(tabs)/index');
      } else {
        // User needs to authenticate
        router.replace('/(auth)/login');
      }
    } catch (error) {
      console.error('Failed to save onboarding status:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleSkip = () => {
    completeOnboarding();
  };

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
      setCurrentIndex(nextIndex);
    } else {
      // Onboarding complete
      completeOnboarding();
    }
  };

  const handleScroll = (event: any) => {
    scrollX.value = event.nativeEvent.contentOffset.x;
    const index = Math.round(event.nativeEvent.contentOffset.x / width);
    if (index !== currentIndex) {
      setCurrentIndex(index);
    }
  };

  const renderPagination = () => {
    return (
      <View style={styles.paginationContainer}>
        {onboardingData.map((_, index) => {
          const animatedDotStyle = useAnimatedStyle(() => {
            const inputRange = [
              (index - 1) * width,
              index * width,
              (index + 1) * width
            ];

            const dotWidth = interpolate(
              scrollX.value,
              inputRange,
              [8, 20, 8],
              Extrapolation.CLAMP
            );

            const opacity = interpolate(
              scrollX.value,
              inputRange,
              [0.5, 1, 0.5],
              Extrapolation.CLAMP
            );

            return {
              width: dotWidth,
              opacity,
              backgroundColor: index === currentIndex
                ? colors.primary[500]
                : colors.textSecondary
            };
          });

          return (
            <Animated.View key={index} style={[styles.paginationDot, animatedDotStyle]} />
          );
        })}
      </View>
    );
  };

  // Create styles with the current theme
  const styles = getStyles(colors, spacing, typography, borders);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <Pressable style={styles.skipButton} onPress={handleSkip}>
          <Text style={[styles.skipText, { color: colors.textSecondary }]}>Skip</Text>
        </Pressable>
      </View>

      <FlatList
        ref={flatListRef}
        data={onboardingData}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <OnboardingCard data={item} />}
      />

      {renderPagination()}

      <View style={styles.footer}>
        <Pressable
          style={styles.nextButton}
          onPress={handleNext}
        >
          <Text style={styles.nextButtonText}>
            {currentIndex === onboardingData.length - 1 ? 'Get Started' : 'Next'}
          </Text>
          {currentIndex === onboardingData.length - 1 ? (
            <ArrowRight size={20} color={colors.white} />
          ) : (
            <ChevronRight size={20} color={colors.white} />
          )}
        </Pressable>
      </View>
    </SafeAreaView>
  );
}

const getStyles = (colors: any, spacing: any, typography: any, borders: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  skipButton: {
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
  },
  skipText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.sm,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  paginationDot: {
    height: 8,
    borderRadius: borders.radius.full,
    marginHorizontal: 4,
  },
  footer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  nextButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borders.radius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    color: colors.white,
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.md,
    marginRight: spacing.xs,
  },
});
