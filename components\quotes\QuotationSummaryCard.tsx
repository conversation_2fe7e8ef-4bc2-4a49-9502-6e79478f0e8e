import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Car, Home, Plane, Heart, Shield, Briefcase, Calendar, FileText, ChevronRight, Edit, Eye, Trash2 } from 'lucide-react-native';
import { Quote, InsuranceProductType } from '@/types/quote.types';
import Animated, { FadeInDown } from 'react-native-reanimated';
import useQuoteStore from '@/store/quoteStore';
import { formatCurrency } from '@/utils/quoteCalculations';

interface QuotationSummaryCardProps {
  quote: Quote;
  onViewDetails: (quote: Quote) => void;
  onViewPdf?: (quote: Quote) => void;
}

const QuotationSummaryCard: React.FC<QuotationSummaryCardProps> = ({
  quote,
  onViewDetails,
  onViewPdf,
}) => {
  // Add error handling for theme context
  const themeContext = useTheme();
  const isDarkMode = themeContext?.isDarkMode ?? false;

  // Create theme with fallback
  const theme = createTheme(isDarkMode);
  const { colors, spacing, typography, borders, shadows } = theme;

  // Get the deleteQuote function from the store
  const { deleteQuote } = useQuoteStore();

  // Handle delete quote with confirmation
  const handleDeleteQuote = () => {
    Alert.alert(
      'Delete Quote',
      'Are you sure you want to delete this quote? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteQuote(quote.id);
            } catch (error) {
              console.error('Error deleting quote:', error);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Get the appropriate icon based on the insurance type
  const getIcon = (type: InsuranceProductType) => {
    switch (type) {
      case 'motor':
        return <Car size={24} color={colors.primary[500]} />;
      case 'houseowners':
      case 'householdContents':
        return <Home size={24} color={colors.primary[500]} />;
      case 'allRisks':
        return <Shield size={24} color={colors.primary[500]} />;
      case 'travel':
        return <Plane size={24} color={colors.primary[500]} />;
      case 'health':
        return <Heart size={24} color={colors.primary[500]} />;
      case 'life':
      case 'funeral':
        return <Shield size={24} color={colors.primary[500]} />;
      case 'scheme':
      case 'business':
        return <Briefcase size={24} color={colors.primary[500]} />;
      default:
        return <Shield size={24} color={colors.primary[500]} />;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'quoted':
        return colors.success[500];
      case 'draft':
        return colors.warning[500];
      case 'expired':
        return colors.error[500];
      case 'submitted':
        return colors.primary[500]; // Use primary instead of info
      default:
        return colors.neutral[500];
    }
  };

  // Using the formatCurrency function from utils/quoteCalculations

  // Capitalize first letter
  const capitalize = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      marginBottom: spacing.md,
      overflow: 'hidden',
      ...shadows.sm,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: borders.radius.md,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: `${colors.primary[500]}15`,
      marginRight: spacing.md,
    },
    headerContent: {
      flex: 1,
    },
    quoteType: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    quoteId: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: 2,
    },
    statusBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.full,
      alignSelf: 'flex-start',
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      textTransform: 'capitalize',
    },
    content: {
      padding: spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    infoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      textAlign: 'right',
    },
    premiumValue: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.primary[500],
    },
    footer: {
      flexDirection: 'row',
      padding: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    footerButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.sm,
    },
    footerButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    divider: {
      width: 1,
      backgroundColor: colors.border,
    },
  });

  return (
    <Animated.View
      entering={FadeInDown.delay(100).springify()}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            {getIcon(quote.type)}
          </View>
          <View style={styles.headerContent}>
            <Text style={styles.quoteType}>{capitalize(quote.type)} Insurance</Text>
            <Text style={styles.quoteId}>Quote #{quote.id}</Text>
          </View>
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: `${getStatusColor(quote.status)}15` },
            ]}
          >
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor(quote.status) },
              ]}
            >
              {quote.status}
            </Text>
          </View>
        </View>

        <View style={styles.content}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Client</Text>
            <Text style={styles.infoValue}>
              {quote.clientInfo.firstName} {quote.clientInfo.lastName}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created</Text>
            <Text style={styles.infoValue}>{quote.createdAt}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Expires</Text>
            <Text style={styles.infoValue}>{quote.expiresAt}</Text>
          </View>

          {quote.premium !== undefined && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Premium</Text>
              <Text style={styles.premiumValue}>
                {formatCurrency(quote.premium, quote.currency)}
              </Text>
            </View>
          )}

          {quote.coverAmount !== undefined && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Cover Amount</Text>
              <Text style={styles.infoValue}>
                {formatCurrency(quote.coverAmount, quote.currency)}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.footerButton}
            onPress={() => {
              try {
                if (onViewDetails) {
                  onViewDetails(quote);
                }
              } catch (error) {
                console.error('Error viewing quote details:', error);
              }
            }}
          >
            {quote.status === 'draft' ? (
              <>
                <Edit size={16} color={colors.primary[500]} />
                <Text style={styles.footerButtonText}>Continue Quote</Text>
              </>
            ) : (
              <>
                <Eye size={16} color={colors.primary[500]} />
                <Text style={styles.footerButtonText}>View Details</Text>
              </>
            )}
          </TouchableOpacity>

          {quote.pdfUrl && onViewPdf && (
            <>
              <View style={styles.divider} />
              <TouchableOpacity
                style={styles.footerButton}
                onPress={() => {
                  try {
                    onViewPdf(quote);
                  } catch (error) {
                    console.error('Error viewing quote PDF:', error);
                  }
                }}
              >
                <FileText size={16} color={colors.primary[500]} />
                <Text style={styles.footerButtonText}>View PDF</Text>
              </TouchableOpacity>
            </>
          )}

          {/* Add delete button for draft quotes */}
          {quote.status === 'draft' && (
            <>
              <View style={styles.divider} />
              <TouchableOpacity
                style={styles.footerButton}
                onPress={handleDeleteQuote}
              >
                <Trash2 size={16} color={colors.error[500]} />
                <Text style={[styles.footerButtonText, { color: colors.error[500] }]}>Delete</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

export default QuotationSummaryCard;
