import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { DollarSign, ArrowUp, ArrowDown } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { formatCurrency } from '@/utils/quoteCalculations';

interface PremiumBreakdownProps {
  breakdown: Record<string, number>;
  currency?: string;
}

const PremiumBreakdown: React.FC<PremiumBreakdownProps> = ({
  breakdown,
  currency = 'P'
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  
  // Format the breakdown items for display
  const getBreakdownItems = () => {
    const items = [];
    
    // Base premium
    if ('basePremium' in breakdown) {
      items.push({
        name: 'Base Premium',
        amount: breakdown.basePremium,
        isBase: true,
      });
    }
    
    // Age adjustment
    if ('ageAdjustment' in breakdown) {
      items.push({
        name: 'Age Factor',
        amount: breakdown.ageAdjustment,
        isAdjustment: true,
      });
    }
    
    // Term adjustment
    if ('termAdjustment' in breakdown) {
      items.push({
        name: 'Term Length',
        amount: breakdown.termAdjustment,
        isAdjustment: true,
      });
    }
    
    // Occupation adjustment
    if ('occupationAdjustment' in breakdown) {
      items.push({
        name: 'Occupation Risk',
        amount: breakdown.occupationAdjustment,
        isAdjustment: true,
      });
    }
    
    // Health adjustment
    if ('healthAdjustment' in breakdown) {
      items.push({
        name: 'Health Conditions',
        amount: breakdown.healthAdjustment,
        isAdjustment: true,
      });
    }
    
    // Smoker adjustment
    if ('smokerAdjustment' in breakdown) {
      items.push({
        name: 'Smoker Status',
        amount: breakdown.smokerAdjustment,
        isAdjustment: true,
      });
    }
    
    // Total premium
    if ('totalPremium' in breakdown) {
      items.push({
        name: 'Total Premium',
        amount: breakdown.totalPremium,
        isTotal: true,
      });
    }
    
    return items;
  };
  
  const breakdownItems = getBreakdownItems();
  
  // Render a breakdown item
  const renderBreakdownItem = (item: any, index: number) => {
    const isPositive = item.amount > 0;
    const isNegative = item.amount < 0;
    
    return (
      <Animated.View 
        key={item.name}
        style={[
          styles.breakdownItem,
          item.isTotal && [
            styles.totalItem,
            { borderTopColor: colors.border }
          ]
        ]}
        entering={FadeInDown.delay(index * 100).springify()}
      >
        <View style={styles.breakdownItemLeft}>
          {item.isAdjustment && isPositive && (
            <ArrowUp size={16} color={colors.error[500]} style={styles.icon} />
          )}
          {item.isAdjustment && isNegative && (
            <ArrowDown size={16} color={colors.success[500]} style={styles.icon} />
          )}
          {item.isBase && (
            <DollarSign size={16} color={colors.primary[500]} style={styles.icon} />
          )}
          <Text 
            style={[
              styles.breakdownItemName,
              { color: colors.text },
              item.isTotal && styles.totalText
            ]}
          >
            {item.name}
          </Text>
        </View>
        
        <Text 
          style={[
            styles.breakdownItemAmount,
            { color: colors.text },
            item.isTotal && styles.totalText,
            item.isAdjustment && isPositive && { color: colors.error[500] },
            item.isAdjustment && isNegative && { color: colors.success[500] }
          ]}
        >
          {item.isAdjustment && isPositive && '+'}
          {formatCurrency(item.amount, currency)}
        </Text>
      </Animated.View>
    );
  };
  
  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>Premium Breakdown</Text>
      
      <View 
        style={[
          styles.breakdownContainer,
          { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }
        ]}
      >
        {breakdownItems.map(renderBreakdownItem)}
      </View>
      
      <Text style={[styles.note, { color: colors.textSecondary }]}>
        This premium breakdown shows how different factors affect your life assurance premium.
        Positive adjustments increase your premium, while negative adjustments decrease it.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  breakdownContainer: {
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  breakdownItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  breakdownItemName: {
    fontSize: 14,
  },
  breakdownItemAmount: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalItem: {
    borderTopWidth: 1,
    paddingTop: 16,
    paddingBottom: 16,
  },
  totalText: {
    fontSize: 16,
    fontWeight: '600',
  },
  note: {
    fontSize: 12,
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default PremiumBreakdown;
