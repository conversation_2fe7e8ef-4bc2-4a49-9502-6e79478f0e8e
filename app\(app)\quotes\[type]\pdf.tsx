import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { ArrowLeft } from 'lucide-react-native';
import QuotePDFPreview from '@/components/quotes/QuotePDFPreview';
import useQuoteStore from '@/store/quoteStore';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { formatCurrency } from '@/utils/quoteCalculations';

export default function QuotePDFScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { quoteId } = useLocalSearchParams();

  // Get quote store functions
  const { getQuoteById, setCurrentQuote, currentQuote } = useQuoteStore();

  // Load quote data
  useEffect(() => {
    if (quoteId) {
      const quote = getQuoteById(quoteId as string);
      if (quote) {
        setCurrentQuote(quote);
      } else {
        // Quote not found, go back
        router.back();
      }
    }
  }, [quoteId, getQuoteById, setCurrentQuote]);

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: spacing.lg,
    },
    noQuoteContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    noQuoteText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    quoteInfoContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.lg,
      marginBottom: spacing.lg,
    },
    quoteInfoTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    quoteInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    quoteInfoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    quoteInfoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      textAlign: 'right',
    },
    premiumValue: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.primary[500],
    },
  });

  // Using the imported formatCurrency function from utils/quoteCalculations

  // Capitalize first letter
  const capitalize = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quote PDF</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {currentQuote ? (
          <>
            <Animated.View
              style={styles.quoteInfoContainer}
              entering={FadeInDown.delay(100).springify()}
            >
              <Text style={styles.quoteInfoTitle}>Quote Information</Text>

              <View style={styles.quoteInfoRow}>
                <Text style={styles.quoteInfoLabel}>Quote ID</Text>
                <Text style={styles.quoteInfoValue}>#{currentQuote.id}</Text>
              </View>

              <View style={styles.quoteInfoRow}>
                <Text style={styles.quoteInfoLabel}>Insurance Type</Text>
                <Text style={styles.quoteInfoValue}>{capitalize(currentQuote.type)}</Text>
              </View>

              <View style={styles.quoteInfoRow}>
                <Text style={styles.quoteInfoLabel}>Status</Text>
                <Text style={styles.quoteInfoValue}>{capitalize(currentQuote.status)}</Text>
              </View>

              <View style={styles.quoteInfoRow}>
                <Text style={styles.quoteInfoLabel}>Created</Text>
                <Text style={styles.quoteInfoValue}>{currentQuote.createdAt}</Text>
              </View>

              <View style={styles.quoteInfoRow}>
                <Text style={styles.quoteInfoLabel}>Expires</Text>
                <Text style={styles.quoteInfoValue}>{currentQuote.expiresAt}</Text>
              </View>

              {currentQuote.premium !== undefined && (
                <View style={styles.quoteInfoRow}>
                  <Text style={styles.quoteInfoLabel}>Premium</Text>
                  <Text style={styles.premiumValue}>
                    {formatCurrency(currentQuote.premium, currentQuote.currency)}
                  </Text>
                </View>
              )}
            </Animated.View>

            <QuotePDFPreview
              quote={currentQuote}
              pdfUrl={currentQuote.pdfUrl || 'https://www.africau.edu/images/default/sample.pdf'}
            />
          </>
        ) : (
          <View style={styles.noQuoteContainer}>
            <Text style={styles.noQuoteText}>Quote not found</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
