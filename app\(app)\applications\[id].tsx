import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft, Clock, Check, AlertCircle, User, Mail, Phone,
  MapPin, FileBarChart, ClipboardList, FileCheck, FileText,
  CreditCard, Shield, Calendar, DollarSign, Building
} from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import Animated, { FadeInDown } from 'react-native-reanimated';
import useApplicationStore, { Application, PaymentMethod } from '@/store/applicationStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import TabNavigation from '@/components/navigation/TabNavigation';
import ApplicationStatusTimeline from '@/components/applications/ApplicationStatusTimeline';
import ApplicationTimelineVisualization from '@/components/applications/ApplicationTimelineVisualization';
import { TimelineStep } from '@/types/application.types';
import ApplicationDetailSection from '@/components/applications/ApplicationDetailSection';
import RequiredActionNotice from '@/components/applications/RequiredActionNotice';
import PaymentProcessingSection from '@/components/applications/PaymentProcessingSection';
import BottomNavBar from '@/components/navigation/BottomNavBar';

export default function ApplicationDetailScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getApplicationById, fetchApplications, isLoading } = useApplicationStore();
  const [application, setApplication] = useState<Application | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Load application details
  useEffect(() => {
    const loadApplication = async () => {
      try {
        // First fetch all applications to ensure we have the latest data
        await fetchApplications();

        if (id) {
          // Now get the specific application by ID
          const app = await getApplicationById(id);
          if (app) {
            setApplication(app);
          }
        }
      } catch (error) {
        console.error('Error loading application:', error);
      }
    };

    loadApplication();
  }, [id, fetchApplications, getApplicationById]);

  // Get status color and icon
  const getStatusInfo = (status: Application['status']) => {
    switch (status) {
      case 'quote_accepted':
        return {
          color: colors.primary[500],
          bgColor: colors.primary[50],
          icon: <Check size={16} color={colors.primary[500]} />,
          text: 'Quote Accepted',
          progressPercentage: 10,
        };
      case 'submitted':
        return {
          color: colors.primary[500],
          bgColor: colors.primary[50],
          icon: <FileText size={16} color={colors.primary[500]} />,
          text: 'Submitted',
          progressPercentage: 20,
        };
      case 'payment_pending':
        return {
          color: colors.warning[500],
          bgColor: colors.warning[50],
          icon: <CreditCard size={16} color={colors.warning[500]} />,
          text: 'Payment Pending',
          progressPercentage: 30,
        };
      case 'payment_verified':
        return {
          color: colors.primary[500],
          bgColor: colors.primary[50],
          icon: <CreditCard size={16} color={colors.primary[500]} />,
          text: 'Payment Verified',
          progressPercentage: 40,
        };
      case 'underwriting':
        return {
          color: colors.primary[700],
          bgColor: colors.primary[50],
          icon: <ClipboardList size={16} color={colors.primary[700]} />,
          text: 'Underwriting',
          progressPercentage: 60,
        };
      case 'additional_info':
        return {
          color: colors.warning[500],
          bgColor: colors.warning[50],
          icon: <AlertCircle size={16} color={colors.warning[500]} />,
          text: 'Additional Info Needed',
          progressPercentage: 70,
        };
      case 'approved':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Approved',
          progressPercentage: 80,
        };
      case 'approved_with_terms':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Approved with Terms',
          progressPercentage: 85,
        };
      case 'policy_issued':
        return {
          color: colors.success[700],
          bgColor: colors.success[50],
          icon: <Shield size={16} color={colors.success[700]} />,
          text: 'Policy Issued',
          progressPercentage: 100,
        };
      case 'rejected':
        return {
          color: colors.error[500],
          bgColor: colors.error[50],
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          text: 'Rejected',
          progressPercentage: 100,
        };
      default:
        return {
          color: colors.textSecondary,
          bgColor: colors.card,
          icon: <Clock size={16} color={colors.textSecondary} />,
          text: 'Unknown Status',
          progressPercentage: 0,
        };
    }
  };

  // Get document status color and icon
  const getDocumentStatusInfo = (status: string) => {
    switch (status) {
      case 'verified':
        return {
          color: colors.success[500],
          bgColor: colors.success[50],
          icon: <Check size={16} color={colors.success[500]} />,
          text: 'Verified',
        };
      case 'rejected':
        return {
          color: colors.error[500],
          bgColor: colors.error[50],
          icon: <AlertCircle size={16} color={colors.error[500]} />,
          text: 'Rejected',
        };
      default:
        return {
          color: colors.warning[500],
          bgColor: colors.warning[50],
          icon: <Clock size={16} color={colors.warning[500]} />,
          text: 'Pending',
        };
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
      marginBottom: Platform.OS === 'ios' ? 88 : 60, // Add margin for bottom nav bar
    },
    tabContent: {
      flex: 1,
      marginTop: spacing.md,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
      marginTop: spacing.lg,
    },
    progressContainer: {
      marginVertical: spacing.md,
    },
    progressBackground: {
      height: 8,
      backgroundColor: colors.neutral[200],
      borderRadius: 4,
    },
    progressBar: {
      height: 8,
      borderRadius: 4,
    },
    progressText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginTop: spacing.xs,
      textAlign: 'right',
    },
    documentStats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: spacing.md,
    },
    documentStatItem: {
      alignItems: 'center',
    },
    documentStatValue: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
    },
    documentStatLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginTop: spacing.xs,
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    label: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      width: 120,
    },
    value: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      flex: 1,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.full,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: spacing.xs,
    },
    documentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    documentName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    documentDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginTop: 2,
    },
    timelineContainer: {
      marginTop: spacing.md,
    },
    timelineItem: {
      flexDirection: 'row',
      marginBottom: spacing.md,
    },
    timelineLeft: {
      width: 24,
      alignItems: 'center',
    },
    timelineLine: {
      width: 2,
      flex: 1,
      backgroundColor: colors.border,
      marginVertical: 4,
    },
    timelineCircle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    timelineContent: {
      flex: 1,
      marginLeft: spacing.md,
    },
    timelineTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    timelineDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginTop: 2,
    },
    timelineDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginTop: 2,
    },
    notes: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      lineHeight: typography.lineHeights.relaxed * typography.sizes.md,
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Application Details</Text>
        </View>
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={[styles.value, { marginTop: spacing.md }]}>Loading application details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!application) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Application Details</Text>
        </View>
        <View style={[styles.content, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.value}>Application not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const statusInfo = getStatusInfo(application.status);

  // Define tabs
  const tabs = [
    {
      id: 'overview',
      title: 'Overview',
      icon: <FileBarChart size={16} color={activeTab === 'overview' ? colors.primary[500] : colors.textSecondary} />
    },
    {
      id: 'status',
      title: 'Status Timeline',
      icon: <ClipboardList size={16} color={activeTab === 'status' ? colors.primary[500] : colors.textSecondary} />
    },
    {
      id: 'documents',
      title: 'Documents',
      icon: <FileCheck size={16} color={activeTab === 'documents' ? colors.primary[500] : colors.textSecondary} />
    },
    {
      id: 'payment',
      title: 'Payment',
      icon: <CreditCard size={16} color={activeTab === 'payment' ? colors.primary[500] : colors.textSecondary} />
    }
  ];

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Convert timeline data for the timeline component with enhanced details
  const timelineSteps = application?.timeline.map(item => {
    // Determine status with more granularity
    let status: TimelineStep['status'];

    // Map the application timeline status to our component status
    switch (item.status) {
      case 'completed':
        status = 'completed';
        break;
      case 'current':
        status = 'current';
        break;
      case 'upcoming':
        status = 'upcoming';
        break;
      default:
        // For any other status, default to upcoming
        status = 'upcoming';
    }

    // For specific steps, we can override with more detailed status
    if (item.title === 'Underwriting' && application.underwriting?.status === 'in_progress') {
      status = 'in_progress';
    } else if (item.title === 'Document Verification' &&
              application.documents.some(doc => doc.status === 'pending')) {
      status = 'in_progress';
    }

    // Add estimated completion dates based on status
    let estimatedCompletionDate: string | undefined;
    if (status === 'current' || status === 'in_progress') {
      const today = new Date();
      today.setDate(today.getDate() + Math.floor(Math.random() * 7) + 1); // Random 1-7 days
      estimatedCompletionDate = today.toISOString().split('T')[0];
    }

    // Add assignee for underwriting steps
    let assignedTo: string | undefined;
    if (item.title === 'Underwriting' && (status === 'current' || status === 'in_progress')) {
      assignedTo = application.underwriting?.assignedTo || 'Underwriting Team';
    }

    // Add progress for current steps
    let progress: number | undefined;
    if (status === 'current' || status === 'in_progress') {
      progress = Math.floor(Math.random() * 70) + 10; // Random 10-80%
    } else if (status === 'completed') {
      progress = 100;
    }

    // Add detailed substeps for certain timeline items
    let subSteps: TimelineStep['subSteps'] = [];

    if (item.title === 'Document Verification') {
      // Add substeps for each document
      subSteps = application.documents.map(doc => ({
        id: `substep-${doc.id}`,
        title: doc.name,
        description: `Document ${doc.status === 'verified' ? 'verified' : doc.status === 'rejected' ? 'rejected' : 'pending verification'}`,
        status: doc.status === 'verified' ? 'completed' :
                doc.status === 'rejected' ? 'rejected' : 'in_progress',
        date: doc.verificationDate || doc.date,
        icon: doc.status === 'verified' ? 'file-check' :
              doc.status === 'rejected' ? 'file-warning' : 'file-search',
        details: doc.rejectionReason
      }));
    } else if (item.title === 'Underwriting' && application.underwriting) {
      // Add substeps for underwriting process
      const underwriting = application.underwriting;

      if (underwriting.status !== 'pending') {
        subSteps = [
          {
            id: 'substep-underwriting-1',
            title: 'Initial Review',
            description: 'Document verification and basic eligibility check',
            status: 'completed',
            date: underwriting.startDate,
            icon: 'file-check'
          },
          {
            id: 'substep-underwriting-2',
            title: 'Risk Assessment',
            description: 'Detailed risk analysis and evaluation',
            status: underwriting.status === 'in_progress' ? 'current' : 'completed',
            date: new Date(underwriting.startDate || '').toISOString().split('T')[0],
            icon: 'clipboard'
          }
        ];

        if (underwriting.status === 'completed') {
          subSteps.push({
            id: 'substep-underwriting-3',
            title: 'Final Decision',
            description: `Application ${underwriting.decision || 'processed'}`,
            status: underwriting.decision === 'rejected' ? 'rejected' : 'completed',
            date: underwriting.completionDate,
            icon: underwriting.decision === 'rejected' ? 'alert-circle' : 'check-circle',
            details: underwriting.decisionReason
          });
        }
      }
    } else if (item.title === 'Payment' && application.payment) {
      // Add substeps for payment process
      const payment = application.payment;

      if (payment.method !== 'none') {
        subSteps = [
          {
            id: 'substep-payment-1',
            title: 'Payment Method Selected',
            description: `${payment.method === 'eft' ? 'Electronic Funds Transfer' : 'Direct Debit'}`,
            status: 'completed',
            date: application.date, // Use application date as fallback
            icon: 'credit-card'
          }
        ];

        if (payment.status === 'pending' || payment.status === 'verified') {
          subSteps.push({
            id: 'substep-payment-2',
            title: 'Payment Initiated',
            description: `${formatCurrency(payment.amount, payment.currency)} payment initiated`,
            status: 'completed',
            date: payment.paidDate || application.date,
            icon: 'credit-card'
          });
        }

        if (payment.status === 'verified') {
          subSteps.push({
            id: 'substep-payment-3',
            title: 'Payment Verified',
            description: `Payment of ${formatCurrency(payment.amount, payment.currency)} verified`,
            status: 'completed',
            date: payment.paidDate,
            icon: 'check-circle'
          });
        }
      }
    }

    // Enhanced actions with priorities
    const enhancedActions = item.actions?.map(action => ({
      label: action.label,
      action: action.action,
      route: action.action === 'navigate_to_payment' ? '/payments' :
             action.action === 'navigate_to_documents' ? '/documents' : undefined,
      params: action.action === 'navigate_to_payment' ? { applicationId: application.id } : undefined,
      priority: action.action === 'navigate_to_payment' ? 'high' as const : undefined,
      icon: action.action === 'navigate_to_payment' ? 'credit-card' :
            action.action === 'navigate_to_documents' ? 'file-text' : undefined
    }));

    return {
      id: item.id,
      title: item.title,
      description: item.description,
      date: item.date || '',
      status,
      icon: item.icon,
      actions: enhancedActions,
      details: item.description, // Use description as details since the original timeline doesn't have details
      progress,
      subSteps: subSteps.length > 0 ? subSteps : undefined,
      estimatedCompletionDate,
      assignedTo
    };
  }) || [];

  // Handle payment method update
  const handleUpdatePaymentMethod = async (method: PaymentMethod) => {
    try {
      await useApplicationStore.getState().updatePaymentMethod(application.id, method);
      return true;
    } catch (error) {
      console.error('Error updating payment method:', error);
      return false;
    }
  };



  // Render content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <>
            {application.requiredActions && application.requiredActions.length > 0 && (
              <RequiredActionNotice
                actions={application.requiredActions.map(action => ({
                  ...action,
                  action: action.description.includes('payment') ? {
                    label: 'Make Payment',
                    onPress: () => setActiveTab('payment')
                  } : undefined
                }))}
                onComplete={(actionId) => console.log('Complete action:', actionId)}
              />
            )}

            <ApplicationDetailSection
              title="Application Details"
              icon={<FileText size={24} color={colors.primary[500]} />}
              initiallyExpanded={true}
            >
              <View style={[styles.row, { justifyContent: 'space-between' }]}>
                <Text style={[styles.value, { fontSize: typography.sizes.lg }]}>{application.type}</Text>
                <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
                  {statusInfo.icon}
                  <Text style={[styles.statusText, { color: statusInfo.color }]}>
                    {statusInfo.text}
                  </Text>
                </View>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Reference:</Text>
                <Text style={styles.value}>{application.reference}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Date:</Text>
                <Text style={styles.value}>{application.date}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Premium:</Text>
                <Text style={styles.value}>{formatCurrency(application.premium, application.currency)}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Cover Amount:</Text>
                <Text style={styles.value}>{formatCurrency(application.coverAmount, application.currency)}</Text>
              </View>
            </ApplicationDetailSection>

            <ApplicationDetailSection
              title="Client Information"
              icon={<User size={24} color={colors.primary[500]} />}
              initiallyExpanded={true}
            >
              <View style={styles.row}>
                <User size={20} color={colors.textSecondary} style={{ marginRight: spacing.sm }} />
                <Text style={styles.value}>{application.clientInfo.firstName} {application.clientInfo.lastName}</Text>
              </View>
              <View style={styles.row}>
                <Mail size={20} color={colors.textSecondary} style={{ marginRight: spacing.sm }} />
                <Text style={styles.value}>{application.clientInfo.email}</Text>
              </View>
              <View style={styles.row}>
                <Phone size={20} color={colors.textSecondary} style={{ marginRight: spacing.sm }} />
                <Text style={styles.value}>{application.clientInfo.phone}</Text>
              </View>
              {application.clientInfo.address && (
                <View style={styles.row}>
                  <MapPin size={20} color={colors.textSecondary} style={{ marginRight: spacing.sm }} />
                  <Text style={styles.value}>{application.clientInfo.address}</Text>
                </View>
              )}
            </ApplicationDetailSection>

            {application.payment && (
              <ApplicationDetailSection
                title="Payment Information"
                icon={<CreditCard size={24} color={colors.primary[500]} />}
                initiallyExpanded={true}
                badge={application.payment.status === 'not_started' || application.payment.status === 'pending' ?
                  { count: 1, color: colors.warning[500] } : undefined}
              >
                <View style={styles.row}>
                  <Text style={styles.label}>Method:</Text>
                  <Text style={styles.value}>
                    {application.payment.method === 'eft' ? 'Electronic Funds Transfer (EFT)' :
                     application.payment.method === 'direct_debit' ? 'Direct Debit Order' : 'Not Selected'}
                  </Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Status:</Text>
                  <View style={[styles.statusBadge, {
                    backgroundColor: application.payment.status === 'verified' ? colors.success[50] :
                                    application.payment.status === 'pending' ? colors.warning[50] :
                                    colors.neutral[100]
                  }]}>
                    {application.payment.status === 'verified' ?
                      <Check size={16} color={colors.success[500]} /> :
                      application.payment.status === 'pending' ?
                      <Clock size={16} color={colors.warning[500]} /> :
                      <AlertCircle size={16} color={colors.textSecondary} />
                    }
                    <Text style={[styles.statusText, {
                      color: application.payment.status === 'verified' ? colors.success[500] :
                            application.payment.status === 'pending' ? colors.warning[500] :
                            colors.textSecondary
                    }]}>
                      {application.payment.status === 'verified' ? 'Verified' :
                       application.payment.status === 'pending' ? 'Pending' :
                       application.payment.status === 'failed' ? 'Failed' : 'Not Started'}
                    </Text>
                  </View>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Amount:</Text>
                  <Text style={styles.value}>{formatCurrency(application.payment.amount, application.payment.currency)}</Text>
                </View>
                {application.payment.dueDate && (
                  <View style={styles.row}>
                    <Text style={styles.label}>Due Date:</Text>
                    <Text style={styles.value}>{application.payment.dueDate}</Text>
                  </View>
                )}
                {application.payment.paidDate && (
                  <View style={styles.row}>
                    <Text style={styles.label}>Paid Date:</Text>
                    <Text style={styles.value}>{application.payment.paidDate}</Text>
                  </View>
                )}
              </ApplicationDetailSection>
            )}

            {application.notes && application.notes.length > 0 && (
              <ApplicationDetailSection
                title="Notes"
                icon={<FileText size={24} color={colors.primary[500]} />}
                initiallyExpanded={false}
              >
                {application.notes.map((note, index) => (
                  <Text key={index} style={styles.notes}>{note}</Text>
                ))}
              </ApplicationDetailSection>
            )}
          </>
        );

      case 'status':
        return (
          <>
            <ApplicationDetailSection
              title="Application Status"
              icon={<FileBarChart size={24} color={colors.primary[500]} />}
              initiallyExpanded={true}
            >
              <View style={[styles.row, { justifyContent: 'space-between', marginBottom: spacing.md }]}>
                <Text style={[styles.value, { fontSize: typography.sizes.lg }]}>Current Status</Text>
                <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
                  {statusInfo.icon}
                  <Text style={[styles.statusText, { color: statusInfo.color }]}>
                    {statusInfo.text}
                  </Text>
                </View>
              </View>

              <View style={styles.progressContainer}>
                <View style={styles.progressBackground}>
                  <View
                    style={[
                      styles.progressBar,
                      {
                        width: `${getStatusInfo(application.status).progressPercentage || 0}%`,
                        backgroundColor: statusInfo.color,
                      },
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {getStatusInfo(application.status).progressPercentage || 0}% Complete
                </Text>
              </View>
            </ApplicationDetailSection>

            <Text style={styles.sectionTitle}>Application Timeline</Text>
            <ApplicationStatusTimeline steps={timelineSteps} />

            <Text style={styles.sectionTitle}>Detailed Timeline</Text>
            <ApplicationTimelineVisualization
              steps={timelineSteps}
              onStepPress={(step) => {
                console.log('Step pressed:', step.id);
                // Handle step press if needed
              }}
            />
          </>
        );

      case 'documents':
        return (
          <>
            <ApplicationDetailSection
              title="Document Verification Status"
              icon={<FileCheck size={24} color={colors.primary[500]} />}
              initiallyExpanded={true}
              badge={application.documents.filter(d => d.status === 'pending').length > 0 ?
                { count: application.documents.filter(d => d.status === 'pending').length, color: colors.warning[500] } : undefined}
            >
              <View style={styles.documentStats}>
                <View style={styles.documentStatItem}>
                  <Text style={styles.documentStatValue}>
                    {application.documents.filter(d => d.status === 'verified').length}
                  </Text>
                  <Text style={styles.documentStatLabel}>Verified</Text>
                </View>
                <View style={styles.documentStatItem}>
                  <Text style={styles.documentStatValue}>
                    {application.documents.filter(d => d.status === 'pending').length}
                  </Text>
                  <Text style={styles.documentStatLabel}>Pending</Text>
                </View>
                <View style={styles.documentStatItem}>
                  <Text style={styles.documentStatValue}>
                    {application.documents.filter(d => d.status === 'rejected').length}
                  </Text>
                  <Text style={styles.documentStatLabel}>Rejected</Text>
                </View>
              </View>
            </ApplicationDetailSection>

            <ApplicationDetailSection
              title="Required Documents"
              icon={<FileText size={24} color={colors.primary[500]} />}
              initiallyExpanded={true}
            >
              {application.documents.map((document, index) => {
                const docStatusInfo = getDocumentStatusInfo(document.status);
                return (
                  <View key={document.id} style={[
                    styles.documentItem,
                    index === application.documents.length - 1 && { borderBottomWidth: 0 }
                  ]}>
                    <View>
                      <Text style={styles.documentName}>{document.name}</Text>
                      <Text style={styles.documentDate}>Uploaded: {document.date}</Text>
                      {document.verificationDate && (
                        <Text style={styles.documentDate}>Verified: {document.verificationDate}</Text>
                      )}
                      {document.rejectionReason && (
                        <Text style={[styles.documentDate, { color: colors.error[500] }]}>
                          Reason: {document.rejectionReason}
                        </Text>
                      )}
                    </View>
                    <View style={[styles.statusBadge, { backgroundColor: docStatusInfo.bgColor }]}>
                      {docStatusInfo.icon}
                      <Text style={[styles.statusText, { color: docStatusInfo.color }]}>
                        {docStatusInfo.text}
                      </Text>
                    </View>
                  </View>
                );
              })}
            </ApplicationDetailSection>
          </>
        );

      case 'payment':
        return (
          <>
            {application.payment ? (
              <PaymentProcessingSection
                application={application}
                onUpdatePaymentMethod={handleUpdatePaymentMethod}
              />
            ) : (
              <View style={[styles.card, { alignItems: 'center', padding: spacing.xl }]}>
                <DollarSign size={48} color={colors.textSecondary} />
                <Text style={[styles.value, { marginTop: spacing.md, textAlign: 'center' }]}>
                  No payment information available for this application yet.
                </Text>
              </View>
            )}
          </>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Application Details</Text>
      </View>

      <View style={styles.content}>
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          scrollable={true}
        />

        <ScrollView showsVerticalScrollIndicator={false} style={styles.tabContent}>
          {renderTabContent()}
        </ScrollView>
      </View>

      {/* Bottom Navigation Bar */}
      <BottomNavBar currentRoute="applications" />
    </SafeAreaView>
  );
}
