import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  Send,
  User,
  Headphones,
  Clock,
} from 'lucide-react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';
import {
  fetchChatSession,
  sendChatMessage,
  markMessageAsRead,
} from '@/store/chatSlice';
import { ChatMessagePublic, MessageParticipant } from '@/types/backend';
import { chatService } from '@/services/chatService';

export default function ChatConversationScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const dispatch = useAppDispatch();

  const { currentSession, isLoading, isSending } = useAppSelector((state) => state.chat);
  const [message, setMessage] = useState('');
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    if (id) {
      dispatch(fetchChatSession(id));
    }
  }, [id, dispatch]);

  useEffect(() => {
    // Mark agent messages as read when session loads
    if (currentSession) {
      currentSession.messages.forEach(msg => {
        if (!msg.is_read && msg.participant === MessageParticipant.AGENT) {
          dispatch(markMessageAsRead(msg.id));
        }
      });
    }
  }, [currentSession, dispatch]);

  const handleSendMessage = async () => {
    if (!message.trim() || !currentSession || isSending) return;

    const messageContent = message.trim();
    setMessage('');

    try {
      await dispatch(sendChatMessage({
        sessionId: currentSession.id,
        messageData: {
          content: messageContent,
          participant: MessageParticipant.USER,
        },
      }));

      // Scroll to bottom after sending
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessage(messageContent); // Restore message on error
    }
  };

  const renderMessage = ({ item, index }: { item: ChatMessagePublic; index: number }) => {
    const isUser = item.participant === MessageParticipant.USER;
    const isAgent = item.participant === MessageParticipant.AGENT;
    const messageTime = chatService.formatMessageTime(item.created_at);

    return (
      <Animated.View
        entering={FadeInRight.delay(index * 50).springify()}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.agentMessageContainer,
        ]}
      >
        <View style={styles.messageHeader}>
          <View style={styles.messageAvatar}>
            {isUser ? (
              <User size={16} color={colors.primary[500]} />
            ) : (
              <Headphones size={16} color={colors.secondary[500]} />
            )}
          </View>
          <Text style={[styles.messageSender, { color: colors.textSecondary }]}>
            {isUser ? 'You' : 'Support Agent'}
          </Text>
          <Text style={[styles.messageTime, { color: colors.textSecondary }]}>
            {messageTime}
          </Text>
        </View>
        <View
          style={[
            styles.messageBubble,
            {
              backgroundColor: isUser ? colors.primary[500] : colors.card,
              borderColor: colors.border,
            },
          ]}
        >
          <Text
            style={[
              styles.messageText,
              { color: isUser ? colors.white : colors.text },
            ]}
          >
            {item.content}
          </Text>
        </View>
        {!item.is_read && isAgent && (
          <View style={[styles.unreadIndicator, { backgroundColor: colors.primary[500] }]} />
        )}
      </Animated.View>
    );
  };

  const renderEmptyState = () => (
    <Animated.View 
      style={styles.emptyContainer}
      entering={FadeInDown.delay(200).springify()}
    >
      <Headphones size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        Start the conversation
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Send a message to begin chatting with our support team.
      </Text>
    </Animated.View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    backButton: {
      padding: spacing.sm,
      marginRight: spacing.md,
    },
    headerContent: {
      flex: 1,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    headerSubtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
    },
    messagesList: {
      flex: 1,
      paddingHorizontal: spacing.lg,
    },
    messageContainer: {
      marginVertical: spacing.sm,
    },
    userMessageContainer: {
      alignItems: 'flex-end',
    },
    agentMessageContainer: {
      alignItems: 'flex-start',
    },
    messageHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    messageAvatar: {
      width: 20,
      height: 20,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.xs,
    },
    messageSender: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginRight: spacing.sm,
    },
    messageTime: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
    },
    messageBubble: {
      maxWidth: '80%',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
    },
    messageText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      lineHeight: 20,
    },
    unreadIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginTop: spacing.xs,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      backgroundColor: colors.card,
    },
    textInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.lg,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      marginRight: spacing.md,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      backgroundColor: colors.background,
      maxHeight: 100,
    },
    sendButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      alignItems: 'center',
      justifyContent: 'center',
    },
    sendButtonDisabled: {
      opacity: 0.5,
    },
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: spacing.xl,
    },
    emptyTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    emptySubtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? 'light' : 'dark'} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>
            {currentSession?.title || 'Support Chat'}
          </Text>
          <Text style={styles.headerSubtitle}>
            Support Team • Online
          </Text>
        </View>
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <FlatList
          ref={flatListRef}
          data={currentSession?.messages || []}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={{ flexGrow: 1 }}
          ListEmptyComponent={renderEmptyState}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
        />

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={message}
            onChangeText={setMessage}
            placeholder="Type your message..."
            placeholderTextColor={colors.textSecondary}
            multiline
            maxLength={1000}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              {
                backgroundColor: message.trim() && !isSending ? colors.primary[500] : colors.border,
              },
              (!message.trim() || isSending) && styles.sendButtonDisabled,
            ]}
            onPress={handleSendMessage}
            disabled={!message.trim() || isSending}
          >
            {isSending ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Send size={20} color={colors.white} />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
