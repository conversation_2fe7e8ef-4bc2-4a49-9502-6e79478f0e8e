import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Document } from './types';
import DocumentItem from './DocumentItem';
import NoDocumentsState from './NoDocumentsState';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface DocumentListSectionProps {
  title: string;
  documents: Document[];
  onViewDocument?: (document: Document) => void;
  onDeleteDocument?: (document: Document) => void;
  onReuploadDocument?: (document: Document) => void;
  emptyStateMessage?: string;
  emptyStateIcon?: React.ReactNode;
}

const DocumentListSection: React.FC<DocumentListSectionProps> = ({
  title,
  documents,
  onViewDocument,
  onDeleteDocument,
  onReuploadDocument,
  emptyStateMessage,
  emptyStateIcon,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  console.log(`[DocumentListSection] Rendering section: ${title} with ${documents.length} documents`);

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.xl,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    listContainer: {
      minHeight: 100, // Minimum height to show empty state properly
    },
  });

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeInDown.delay(100).springify()}
    >
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.listContainer}>
        {documents.length > 0 ? (
          <FlatList
            data={documents}
            renderItem={({ item }) => (
              <DocumentItem
                document={item}
                onView={onViewDocument}
                onDelete={onDeleteDocument}
                onReupload={onReuploadDocument}
              />
            )}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            scrollEnabled={false} // Disable scrolling within this component
          />
        ) : (
          <NoDocumentsState 
            message={emptyStateMessage} 
            icon={emptyStateIcon} 
          />
        )}
      </View>
    </Animated.View>
  );
};

export default DocumentListSection;
