import { Platform } from 'react-native';

// Base colors from the image
const baseColors = {
  coral: '#E85D3F',
  deepPurple: '#1E1E3F', // Deep purple from the image
  purple: '#4A4A9C',
  lightPurple: '#8B5CF6',
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
};

// Color palettes
const palettes = {
  coral: {
    50: '#FFF5F2',
    100: '#FFE6E0',
    200: '#FFC7BA',
    300: '#FFA494',
    400: '#FF7A63',
    500: baseColors.coral, // Main primary color (coral/orange from logo)
    600: '#D43D22',
    700: '#B82D15',
    800: '#9C1F0A',
    900: '#801600',
  },
  purple: {
    50: '#F5F3FF',
    100: '#EDE9FE',
    200: '#DDD6FE',
    300: '#C4B5FD',
    400: '#A78BFA',
    500: baseColors.purple, // Main secondary color (purple/blue from logo)
    600: '#3F3F8C',
    700: '#33337D',
    800: '#28286E',
    900: baseColors.deepPurple, // Deep purple from the image
  },
  neutral: {
    50: '#F8FAFC',
    100: '#F1F5F9',
    200: '#E2E8F0',
    300: '#CBD5E1',
    400: '#94A3B8',
    500: '#64748B',
    600: '#475569',
    700: '#334155',
    800: '#1E293B',
    900: '#0F172A',
  },
  success: {
    50: '#ECFDF5',
    100: '#D1FAE5',
    200: '#A7F3D0',
    300: '#6EE7B7',
    400: '#34D399',
    500: '#10B981',
    600: '#059669',
    700: '#047857',
    800: '#065F46',
    900: '#064E3B',
  },
  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
  },
  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
  },
  info: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
  },
};

// Theme-specific colors
export const lightColors = {
  primary: palettes.coral,
  secondary: palettes.purple,
  background: baseColors.white,
  card: baseColors.white,
  text: palettes.neutral[900],
  textSecondary: palettes.neutral[600],
  border: palettes.neutral[200],
  notification: palettes.coral[500],
  success: palettes.success,
  warning: palettes.warning,
  error: palettes.error,
  info: palettes.info,
  neutral: palettes.neutral,
  ...baseColors,
};

export const darkColors = {
  primary: palettes.coral,
  secondary: palettes.purple,
  background: baseColors.deepPurple, // Deep purple background
  card: palettes.purple[800],
  text: baseColors.white,
  textSecondary: palettes.neutral[300],
  border: palettes.purple[700],
  notification: palettes.coral[500],
  success: palettes.success,
  warning: palettes.warning,
  error: palettes.error,
  info: palettes.info,
  neutral: palettes.neutral,
  ...baseColors,
};

// Typography
export const typography = {
  fonts: {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    bold: 'Inter-Bold',
  },
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
  // Text styles
  h1: {
    fontFamily: 'Inter-Bold',
    fontSize: 30,
    lineHeight: 1.2 * 30,
  },
  h2: {
    fontFamily: 'Inter-Bold',
    fontSize: 24,
    lineHeight: 1.2 * 24,
  },
  h3: {
    fontFamily: 'Inter-Bold',
    fontSize: 20,
    lineHeight: 1.2 * 20,
  },
  h4: {
    fontFamily: 'Inter-Bold',
    fontSize: 18,
    lineHeight: 1.2 * 18,
  },
  body: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    lineHeight: 1.5 * 16,
  },
  caption: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    lineHeight: 1.5 * 12,
  },
  button: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    lineHeight: 1.5 * 16,
  },
};

// Spacing
export const spacing = {
  none: 0,
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 40,
  '3xl': 48,
  '4xl': 56,
  '5xl': 64,
  '6xl': 72,
};

// Borders & Shadows
export const borders = {
  radius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
  width: {
    none: 0,
    thin: 1,
    thick: 2,
    thicker: 3,
  },
};

export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: Platform.select({
    ios: {
      shadowColor: baseColors.black,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    android: {
      elevation: 2,
    },
    default: {
      shadowColor: baseColors.black,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
  }),
  md: Platform.select({
    ios: {
      shadowColor: baseColors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
    },
    android: {
      elevation: 4,
    },
    default: {
      shadowColor: baseColors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
    },
  }),
  lg: Platform.select({
    ios: {
      shadowColor: baseColors.black,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
    },
    android: {
      elevation: 8,
    },
    default: {
      shadowColor: baseColors.black,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
    },
  }),
};

// Create a theme object based on the current mode
export const createTheme = (isDark: boolean) => {
  const colors = isDark ? darkColors : lightColors;

  // Update shadows based on the current theme
  const themeShadows = {
    ...shadows,
    sm: Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: isDark ? 0.3 : 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      default: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: isDark ? 0.3 : 0.1,
        shadowRadius: 2,
      },
    }),
    md: Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: isDark ? 0.4 : 0.15,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
      default: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: isDark ? 0.4 : 0.15,
        shadowRadius: 4,
      },
    }),
    lg: Platform.select({
      ios: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: isDark ? 0.5 : 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      default: {
        shadowColor: colors.black,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: isDark ? 0.5 : 0.2,
        shadowRadius: 8,
      },
    }),
  };

  return {
    colors,
    typography,
    spacing,
    borders,
    shadows: themeShadows,
  };
};

// Default export with light theme
export default createTheme(false);