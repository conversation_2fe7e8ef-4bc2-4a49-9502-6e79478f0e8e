import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import { checkApiHealth, checkApiHealthWithToast, getApiHealthStatus } from '../utils/apiHealthCheck';
import { showToast } from '../utils/toast';

// Mock the toast function
jest.mock('../utils/toast', () => ({
  showToast: jest.fn(),
}));

// Create a mock for axios
const mockAxios = new MockAdapter(axios);

describe('API Health Check Utilities', () => {
  // Reset mocks before each test
  beforeEach(() => {
    mockAxios.reset();
    jest.clearAllMocks();
  });

  describe('checkApiHealth', () => {
    test('returns true when API is healthy', async () => {
      // Mock the health check endpoint
      mockAxios.onGet('/api/v1/health').reply(200, { status: 'ok' });

      // Call the health check function
      const result = await checkApiHealth();

      // Verify the result
      expect(result).toBe(true);
    });

    test('returns false when API returns unexpected response', async () => {
      // Mock the health check endpoint with unexpected response
      mockAxios.onGet('/api/v1/health').reply(200, { status: 'error' });

      // Call the health check function
      const result = await checkApiHealth();

      // Verify the result
      expect(result).toBe(false);
    });

    test('returns false when API request fails', async () => {
      // Mock the health check endpoint to fail
      mockAxios.onGet('/api/v1/health').reply(500);

      // Call the health check function
      const result = await checkApiHealth();

      // Verify the result
      expect(result).toBe(false);
    });
  });

  describe('checkApiHealthWithToast', () => {
    test('shows success toast when API is healthy and showSuccessToast is true', async () => {
      // Mock the health check endpoint
      mockAxios.onGet('/api/v1/health').reply(200, { status: 'ok' });

      // Call the health check function
      const result = await checkApiHealthWithToast(true);

      // Verify the result
      expect(result).toBe(true);
      expect(showToast).toHaveBeenCalledWith(
        'success',
        'Connection Successful',
        'Connected to the Inerca backend server',
        expect.any(Object)
      );
    });

    test('does not show success toast when API is healthy but showSuccessToast is false', async () => {
      // Mock the health check endpoint
      mockAxios.onGet('/api/v1/health').reply(200, { status: 'ok' });

      // Call the health check function
      const result = await checkApiHealthWithToast(false);

      // Verify the result
      expect(result).toBe(true);
      expect(showToast).not.toHaveBeenCalled();
    });

    test('shows error toast when API is not healthy', async () => {
      // Mock the health check endpoint to fail
      mockAxios.onGet('/api/v1/health').reply(500);

      // Call the health check function
      const result = await checkApiHealthWithToast(true);

      // Verify the result
      expect(result).toBe(false);
      expect(showToast).toHaveBeenCalledWith(
        'error',
        'Connection Failed',
        'Could not connect to the Inerca backend server. Some features may not work properly.',
        expect.any(Object)
      );
    });
  });

  describe('getApiHealthStatus', () => {
    test('returns detailed status when API is healthy', async () => {
      // Mock the health check endpoint
      mockAxios.onGet('/api/v1/health').reply(200, { status: 'ok', version: '1.0.0' });

      // Call the health check function
      const result = await getApiHealthStatus();

      // Verify the result
      expect(result).toEqual({
        isHealthy: true,
        message: 'API is healthy',
        details: { status: 'ok', version: '1.0.0' }
      });
    });

    test('returns detailed status when API returns unexpected response', async () => {
      // Mock the health check endpoint with unexpected response
      mockAxios.onGet('/api/v1/health').reply(200, { status: 'error' });

      // Call the health check function
      const result = await getApiHealthStatus();

      // Verify the result
      expect(result).toEqual({
        isHealthy: false,
        message: 'API returned unexpected response',
        details: { status: 'error' }
      });
    });

    test('returns detailed status when API request fails', async () => {
      // Mock the health check endpoint to fail
      mockAxios.onGet('/api/v1/health').reply(500, { message: 'Internal Server Error' });

      // Call the health check function
      const result = await getApiHealthStatus();

      // Verify the result
      expect(result.isHealthy).toBe(false);
      expect(result.message).toContain('Error');
      expect(result.details).toBeDefined();
    });
  });
});
