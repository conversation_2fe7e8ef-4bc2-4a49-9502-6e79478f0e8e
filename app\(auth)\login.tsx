import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { login, authenticateWithBiometric, loginWithGoogle, UserType } from '@/store/authSlice';
import { Mail, Lock, Fingerprint, ArrowLeft, User, Building, Eye, EyeOff, AlertCircle } from 'lucide-react-native';
import * as LocalAuthentication from 'expo-local-authentication';
import { Image } from 'react-native';

export default function LoginScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  // Get auth state and actions from Redux store
  const dispatch = useAppDispatch();
  const {
    isLoading,
    isBiometricEnabled,
    isBiometricAvailable,
    error
  } = useAppSelector(state => state.auth);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [userType, setUserType] = useState<UserType>('individual');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showBiometricOption, setShowBiometricOption] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState('');

  // Check if biometric authentication is available
  useEffect(() => {
    const checkBiometric = async () => {
      setShowBiometricOption(isBiometricAvailable && isBiometricEnabled);
    };

    checkBiometric();
  }, [isBiometricAvailable, isBiometricEnabled]);

  // Validate email
  const validateEmail = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  // Validate password
  const validatePassword = () => {
    if (!password) {
      setPasswordError('Password is required');
      return false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return false;
    }
    setPasswordError('');
    return true;
  };

  // Clear all errors
  const clearErrors = () => {
    setEmailError('');
    setPasswordError('');
    setLoginError('');
  };

  // Handle login
  const handleLogin = async () => {
    console.log('Login button pressed');
    clearErrors();

    // Validate inputs
    const isEmailValid = validateEmail();
    const isPasswordValid = validatePassword();

    if (!isEmailValid || !isPasswordValid) {
      console.log('Validation failed');
      return;
    }

    try {
      const resultAction = await dispatch(login({
        email,
        password,
        rememberMe,
        userType
      }));

      if (login.fulfilled.match(resultAction)) {
        console.log('Login successful, navigating to app');
        router.replace('/(app)/(tabs)');
      } else if (login.rejected.match(resultAction)) {
        const errorMessage = resultAction.payload as string || 'Login failed';
        console.log('Login failed:', errorMessage);

        // Provide specific error messages based on the error
        if (errorMessage.toLowerCase().includes('email')) {
          setEmailError('Email address not found. Please check your email or sign up.');
        } else if (errorMessage.toLowerCase().includes('password')) {
          setPasswordError('Incorrect password. Please try again.');
        } else if (errorMessage.toLowerCase().includes('invalid credentials') ||
                   errorMessage.toLowerCase().includes('unauthorized')) {
          setLoginError('Invalid email or password. Please check your credentials and try again.');
        } else if (errorMessage.toLowerCase().includes('network') ||
                   errorMessage.toLowerCase().includes('connection')) {
          setLoginError('Network error. Please check your internet connection and try again.');
        } else if (errorMessage.toLowerCase().includes('account')) {
          setLoginError('Account issue detected. Please contact support if this persists.');
        } else {
          setLoginError(errorMessage);
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginError('An unexpected error occurred. Please try again.');
    }
  };

  // Handle biometric authentication
  const handleBiometricAuth = async () => {
    console.log('Biometric authentication requested');

    try {
      // First check if biometric hardware is available
      const compatible = await LocalAuthentication.hasHardwareAsync();
      if (!compatible) {
        Alert.alert('Incompatible Device', 'Your device doesn\'t support biometric authentication.');
        return;
      }

      // Check if biometric is enrolled
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      if (!enrolled) {
        Alert.alert(
          'Biometric Not Set Up',
          'You haven\'t set up biometric authentication on your device. Please set it up in your device settings.',
          [
            { text: 'OK' }
          ]
        );
        return;
      }

      // Dispatch the authentication action
      const resultAction = await dispatch(authenticateWithBiometric());

      if (authenticateWithBiometric.fulfilled.match(resultAction)) {
        console.log('Biometric authentication successful, navigating to app');
        router.replace('/(app)/(tabs)');
      } else if (authenticateWithBiometric.rejected.match(resultAction)) {
        console.log('Biometric authentication failed');
        if (resultAction.payload) {
          // Only show alert if it's not a user cancellation
          if (resultAction.payload !== 'Biometric authentication failed or cancelled') {
            Alert.alert('Authentication Failed', resultAction.payload as string);
          }
        }
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      Alert.alert('Authentication Error', 'An unexpected error occurred during biometric authentication.');
    }
  };

  // Handle Google Sign-in
  const handleGoogleSignIn = async () => {
    console.log('Google Sign-in requested');
    clearErrors();

    try {
      console.log('Dispatching loginWithGoogle action...');
      const resultAction = await dispatch(loginWithGoogle());

      if (loginWithGoogle.fulfilled.match(resultAction)) {
        console.log('Google Sign-in successful, navigating to app');
        router.replace('/(app)/(tabs)');
      } else if (loginWithGoogle.rejected.match(resultAction)) {
        const errorMessage = resultAction.payload as string || 'Google Sign-in failed';
        console.log('Google Sign-in failed:', errorMessage);

        // Provide specific error messages for Google Sign-in
        if (errorMessage.toLowerCase().includes('cancelled') ||
            errorMessage.toLowerCase().includes('user cancelled')) {
          // Don't show error for user cancellation
          console.log('Google Sign-in was cancelled by user');
          return;
        } else if (errorMessage.toLowerCase().includes('network') ||
                   errorMessage.toLowerCase().includes('connection')) {
          setLoginError('Network error. Please check your internet connection and try again.');
        } else if (errorMessage.toLowerCase().includes('google') ||
                   errorMessage.toLowerCase().includes('authentication url')) {
          setLoginError('Google Sign-in is temporarily unavailable. Please try signing in with email and password.');
        } else {
          setLoginError(`Google Sign-in failed: ${errorMessage}`);
        }
      }
    } catch (error) {
      console.error('Google Sign-in error:', error);
      setLoginError('Google Sign-in is currently unavailable. Please try signing in with email and password.');
    }
  };



  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContent: {
      flexGrow: 1,
      justifyContent: 'center',
    },
    contentContainer: {
      flex: 1,
      padding: spacing.lg,
      justifyContent: 'center',
    },
    header: {
      marginBottom: spacing.xl,
    },
    backButton: {
      marginBottom: spacing.md,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes['3xl'],
      color: colors.text,
      marginBottom: spacing.xs,
    },
    subtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      marginBottom: spacing.lg,
    },
    userTypeContainer: {
      marginBottom: spacing.lg,
    },
    userTypeButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    userTypeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      width: '48%',
    },
    userTypeButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    userTypeButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.primary[500],
      marginLeft: spacing.sm,
    },
    userTypeButtonTextActive: {
      color: colors.white,
    },
    inputContainer: {
      marginBottom: spacing.md,
    },
    inputLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      backgroundColor: colors.card,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      minHeight: 50,
    },
    input: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
      textAlignVertical: 'top',
    },
    errorText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.error[500],
      marginTop: spacing.xs,
    },
    forgotPassword: {
      alignSelf: 'flex-end',
      marginBottom: spacing.lg,
    },
    forgotPasswordText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
    },
    rememberMeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.lg,
    },
    rememberMeText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    loginButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    loginButtonText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
    biometricButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      marginBottom: spacing.lg,
    },
    biometricButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
    },
    divider: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: spacing.lg,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: colors.border,
    },
    dividerText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginHorizontal: spacing.md,
    },
    googleButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      marginBottom: spacing.lg,
    },
    googleButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
    },
    registerContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: spacing.lg,
    },
    registerText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    registerLink: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    legalLinksContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: spacing.md,
    },
    legalLink: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
    },
    legalSeparator: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginHorizontal: spacing.xs,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.contentContainer}>
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color={colors.text} />
              </TouchableOpacity>
              <Text style={styles.title}>Welcome back</Text>
              <Text style={styles.subtitle}>Sign in to your account</Text>
            </View>

            {/* User Type Selection */}
            <View style={styles.userTypeContainer}>
              <View style={styles.userTypeButtons}>
                <TouchableOpacity
                  style={[
                    styles.userTypeButton,
                    userType === 'individual' && styles.userTypeButtonActive
                  ]}
                  onPress={() => setUserType('individual')}
                >
                  <User size={20} color={userType === 'individual' ? colors.white : colors.primary[500]} />
                  <Text style={[
                    styles.userTypeButtonText,
                    userType === 'individual' && styles.userTypeButtonTextActive
                  ]}>Individual</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.userTypeButton,
                    userType === 'business' && styles.userTypeButtonActive
                  ]}
                  onPress={() => setUserType('business')}
                >
                  <Building size={20} color={userType === 'business' ? colors.white : colors.primary[500]} />
                  <Text style={[
                    styles.userTypeButtonText,
                    userType === 'business' && styles.userTypeButtonTextActive
                  ]}>Business</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <View style={[
                styles.inputWrapper,
                emailError ? { borderColor: colors.error[500] } : {}
              ]}>
                <Mail size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your email"
                  placeholderTextColor={colors.textSecondary}
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (emailError) setEmailError('');
                    if (loginError) setLoginError('');
                  }}
                  onBlur={validateEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
              {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Password</Text>
              <View style={[
                styles.inputWrapper,
                passwordError ? { borderColor: colors.error[500] } : {}
              ]}>
                <Lock size={20} color={colors.textSecondary} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your password"
                  placeholderTextColor={colors.textSecondary}
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (passwordError) setPasswordError('');
                  }}
                  onBlur={validatePassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeButton}
                >
                  {showPassword ? (
                    <EyeOff size={20} color={colors.textSecondary} />
                  ) : (
                    <Eye size={20} color={colors.textSecondary} />
                  )}
                </TouchableOpacity>
              </View>
              {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
            </View>

            <TouchableOpacity
              style={styles.forgotPassword}
              onPress={() => router.push('/(auth)/forgot-password')}
            >
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            <View style={styles.rememberMeContainer}>
              <Text style={styles.rememberMeText}>Remember Me</Text>
              <Switch
                value={rememberMe}
                onValueChange={setRememberMe}
                trackColor={{ false: colors.border, true: colors.primary[500] }}
                thumbColor={colors.white}
              />
            </View>

            <TouchableOpacity
              style={styles.loginButton}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <Text style={styles.loginButtonText}>Sign In</Text>
              )}
            </TouchableOpacity>

            {showBiometricOption && (
              <TouchableOpacity
                style={styles.biometricButton}
                onPress={handleBiometricAuth}
              >
                <Fingerprint size={20} color={colors.primary[500]} />
                <Text style={styles.biometricButtonText}>Sign in with Biometrics</Text>
              </TouchableOpacity>
            )}

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.dividerLine} />
            </View>

            <TouchableOpacity
              style={styles.googleButton}
              onPress={handleGoogleSignIn}
            >
              <Image
                source={require('@/assets/images/google-logo.png')}
                style={{ width: 20, height: 20 }}
                resizeMode="contain"
              />
              <Text style={styles.googleButtonText}>Continue with Google</Text>
            </TouchableOpacity>

            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>Don't have an account?</Text>
              <TouchableOpacity onPress={() => router.push('/(auth)/register')}>
                <Text style={styles.registerLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.legalLinksContainer}>
              <TouchableOpacity onPress={() => router.push('/legal/terms')}>
                <Text style={styles.legalLink}>Terms & Conditions</Text>
              </TouchableOpacity>
              <Text style={styles.legalSeparator}>•</Text>
              <TouchableOpacity onPress={() => router.push('/legal/privacy')}>
                <Text style={styles.legalLink}>Privacy Policy</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
