import React, { useState, memo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { X, ChevronDown, FileText, Home, Car, User, CreditCard, FileCheck } from 'lucide-react-native';
import { DocumentCategory } from './types';

interface DocumentTypeSelectorProps {
  onSelect: (documentType: string, documentCategory: DocumentCategory) => void;
}

// Document type options with icons
const documentTypes = [
  // General/KYC Documents
  {
    id: '1',
    name: 'National ID/Omang/Passport',
    category: 'ID' as DocumentCategory,
    icon: (color: string) => <User size={20} color={color} />
  },
  {
    id: '2',
    name: 'Proof of Address',
    category: 'Proof of Address' as DocumentCategory,
    icon: (color: string) => <Home size={20} color={color} />
  },
  {
    id: '3',
    name: 'Proof of Income',
    category: 'Financial' as DocumentCategory,
    icon: (color: string) => <CreditCard size={20} color={color} />
  },
  {
    id: '4',
    name: 'Bank Confirmation Letter',
    category: 'Financial' as DocumentCategory,
    icon: (color: string) => <CreditCard size={20} color={color} />
  },
  // Payment Documents
  {
    id: '5',
    name: 'Payment Proof - Premium',
    category: 'Payment' as DocumentCategory,
    icon: (color: string) => <CreditCard size={20} color={color} />
  },
  {
    id: '6',
    name: 'Payment Proof - Renewal',
    category: 'Payment' as DocumentCategory,
    icon: (color: string) => <CreditCard size={20} color={color} />
  },
  {
    id: '7',
    name: 'Payment Proof - Claim Deductible',
    category: 'Payment' as DocumentCategory,
    icon: (color: string) => <CreditCard size={20} color={color} />
  },
  {
    id: '8',
    name: 'KYC Compliance Acknowledgment',
    category: 'ID' as DocumentCategory,
    icon: (color: string) => <FileCheck size={20} color={color} />
  },

  // Motor Insurance Documents
  {
    id: '9',
    name: 'Driver\'s License',
    category: 'ID' as DocumentCategory,
    icon: (color: string) => <User size={20} color={color} />
  },
  {
    id: '10',
    name: 'Vehicle Registration Book',
    category: 'ID' as DocumentCategory,
    icon: (color: string) => <Car size={20} color={color} />
  },
  {
    id: '11',
    name: 'Vehicle Valuation Report',
    category: 'Insurance' as DocumentCategory,
    icon: (color: string) => <Car size={20} color={color} />
  },
  {
    id: '12',
    name: 'Claim-Free Group Letter',
    category: 'Insurance' as DocumentCategory,
    icon: (color: string) => <FileCheck size={20} color={color} />
  },

  // Houseowners Insurance Documents
  {
    id: '13',
    name: 'Title Deed/Land Board Certificate',
    category: 'ID' as DocumentCategory,
    icon: (color: string) => <Home size={20} color={color} />
  },
  {
    id: '14',
    name: 'Building Valuation Report',
    category: 'Insurance' as DocumentCategory,
    icon: (color: string) => <Home size={20} color={color} />
  },

  // Household Contents Insurance Documents
  {
    id: '15',
    name: 'Completed Inventory Form',
    category: 'Insurance' as DocumentCategory,
    icon: (color: string) => <FileText size={20} color={color} />
  },

  // Life Assurance Documents
  {
    id: '16',
    name: 'Beneficiary Nomination Form',
    category: 'Insurance' as DocumentCategory,
    icon: (color: string) => <User size={20} color={color} />
  },

  // Other
  {
    id: '17',
    name: 'Insurance Certificate',
    category: 'Insurance' as DocumentCategory,
    icon: (color: string) => <FileCheck size={20} color={color} />
  },
  {
    id: '18',
    name: 'Other Document',
    category: 'Other' as DocumentCategory,
    icon: (color: string) => <FileText size={20} color={color} />
  },
];

const DocumentTypeSelector: React.FC<DocumentTypeSelectorProps> = memo(({
  onSelect,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedType, setSelectedType] = useState<string | null>(null);

  // Use useCallback to memoize the handleSelect function
  const handleSelect = useCallback((name: string, category: DocumentCategory) => {
    setSelectedType(name);
    setModalVisible(false);
    onSelect(name, category);
  }, [onSelect]);

  // Memoize styles to prevent recreation on every render
  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      marginBottom: spacing.md,
    },
    selectorButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    selectorText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: selectedType ? colors.text : colors.textSecondary,
      flex: 1,
      marginRight: spacing.md,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    closeButton: {
      padding: spacing.xs,
    },
    typeItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    typeIcon: {
      marginRight: spacing.md,
      width: 24,
      alignItems: 'center',
    },
    typeName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      flex: 1,
    },
  }), [spacing, colors, borders, typography, selectedType]);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selectorButton}
        onPress={() => setModalVisible(true)}
      >
        <Text
          style={styles.selectorText}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {selectedType || 'Select Document Type'}
        </Text>
        <ChevronDown size={20} color={colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Document Type</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}
            >
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={documentTypes}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.typeItem}
                onPress={() => handleSelect(item.name, item.category)}
              >
                <View style={styles.typeIcon}>
                  {item.icon(colors.primary[500])}
                </View>
                <Text
                  style={styles.typeName}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {item.name}
                </Text>
              </TouchableOpacity>
            )}
          />
        </SafeAreaView>
      </Modal>
    </View>
  );

});

export default DocumentTypeSelector;
