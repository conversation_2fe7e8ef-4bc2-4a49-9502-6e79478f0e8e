import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { ClaimStatus, CLAIM_STATUS_DISPLAY_NAMES } from '@/types/claim.types';
import {
  Clock,
  FileText,
  Search,
  AlertCircle,
  CheckCircle,
  DollarSign,
  XCircle,
  HelpCircle,
  FileQuestion
} from 'lucide-react-native';

interface ClaimStatusBadgeProps {
  status: ClaimStatus;
  size?: 'small' | 'medium' | 'large';
}

const ClaimStatusBadge: React.FC<ClaimStatusBadgeProps> = ({ 
  status, 
  size = 'medium' 
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Get status color and icon
  const getStatusInfo = (status: ClaimStatus) => {
    switch (status) {
      case 'draft':
        return {
          color: colors.neutral[500],
          backgroundColor: `${colors.neutral[500]}20`,
          icon: <FileText size={getIconSize()} color={colors.neutral[500]} />
        };
      case 'submitted':
        return {
          color: colors.info[500],
          backgroundColor: `${colors.info[500]}20`,
          icon: <FileText size={getIconSize()} color={colors.info[500]} />
        };
      case 'under_review':
        return {
          color: colors.primary[500],
          backgroundColor: `${colors.primary[500]}20`,
          icon: <Search size={getIconSize()} color={colors.primary[500]} />
        };
      case 'additional_info':
        return {
          color: colors.warning[500],
          backgroundColor: `${colors.warning[500]}20`,
          icon: <FileQuestion size={getIconSize()} color={colors.warning[500]} />
        };
      case 'approved':
        return {
          color: colors.success[500],
          backgroundColor: `${colors.success[500]}20`,
          icon: <CheckCircle size={getIconSize()} color={colors.success[500]} />
        };
      case 'partially_approved':
        return {
          color: colors.info[500],
          backgroundColor: `${colors.info[500]}20`,
          icon: <HelpCircle size={getIconSize()} color={colors.info[500]} />
        };
      case 'rejected':
        return {
          color: colors.error[500],
          backgroundColor: `${colors.error[500]}20`,
          icon: <XCircle size={getIconSize()} color={colors.error[500]} />
        };
      case 'paid':
        return {
          color: colors.success[500],
          backgroundColor: `${colors.success[500]}20`,
          icon: <DollarSign size={getIconSize()} color={colors.success[500]} />
        };
      case 'closed':
        return {
          color: colors.neutral[500],
          backgroundColor: `${colors.neutral[500]}20`,
          icon: <CheckCircle size={getIconSize()} color={colors.neutral[500]} />
        };
      default:
        return {
          color: colors.textSecondary,
          backgroundColor: colors.neutral[200],
          icon: <Clock size={getIconSize()} color={colors.textSecondary} />
        };
    }
  };

  // Get icon size based on badge size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 12;
      case 'large':
        return 20;
      default:
        return 16;
    }
  };

  // Get text size based on badge size
  const getTextSize = () => {
    switch (size) {
      case 'small':
        return typography.sizes.xs;
      case 'large':
        return typography.sizes.md;
      default:
        return typography.sizes.sm;
    }
  };

  // Get padding based on badge size
  const getPadding = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: spacing.xs,
          paddingVertical: spacing.xs / 2
        };
      case 'large':
        return {
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.sm
        };
      default:
        return {
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.xs
        };
    }
  };

  const statusInfo = getStatusInfo(status);
  const displayName = CLAIM_STATUS_DISPLAY_NAMES[status] || status.replace(/_/g, ' ');

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: 100,
      backgroundColor: statusInfo.backgroundColor,
      ...getPadding()
    },
    text: {
      fontFamily: typography.fonts.medium,
      fontSize: getTextSize(),
      color: statusInfo.color,
      marginLeft: spacing.xs
    }
  });

  return (
    <View style={styles.container}>
      {statusInfo.icon}
      <Text style={styles.text}>{displayName}</Text>
    </View>
  );
};

export default ClaimStatusBadge;
