import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '../context/ThemeContext';
import { DocumentUploadProvider } from '../context/DocumentUploadContext';
import VerificationScreen from '../app/(app)/verification/index';
import authReducer from '../store/authSlice';

// Mock the router
jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
    back: jest.fn(),
  },
  useLocalSearchParams: jest.fn().mockReturnValue({}),
}));

// Mock the toast
jest.mock('../utils/toast', () => ({
  showToast: jest.fn(),
}));

// Create a mock store
const createMockStore = (userType = 'individual') => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'test-user',
          userType,
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
        },
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    },
  });
};

// Wrapper component for tests
const TestWrapper = ({ children, userType = 'individual' }) => {
  const store = createMockStore(userType);
  
  return (
    <Provider store={store}>
      <ThemeProvider>
        <DocumentUploadProvider>
          {children}
        </DocumentUploadProvider>
      </ThemeProvider>
    </Provider>
  );
};

describe('VerificationScreen', () => {
  test('renders correctly for individual user', () => {
    const { getByText, queryByText } = render(
      <TestWrapper userType="individual">
        <VerificationScreen />
      </TestWrapper>
    );
    
    // Check that the screen title is rendered
    expect(getByText('Document Verification')).toBeTruthy();
    
    // Check that individual-specific documents are rendered
    expect(getByText('Proof of Identity')).toBeTruthy();
    expect(getByText('Proof of Address')).toBeTruthy();
    expect(getByText('Proof of Income')).toBeTruthy();
    
    // Check that business-specific documents are not rendered
    expect(queryByText('Business Registration')).toBeNull();
    expect(queryByText('Tax Clearance')).toBeNull();
    expect(queryByText('Business License')).toBeNull();
  });
  
  test('renders correctly for business user', () => {
    const { getByText, queryByText } = render(
      <TestWrapper userType="business">
        <VerificationScreen />
      </TestWrapper>
    );
    
    // Check that the screen title is rendered
    expect(getByText('Document Verification')).toBeTruthy();
    
    // Check that common documents are rendered
    expect(getByText('Proof of Identity')).toBeTruthy();
    expect(getByText('Proof of Address')).toBeTruthy();
    
    // Check that business-specific documents are rendered
    expect(getByText('Business Registration')).toBeTruthy();
    expect(getByText('Tax Clearance')).toBeTruthy();
    expect(getByText('Business License')).toBeTruthy();
    
    // Check that individual-specific documents are not rendered
    expect(queryByText('Proof of Income')).toBeNull();
  });
  
  test('shows error when submitting without required documents', async () => {
    const { getByText } = render(
      <TestWrapper>
        <VerificationScreen />
      </TestWrapper>
    );
    
    // Find and press the submit button
    const submitButton = getByText('Submit for Verification');
    fireEvent.press(submitButton);
    
    // Wait for the error message
    await waitFor(() => {
      expect(require('../utils/toast').showToast).toHaveBeenCalledWith(
        'error',
        'Missing Documents',
        'Please upload all required documents before submitting',
        expect.any(Object)
      );
    });
  });
});
