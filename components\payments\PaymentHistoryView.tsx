import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  TextInput
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  Search,
  Filter,
  SortDesc,
  SortAsc,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  RefreshCw,
  FileText,
  ChevronRight,
  DollarSign
} from 'lucide-react-native';
import { formatCurrency } from '@/utils/quoteCalculations';
import { format, parseISO } from 'date-fns';
import Animated, {
  FadeInDown,
  FadeIn
} from 'react-native-reanimated';
import { router } from 'expo-router';
import { PaymentMethod } from '@/store/applicationStore';
import { PaymentProof } from '@/store/paymentStore';
import PaymentFilterModal from './PaymentFilterModal';
import { PaymentFilters, PaymentHistoryStatus } from '@/types/payment.types';
import { useDocumentUpload } from '@/context/DocumentUploadContext';
import { Document as DocumentType } from '@/components/documents/types';

// Define payment status type
type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'cancelled';

// Define payment type
interface Payment {
  id: string;
  reference: string;
  amount: number;
  currency: string;
  date: string;
  dueDate?: string;
  status: PaymentStatus;
  method: PaymentMethod;
  description: string;
  policyId?: string;
  policyNumber?: string;
  claimId?: string;
  claimReference?: string;
  applicationId?: string;
  applicationReference?: string;
  receiptUrl?: string;
  proofOfPaymentUrl?: string;
  verificationDate?: string;
}

// Define payment filters
interface PaymentFilters {
  status?: PaymentStatus[];
  method?: PaymentMethod[];
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  searchQuery?: string;
}

interface PaymentHistoryViewProps {
  payments: Payment[];
  paymentProofs?: PaymentProof[];
  isLoading: boolean;
  onFilterChange?: (filters: PaymentFilters) => void;
  onViewPayment?: (paymentId: string) => void;
  onViewReceipt?: (receiptUrl: string) => void;
  onUploadProof?: (paymentId: string) => void;
}

const PaymentHistoryView: React.FC<PaymentHistoryViewProps> = ({
  payments,
  paymentProofs,
  isLoading,
  onFilterChange,
  onViewPayment,
  onViewReceipt,
  onUploadProof
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // State
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showFilterModal, setShowFilterModal] = useState<boolean>(false);
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [sortBy, setSortBy] = useState<'date' | 'amount'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>(payments);

  // Update filtered payments when payments or filters change
  useEffect(() => {
    let result = [...payments];

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(payment =>
        payment.reference.toLowerCase().includes(query) ||
        payment.description.toLowerCase().includes(query) ||
        (payment.policyNumber && payment.policyNumber.toLowerCase().includes(query)) ||
        (payment.claimReference && payment.claimReference.toLowerCase().includes(query)) ||
        (payment.applicationReference && payment.applicationReference.toLowerCase().includes(query))
      );
    }

    // Apply status filters
    if (filters.status && filters.status.length > 0) {
      result = result.filter(payment => filters.status?.includes(payment.status as any));
    }

    // Apply method filters
    if (filters.method && filters.method.length > 0) {
      result = result.filter(payment => filters.method?.includes(payment.method));
    }

    // Type and category filters removed as they're not in the Payment interface

    // Apply date range filters
    if (filters.dateRange) {
      if (filters.dateRange.start) {
        result = result.filter(payment => {
          const paymentDate = new Date(payment.date);
          return paymentDate >= filters.dateRange!.start!;
        });
      }

      if (filters.dateRange.end) {
        result = result.filter(payment => {
          const paymentDate = new Date(payment.date);
          return paymentDate <= filters.dateRange!.end!;
        });
      }
    }

    // Apply sorting
    result.sort((a, b) => {
      if (sortBy === 'date') {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      } else {
        return sortOrder === 'asc'
          ? a.amount - b.amount
          : b.amount - a.amount;
      }
    });

    setFilteredPayments(result);

    // Notify parent component of filter changes
    if (onFilterChange) {
      onFilterChange(filters);
    }
  }, [payments, searchQuery, filters, sortBy, sortOrder]);

  // Get status color
  const getStatusColor = (status: PaymentStatus): string => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'processing':
        return colors.info[500];
      case 'completed':
        return colors.success[500];
      case 'failed':
        return colors.error[500];
      case 'refunded':
        return colors.warning[700];
      case 'cancelled':
        return colors.textSecondary;
      default:
        return colors.textSecondary;
    }
  };

  // Get status icon
  const getStatusIcon = (status: PaymentStatus) => {
    const color = getStatusColor(status);

    switch (status) {
      case 'pending':
        return <Clock size={20} color={color} />;
      case 'processing':
        return <RefreshCw size={20} color={color} />;
      case 'completed':
        return <CheckCircle size={20} color={color} />;
      case 'failed':
        return <XCircle size={20} color={color} />;
      case 'refunded':
        return <RefreshCw size={20} color={color} />;
      case 'cancelled':
        return <XCircle size={20} color={color} />;
      default:
        return <Clock size={20} color={color} />;
    }
  };

  // Get status display name
  const getStatusDisplayName = (status: PaymentStatus): string => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown Status';
    }
  };

  // Format date
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), 'dd MMM yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Toggle sort order
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  // Toggle sort field
  const toggleSortField = () => {
    setSortBy(sortBy === 'date' ? 'amount' : 'date');
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  // These filter toggle functions are now handled by the PaymentFilterModal component

  // Import document upload context
  const { pendingDocuments, verifiedDocuments } = useDocumentUpload();

  // Check if payment has proof
  const hasProofOfPayment = (paymentId: string): boolean => {
    // First check payment proofs from payment store
    if (paymentProofs && paymentProofs.some(proof => proof.reference === paymentId)) {
      return true;
    }

    // Then check documents from document upload context
    const allDocuments = [...pendingDocuments, ...verifiedDocuments];
    return allDocuments.some((doc: any) =>
      doc.type === 'Payment' &&
      doc.metadata &&
      doc.metadata.paymentId === paymentId
    );
  };

  // Render payment item
  const renderPaymentItem = ({ item, index }: { item: Payment; index: number }) => (
    <Animated.View
      entering={FadeInDown.delay(index * 100).duration(300)}
    >
      <TouchableOpacity
        style={styles.paymentItem}
        onPress={() => onViewPayment && onViewPayment(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.paymentHeader}>
          <View style={styles.paymentTypeContainer}>
            <Text style={styles.paymentType}>
              {item.method === 'eft' ? 'EFT Payment' : 'Direct Debit'}
            </Text>
            <Text style={styles.paymentReference}>
              Ref: {item.reference}
            </Text>
          </View>
          <View style={styles.paymentAmountContainer}>
            <Text style={styles.paymentAmount}>
              {formatCurrency(item.amount, item.currency)}
            </Text>
          </View>
        </View>

        <View style={styles.paymentDetails}>
          <Text style={styles.paymentDescription} numberOfLines={1}>
            {item.description}
          </Text>

          <View style={styles.paymentDetail}>
            <Text style={styles.paymentDetailLabel}>Date:</Text>
            <Text style={styles.paymentDetailValue}>{formatDate(item.date)}</Text>
          </View>

          {item.dueDate && (
            <View style={styles.paymentDetail}>
              <Text style={styles.paymentDetailLabel}>Due Date:</Text>
              <Text style={styles.paymentDetailValue}>{formatDate(item.dueDate)}</Text>
            </View>
          )}

          {(item.policyNumber || item.claimReference || item.applicationReference) && (
            <View style={styles.paymentDetail}>
              <Text style={styles.paymentDetailLabel}>Related to:</Text>
              <Text style={styles.paymentDetailValue}>
                {item.policyNumber ? `Policy ${item.policyNumber}` :
                 item.claimReference ? `Claim ${item.claimReference}` :
                 `Application ${item.applicationReference}`}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.paymentFooter}>
          <View style={styles.statusContainer}>
            {getStatusIcon(item.status)}
            <Text
              style={[
                styles.statusText,
                { color: getStatusColor(item.status) }
              ]}
            >
              {getStatusDisplayName(item.status)}
            </Text>
          </View>

          <View style={styles.paymentActions}>
            {item.status === 'pending' && !hasProofOfPayment(item.id) && onUploadProof && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onUploadProof(item.id);
                }}
              >
                <Text style={styles.actionButtonText}>Upload Proof</Text>
              </TouchableOpacity>
            )}

            {item.receiptUrl && onViewReceipt && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  onViewReceipt(item.receiptUrl!);
                }}
              >
                <FileText size={16} color={colors.primary[500]} />
              </TouchableOpacity>
            )}

            <ChevronRight size={20} color={colors.textSecondary} />
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <DollarSign size={48} color={colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Payments Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery || Object.keys(filters).length > 0
          ? 'Try adjusting your filters or search query'
          : 'You haven\'t made any payments yet'}
      </Text>
      {(searchQuery || Object.keys(filters).length > 0) && (
        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetFilters}
        >
          <Text style={styles.resetButtonText}>Reset Filters</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  // Handle filter apply
  const handleApplyFilters = (newFilters: PaymentFilters) => {
    setFilters(newFilters);

    // Update search query if it's in the filters
    if (newFilters.searchQuery) {
      setSearchQuery(newFilters.searchQuery);
    }

    // Notify parent component of filter changes
    if (onFilterChange) {
      onFilterChange(newFilters);
    }
  };

  return (
    <View style={styles.container}>
      {/* Search and Filter Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search payments..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text);
              setFilters(prev => ({ ...prev, searchQuery: text }));
            }}
          />
        </View>
        <TouchableOpacity
          style={[
            styles.filterButton,
            Object.keys(filters).length > 0 && styles.filterButtonActive
          ]}
          onPress={() => setShowFilterModal(true)}
        >
          <Filter
            size={20}
            color={Object.keys(filters).length > 0 ? colors.white : colors.text}
          />
        </TouchableOpacity>
      </View>

      {/* Sort Controls */}
      <View style={styles.sortContainer}>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={toggleSortField}
        >
          <Text style={[styles.sortButtonText, { color: colors.primary[500] }]}>
            Sort by: {sortBy === 'date' ? 'Date' : 'Amount'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.sortOrderButton, { backgroundColor: colors.background }]}
          onPress={toggleSortOrder}
        >
          {sortOrder === 'asc' ? (
            <SortAsc size={20} color={colors.text} />
          ) : (
            <SortDesc size={20} color={colors.text} />
          )}
        </TouchableOpacity>
      </View>

      {/* Active Filters Display */}
      {Object.keys(filters).length > 0 && (
        <View style={[styles.activeFiltersContainer, { backgroundColor: colors.background }]}>
          <Text style={[styles.activeFiltersTitle, { color: colors.textSecondary }]}>
            Active Filters:
          </Text>
          <View style={styles.activeFiltersList}>
            {filters.status && filters.status.length > 0 && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterChipText}>
                  Status: {filters.status.length} selected
                </Text>
              </View>
            )}
            {filters.method && filters.method.length > 0 && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterChipText}>
                  Method: {filters.method.length} selected
                </Text>
              </View>
            )}
            {/* Type and category filters removed as they're not in the PaymentFilters type */}
            {filters.dateRange && (filters.dateRange.start || filters.dateRange.end) && (
              <View style={styles.activeFilterChip}>
                <Text style={styles.activeFilterChipText}>
                  Date Range
                </Text>
              </View>
            )}
            <TouchableOpacity
              style={[styles.resetFiltersButton, { backgroundColor: colors.error[100] }]}
              onPress={resetFilters}
            >
              <Text style={[styles.resetFiltersText, { color: colors.error[500] }]}>Clear All</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Filter Modal */}
      <PaymentFilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApplyFilters={handleApplyFilters}
        initialFilters={filters}
      />

      {/* Payments List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      ) : (
        <FlatList
          data={filteredPayments}
          renderItem={renderPaymentItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    padding: 8,
    fontSize: 16,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#6200ee',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortOrderButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeFiltersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  activeFiltersTitle: {
    fontSize: 12,
    marginBottom: 8,
  },
  activeFiltersList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  activeFilterChip: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  activeFilterChipText: {
    fontSize: 12,
    color: '#6200ee',
  },
  resetFiltersButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 8,
  },
  resetFiltersText: {
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  paymentItem: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  paymentTypeContainer: {
    flex: 1,
  },
  paymentType: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  paymentReference: {
    fontSize: 12,
    color: '#757575',
  },
  paymentAmountContainer: {
    alignItems: 'flex-end',
  },
  paymentAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paymentDetails: {
    marginBottom: 12,
  },
  paymentDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  paymentDetail: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  paymentDetailLabel: {
    fontSize: 14,
    color: '#757575',
    width: 70,
  },
  paymentDetailValue: {
    fontSize: 14,
    flex: 1,
  },
  paymentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  paymentActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 12,
    color: '#6200ee',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#757575',
    textAlign: 'center',
    marginBottom: 16,
  },
  resetButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#6200ee',
    borderRadius: 20,
  },
  resetButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default PaymentHistoryView;
