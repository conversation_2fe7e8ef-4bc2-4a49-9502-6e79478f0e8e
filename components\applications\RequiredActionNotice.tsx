import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { AlertCircle, ChevronRight, Clock } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { router } from 'expo-router';

interface RequiredAction {
  id: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  dueDate?: string;
  completed: boolean;
  action?: {
    label: string;
    route?: string;
    params?: Record<string, string>;
    onPress?: () => void;
  };
}

interface RequiredActionNoticeProps {
  actions: RequiredAction[];
  onComplete?: (actionId: string) => void;
}

const RequiredActionNotice: React.FC<RequiredActionNoticeProps> = ({
  actions,
  onComplete,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Filter out completed actions
  const pendingActions = actions.filter(action => !action.completed);

  // If no pending actions, don't render anything
  if (pendingActions.length === 0) {
    return null;
  }

  // Sort actions by priority (high -> medium -> low)
  const sortedActions = [...pendingActions].sort((a, b) => {
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    return priorityOrder[a.priority] - priorityOrder[b.priority];
  });

  // Get priority color
  const getPriorityColor = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return colors.error[500];
      case 'medium':
        return colors.warning[500];
      case 'low':
        return colors.info[500];
      default:
        return colors.textSecondary;
    }
  };

  // Format due date
  const formatDueDate = (dateString?: string) => {
    if (!dateString) return 'No deadline';

    const dueDate = new Date(dateString);
    const today = new Date();

    // Calculate days difference
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else {
      return `Due in ${diffDays} days`;
    }
  };

  // Handle action button press
  const handleActionPress = (action: RequiredAction) => {
    if (action.action) {
      if (action.action.onPress) {
        // Use the onPress handler if provided
        action.action.onPress();
      } else if (action.action.route) {
        // Otherwise use the route if provided
        if (action.action.params) {
          router.push({
            pathname: action.action.route,
            params: action.action.params
          });
        } else {
          router.push(action.action.route);
        }
      }
    }
  };

  const styles = StyleSheet.create({
    container: {
      marginVertical: spacing.md,
      borderRadius: borders.radius.lg,
      overflow: 'hidden',
      backgroundColor: colors.card,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      backgroundColor: colors.primary[50],
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerIcon: {
      marginRight: spacing.sm,
    },
    headerTitle: {
      ...typography.h4,
      color: colors.primary[700],
      flex: 1,
    },
    actionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    actionContent: {
      flex: 1,
      marginRight: spacing.sm,
    },
    actionDescription: {
      ...typography.body,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    actionMeta: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    priorityIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: spacing.xs,
    },
    priorityText: {
      ...typography.caption,
      marginRight: spacing.md,
    },
    dueDate: {
      ...typography.caption,
      color: colors.textSecondary,
      flex: 1,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.md,
    },
    actionButtonText: {
      ...typography.button,
      color: colors.white,
      marginRight: spacing.xs,
    },
  });

  return (
    <Animated.View style={styles.container} entering={FadeIn.duration(300)}>
      <View style={styles.header}>
        <AlertCircle size={20} color={colors.primary[700]} style={styles.headerIcon} />
        <Text style={styles.headerTitle}>Required Actions</Text>
      </View>

      {sortedActions.map((action) => (
        <View key={action.id} style={styles.actionItem}>
          <View style={styles.actionContent}>
            <Text style={styles.actionDescription}>{action.description}</Text>
            <View style={styles.actionMeta}>
              <View
                style={[
                  styles.priorityIndicator,
                  { backgroundColor: getPriorityColor(action.priority) },
                ]}
              />
              <Text
                style={[
                  styles.priorityText,
                  { color: getPriorityColor(action.priority) },
                ]}
              >
                {action.priority.charAt(0).toUpperCase() + action.priority.slice(1)} Priority
              </Text>
              <Clock size={12} color={colors.textSecondary} />
              <Text style={styles.dueDate}>{formatDueDate(action.dueDate)}</Text>
            </View>
          </View>

          {action.action && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleActionPress(action)}
            >
              <Text style={styles.actionButtonText}>{action.action.label}</Text>
              <ChevronRight size={16} color={colors.white} />
            </TouchableOpacity>
          )}
        </View>
      ))}
    </Animated.View>
  );
};

export default RequiredActionNotice;
