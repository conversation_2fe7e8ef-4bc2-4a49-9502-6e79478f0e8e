import React from 'react';
import { Stack } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';

export default function LegalLayout() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  console.log('[LegalLayout] Rendering legal layout');

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: colors.background },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="terms" />
      <Stack.Screen name="privacy" />
      <Stack.Screen name="manage" />
    </Stack>
  );
}
