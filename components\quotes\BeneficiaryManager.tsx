import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { User, Phone, Mail, Percent, Plus, Trash2, Edit2 } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import DynamicFormField from './DynamicFormField';
import { showToast } from '@/utils/toast';

export interface Beneficiary {
  id: string;
  firstName: string;
  lastName: string;
  relationship: string;
  phone?: string;
  email?: string;
  percentage: number;
}

interface BeneficiaryManagerProps {
  beneficiaries: Beneficiary[];
  onBeneficiariesUpdated: (beneficiaries: Beneficiary[]) => void;
}

const BeneficiaryManager: React.FC<BeneficiaryManagerProps> = ({
  beneficiaries,
  onBeneficiariesUpdated,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const [isAddingBeneficiary, setIsAddingBeneficiary] = useState(false);
  const [editingBeneficiaryId, setEditingBeneficiaryId] = useState<string | null>(null);

  // Form state for new/editing beneficiary
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [relationship, setRelationship] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [percentage, setPercentage] = useState('');

  // Form errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form
  const resetForm = () => {
    setFirstName('');
    setLastName('');
    setRelationship('');
    setPhone('');
    setEmail('');
    setPercentage('');
    setErrors({});
  };

  // Start adding a new beneficiary
  const handleAddBeneficiary = () => {
    resetForm();
    setIsAddingBeneficiary(true);
    setEditingBeneficiaryId(null);
  };

  // Start editing an existing beneficiary
  const handleEditBeneficiary = (beneficiary: Beneficiary) => {
    setFirstName(beneficiary.firstName);
    setLastName(beneficiary.lastName);
    setRelationship(beneficiary.relationship);
    setPhone(beneficiary.phone || '');
    setEmail(beneficiary.email || '');
    setPercentage(beneficiary.percentage.toString());
    setEditingBeneficiaryId(beneficiary.id);
    setIsAddingBeneficiary(true);
  };

  // Delete a beneficiary
  const handleDeleteBeneficiary = (id: string) => {
    const updatedBeneficiaries = beneficiaries.filter(b => b.id !== id);
    onBeneficiariesUpdated(updatedBeneficiaries);

    showToast(
      'success',
      'Beneficiary Removed',
      'Beneficiary has been removed successfully',
      { visibilityTime: 3000 }
    );
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!relationship.trim()) {
      newErrors.relationship = 'Relationship is required';
    }

    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (phone && !/^\+?[0-9]{10,15}$/.test(phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (!percentage.trim()) {
      newErrors.percentage = 'Percentage is required';
    } else {
      const percentValue = parseFloat(percentage);
      if (isNaN(percentValue) || percentValue <= 0 || percentValue > 100) {
        newErrors.percentage = 'Percentage must be between 1 and 100';
      } else {
        // Check if total percentage would exceed 100%
        const currentTotal = beneficiaries.reduce((sum, b) => {
          if (editingBeneficiaryId && b.id === editingBeneficiaryId) {
            return sum; // Skip the one being edited
          }
          return sum + b.percentage;
        }, 0);

        if (currentTotal + percentValue > 100) {
          newErrors.percentage = `Total percentage cannot exceed 100%. Current total: ${currentTotal}%`;
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save beneficiary
  const handleSaveBeneficiary = () => {
    if (!validateForm()) {
      return;
    }

    const beneficiary: Beneficiary = {
      id: editingBeneficiaryId || Date.now().toString(),
      firstName,
      lastName,
      relationship,
      phone: phone || undefined,
      email: email || undefined,
      percentage: parseFloat(percentage),
    };

    let updatedBeneficiaries: Beneficiary[];

    if (editingBeneficiaryId) {
      // Update existing beneficiary
      updatedBeneficiaries = beneficiaries.map(b =>
        b.id === editingBeneficiaryId ? beneficiary : b
      );

      showToast(
        'success',
        'Beneficiary Updated',
        'Beneficiary has been updated successfully',
        { visibilityTime: 3000 }
      );
    } else {
      // Add new beneficiary
      updatedBeneficiaries = [...beneficiaries, beneficiary];

      showToast(
        'success',
        'Beneficiary Added',
        'Beneficiary has been added successfully',
        { visibilityTime: 3000 }
      );
    }

    onBeneficiariesUpdated(updatedBeneficiaries);
    setIsAddingBeneficiary(false);
    resetForm();
  };

  // Cancel adding/editing
  const handleCancel = () => {
    setIsAddingBeneficiary(false);
    resetForm();
  };

  // Render beneficiary form
  const renderBeneficiaryForm = () => {
    return (
      <Animated.View
        style={styles.formContainer}
        entering={FadeInDown.delay(100).springify()}
      >
        <Text style={[styles.formTitle, { color: colors.text }]}>
          {editingBeneficiaryId ? 'Edit Beneficiary' : 'Add Beneficiary'}
        </Text>

        <DynamicFormField
          label="First Name"
          type="text"
          value={firstName}
          onChange={setFirstName}
          placeholder="Enter first name"
          error={errors.firstName}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Last Name"
          type="text"
          value={lastName}
          onChange={setLastName}
          placeholder="Enter last name"
          error={errors.lastName}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Relationship"
          type="text"
          value={relationship}
          onChange={setRelationship}
          placeholder="e.g., Spouse, Child, Parent"
          error={errors.relationship}
          required
          icon={<User size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Phone Number"
          type="phone"
          value={phone}
          onChange={setPhone}
          placeholder="Enter phone number (optional)"
          error={errors.phone}
          icon={<Phone size={20} color={colors.textSecondary} />}
          keyboardType="phone-pad"
        />

        <DynamicFormField
          label="Email"
          type="email"
          value={email}
          onChange={setEmail}
          placeholder="Enter email address (optional)"
          error={errors.email}
          icon={<Mail size={20} color={colors.textSecondary} />}
          keyboardType="email-address"
        />

        <DynamicFormField
          label="Percentage"
          type="number"
          value={percentage}
          onChange={setPercentage}
          placeholder="e.g., 50"
          error={errors.percentage}
          required
          keyboardType="numeric"
          icon={<Percent size={20} color={colors.textSecondary} />}
        />

        <View style={styles.formActions}>
          <TouchableOpacity
            style={[styles.cancelButton, { backgroundColor: colors.neutral[200] }]}
            onPress={handleCancel}
          >
            <Text style={[styles.buttonText, { color: colors.text }]}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary[500] }]}
            onPress={handleSaveBeneficiary}
          >
            <Text style={[styles.buttonText, { color: colors.white }]}>Save</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  // Render beneficiary item
  const renderBeneficiaryItem = (beneficiary: Beneficiary, index: number) => {
    return (
      <Animated.View
        key={beneficiary.id}
        style={[
          styles.beneficiaryItem,
          {
            backgroundColor: colors.card,
            borderColor: colors.border
          }
        ]}
        entering={FadeInDown.delay(index * 100).springify()}
      >
        <View style={styles.beneficiaryInfo}>
          <Text style={[styles.beneficiaryName, { color: colors.text }]}>
            {beneficiary.firstName} {beneficiary.lastName}
          </Text>
          <Text style={[styles.beneficiaryRelationship, { color: colors.textSecondary }]}>
            {beneficiary.relationship}
          </Text>
          <Text style={[styles.beneficiaryPercentage, { color: colors.primary[500] }]}>
            {beneficiary.percentage}%
          </Text>
        </View>

        <View style={styles.beneficiaryActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary[50] }]}
            onPress={() => handleEditBeneficiary(beneficiary)}
          >
            <Edit2 size={16} color={colors.primary[500]} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.error[50] }]}
            onPress={() => handleDeleteBeneficiary(beneficiary.id)}
          >
            <Trash2 size={16} color={colors.error[500]} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  // Calculate total percentage
  const totalPercentage = beneficiaries.reduce((sum, b) => sum + b.percentage, 0);

  return (
    <View style={styles.container}>
      {!isAddingBeneficiary && (
        <>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text }]}>Beneficiaries</Text>
            <Text style={[styles.totalPercentage, { color: totalPercentage === 100 ? colors.success[500] : colors.warning[500] }]}>
              Total: {totalPercentage}%
            </Text>
          </View>

          {beneficiaries.length === 0 ? (
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No beneficiaries added yet. Add beneficiaries who will receive the policy benefits.
            </Text>
          ) : (
            beneficiaries.map((beneficiary, index) => renderBeneficiaryItem(beneficiary, index))
          )}

          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary[500] }]}
            onPress={handleAddBeneficiary}
          >
            <Plus size={20} color={colors.white} />
            <Text style={[styles.addButtonText, { color: colors.white }]}>
              Add Beneficiary
            </Text>
          </TouchableOpacity>
        </>
      )}

      {isAddingBeneficiary && renderBeneficiaryForm()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  totalPercentage: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyText: {
    fontSize: 14,
    marginBottom: 16,
  },
  beneficiaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  beneficiaryInfo: {
    flex: 1,
  },
  beneficiaryName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  beneficiaryRelationship: {
    fontSize: 14,
    marginBottom: 4,
  },
  beneficiaryPercentage: {
    fontSize: 14,
    fontWeight: '500',
  },
  beneficiaryActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  formContainer: {
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default BeneficiaryManager;
