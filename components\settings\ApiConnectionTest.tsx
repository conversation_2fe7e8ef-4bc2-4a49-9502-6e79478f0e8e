import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react-native';
import { checkApiHealthWithToast } from '@/utils/apiHealthCheck';

interface ApiConnectionTestProps {
  onConnectionChange?: (isConnected: boolean) => void;
}

const ApiConnectionTest: React.FC<ApiConnectionTestProps> = ({ 
  onConnectionChange 
}) => {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const testConnection = async () => {
    setIsLoading(true);
    
    try {
      const result = await checkApiHealthWithToast(true);
      setIsConnected(result);
      
      if (onConnectionChange) {
        onConnectionChange(result);
      }
    } catch (error) {
      console.error('Error testing API connection:', error);
      setIsConnected(false);
      
      if (onConnectionChange) {
        onConnectionChange(false);
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          {isLoading ? (
            <ActivityIndicator size="small" color={colors.primary[500]} />
          ) : isConnected === null ? (
            <Wifi size={24} color={colors.textSecondary} />
          ) : isConnected ? (
            <Wifi size={24} color={colors.success[500]} />
          ) : (
            <WifiOff size={24} color={colors.error[500]} />
          )}
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            API Connection
          </Text>
          <Text style={[styles.description, { color: colors.textSecondary }]}>
            {isLoading
              ? 'Testing connection to backend...'
              : isConnected === null
              ? 'Test connection to the Inerca backend'
              : isConnected
              ? 'Connected to the Inerca backend'
              : 'Not connected to the Inerca backend'}
          </Text>
        </View>
      </View>
      
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: colors.primary[50],
            borderColor: colors.primary[100],
          },
        ]}
        onPress={testConnection}
        disabled={isLoading}
      >
        <RefreshCw
          size={16}
          color={colors.primary[500]}
          style={isLoading ? styles.rotating : undefined}
        />
        <Text style={[styles.buttonText, { color: colors.primary[500] }]}>
          {isLoading ? 'Testing...' : 'Test Connection'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  rotating: {
    // Note: In a real app, you would use Animated.timing to create a rotation animation
  },
});

export default ApiConnectionTest;
