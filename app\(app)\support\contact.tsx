import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Linking,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  ArrowLeft,
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageCircle,
  Send,
  ExternalLink,
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { showToast } from '@/utils/toast';

interface ContactMethod {
  id: string;
  title: string;
  subtitle: string;
  icon: any;
  color: string;
  action: () => void;
}

export default function ContactSupportScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const contactMethods: ContactMethod[] = [
    {
      id: 'phone',
      title: 'Call Us',
      subtitle: '+************',
      icon: Phone,
      color: colors.primary[500],
      action: () => {
        Linking.openURL('tel:+2671234567').catch(() => {
          Alert.alert('Error', 'Unable to make phone call');
        });
      },
    },
    {
      id: 'email',
      title: 'Email Us',
      subtitle: '<EMAIL>',
      icon: Mail,
      color: colors.secondary[500],
      action: () => {
        Linking.openURL('mailto:<EMAIL>').catch(() => {
          Alert.alert('Error', 'Unable to open email client');
        });
      },
    },
    {
      id: 'chat',
      title: 'Live Chat',
      subtitle: 'Chat with our support team',
      icon: MessageCircle,
      color: colors.success[500],
      action: () => {
        router.push('/support/chat');
      },
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp',
      subtitle: '+************',
      icon: MessageCircle,
      color: '#25D366',
      action: () => {
        Linking.openURL('https://wa.me/2671234567').catch(() => {
          Alert.alert('Error', 'Unable to open WhatsApp');
        });
      },
    },
  ];

  const officeHours = [
    { day: 'Monday - Friday', hours: '8:00 AM - 5:00 PM' },
    { day: 'Saturday', hours: '9:00 AM - 1:00 PM' },
    { day: 'Sunday', hours: 'Closed' },
    { day: 'Emergency Claims', hours: '24/7' },
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmitForm = async () => {
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      showToast('Please fill in all fields', 'error');
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showToast('Your message has been sent successfully!', 'success');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      showToast('Failed to send message. Please try again.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderContactMethod = (method: ContactMethod, index: number) => {
    const Icon = method.icon;

    return (
      <Animated.View
        key={method.id}
        entering={FadeInDown.delay(index * 100).springify()}
      >
        <TouchableOpacity
          style={[styles.contactMethod, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={method.action}
        >
          <View style={[styles.contactIcon, { backgroundColor: `${method.color}20` }]}>
            <Icon size={24} color={method.color} />
          </View>
          <View style={styles.contactInfo}>
            <Text style={[styles.contactTitle, { color: colors.text }]}>
              {method.title}
            </Text>
            <Text style={[styles.contactSubtitle, { color: colors.textSecondary }]}>
              {method.subtitle}
            </Text>
          </View>
          <ExternalLink size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      padding: spacing.sm,
      marginRight: spacing.md,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.xl,
      color: colors.text,
      flex: 1,
    },
    content: {
      flex: 1,
    },
    section: {
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    contactMethod: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      marginBottom: spacing.sm,
    },
    contactIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    contactInfo: {
      flex: 1,
    },
    contactTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginBottom: spacing.xs,
    },
    contactSubtitle: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
    },
    officeHoursContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      borderColor: colors.border,
      padding: spacing.md,
    },
    officeHourRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    officeHourDay: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    officeHourTime: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    formContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      borderColor: colors.border,
      padding: spacing.md,
    },
    formGroup: {
      marginBottom: spacing.md,
    },
    label: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    input: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      backgroundColor: colors.background,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    submitButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.lg,
      paddingVertical: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      marginTop: spacing.md,
    },
    submitButtonDisabled: {
      opacity: 0.6,
    },
    submitButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    locationContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      borderWidth: 1,
      borderColor: colors.border,
      padding: spacing.md,
    },
    locationText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      lineHeight: 20,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Contact Support</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Get in Touch</Text>
          {contactMethods.map(renderContactMethod)}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Office Hours</Text>
          <View style={styles.officeHoursContainer}>
            <View style={[styles.contactIcon, { backgroundColor: `${colors.primary[500]}20`, marginBottom: spacing.md }]}>
              <Clock size={24} color={colors.primary[500]} />
            </View>
            {officeHours.map((item, index) => (
              <View
                key={index}
                style={[
                  styles.officeHourRow,
                  index === officeHours.length - 1 && { borderBottomWidth: 0 },
                ]}
              >
                <Text style={styles.officeHourDay}>{item.day}</Text>
                <Text style={styles.officeHourTime}>{item.hours}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Send us a Message</Text>
          <View style={styles.formContainer}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Name *</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                placeholder="Your full name"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Email *</Text>
              <TextInput
                style={styles.input}
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                placeholder="<EMAIL>"
                placeholderTextColor={colors.textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Subject *</Text>
              <TextInput
                style={styles.input}
                value={formData.subject}
                onChangeText={(value) => handleInputChange('subject', value)}
                placeholder="Brief description of your inquiry"
                placeholderTextColor={colors.textSecondary}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Message *</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.message}
                onChangeText={(value) => handleInputChange('message', value)}
                placeholder="Please provide details about your inquiry..."
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={4}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.submitButton,
                isSubmitting && styles.submitButtonDisabled,
              ]}
              onPress={handleSubmitForm}
              disabled={isSubmitting}
            >
              <Send size={20} color={colors.white} />
              <Text style={styles.submitButtonText}>
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Our Location</Text>
          <View style={styles.locationContainer}>
            <View style={[styles.contactIcon, { backgroundColor: `${colors.primary[500]}20`, marginBottom: spacing.md }]}>
              <MapPin size={24} color={colors.primary[500]} />
            </View>
            <Text style={styles.locationText}>
              Inerca Holdings{'\n'}
              Plot 123, Main Mall{'\n'}
              Gaborone, Botswana{'\n'}
              P.O. Box 12345{'\n'}
              Gaborone
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
