import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import {
  CheckCircle,
  AlertCircle,
  Info,
  X,
  Bell,
  FileText,
  CreditCard,
  Shield,
} from 'lucide-react-native';

import { useTheme } from '@/context/ThemeContext';
import { Notification, NotificationType } from '@/store/notificationSlice';

interface NotificationToastProps {
  notification: Notification;
  onDismiss: () => void;
  onPress?: () => void;
  duration?: number;
}

const { width: screenWidth } = Dimensions.get('window');

const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onDismiss,
  onPress,
  duration = 5000,
}) => {
  const { colors } = useTheme();
  const [isVisible, setIsVisible] = useState(true);

  const translateY = useSharedValue(-100);
  const opacity = useSharedValue(0);

  useEffect(() => {
    // Animate in
    translateY.value = withSpring(0, { damping: 15, stiffness: 150 });
    opacity.value = withTiming(1, { duration: 300 });

    // Auto dismiss after duration
    const timer = setTimeout(() => {
      handleDismiss();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleDismiss = () => {
    translateY.value = withTiming(-100, { duration: 300 });
    opacity.value = withTiming(0, { duration: 300 }, () => {
      runOnJS(onDismiss)();
    });
    setIsVisible(false);
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
    handleDismiss();
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  const getNotificationIcon = (type: NotificationType) => {
    const iconProps = { size: 20, color: colors.white };
    
    switch (type) {
      case 'success':
      case 'document_verified':
      case 'payment_verified':
      case 'policy_dispatched':
        return <CheckCircle {...iconProps} />;
      
      case 'error':
        return <AlertCircle {...iconProps} />;
      
      case 'warning':
      case 'document_reminder':
      case 'renewal_reminder':
        return <AlertCircle {...iconProps} />;
      
      case 'info':
      case 'quotation_sent':
      case 'quote_generated':
      case 'admin_notes':
        return <Info {...iconProps} />;
      
      case 'document':
        return <FileText {...iconProps} />;
      
      case 'payment':
      case 'payment_uploaded':
        return <CreditCard {...iconProps} />;
      
      case 'policy':
      case 'policy_issued':
      case 'policy_bound':
        return <Shield {...iconProps} />;
      
      default:
        return <Bell {...iconProps} />;
    }
  };

  const getNotificationColors = (type: NotificationType) => {
    switch (type) {
      case 'success':
      case 'document_verified':
      case 'payment_verified':
      case 'policy_dispatched':
        return {
          background: colors.success[500],
          border: colors.success[600],
        };
      
      case 'error':
        return {
          background: colors.error[500],
          border: colors.error[600],
        };
      
      case 'warning':
      case 'document_reminder':
      case 'renewal_reminder':
        return {
          background: colors.warning[500],
          border: colors.warning[600],
        };
      
      case 'info':
      case 'quotation_sent':
      case 'quote_generated':
      case 'admin_notes':
        return {
          background: colors.info[500],
          border: colors.info[600],
        };
      
      default:
        return {
          background: colors.primary[500],
          border: colors.primary[600],
        };
    }
  };

  if (!isVisible) return null;

  const notificationColors = getNotificationColors(notification.type);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: notificationColors.background,
          borderColor: notificationColors.border,
        },
        animatedStyle,
      ]}
    >
      <TouchableOpacity
        style={styles.content}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <View style={styles.iconContainer}>
          {getNotificationIcon(notification.type)}
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: colors.white }]} numberOfLines={1}>
            {notification.title}
          </Text>
          <Text style={[styles.message, { color: colors.white }]} numberOfLines={2}>
            {notification.message}
          </Text>
          {notification.actionLabel && (
            <Text style={[styles.actionLabel, { color: colors.white }]}>
              Tap to {notification.actionLabel.toLowerCase()}
            </Text>
          )}
        </View>

        <TouchableOpacity
          style={styles.dismissButton}
          onPress={handleDismiss}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <X size={18} color={colors.white} />
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Progress indicator */}
      <Animated.View
        style={[
          styles.progressBar,
          {
            backgroundColor: 'rgba(255, 255, 255, 0.3)',
          },
        ]}
      >
        <Animated.View
          style={[
            styles.progress,
            {
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              width: `${100 - (duration / 100)}%`,
            },
          ]}
        />
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
    gap: 2,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
  },
  message: {
    fontSize: 13,
    lineHeight: 16,
    opacity: 0.9,
  },
  actionLabel: {
    fontSize: 11,
    fontWeight: '500',
    marginTop: 4,
    opacity: 0.8,
  },
  dismissButton: {
    padding: 4,
  },
  progressBar: {
    height: 3,
    width: '100%',
  },
  progress: {
    height: '100%',
  },
});

export default NotificationToast;
