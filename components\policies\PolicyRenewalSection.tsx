import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Calendar, AlertCircle, Check, Clock, RefreshCw, FileText } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Policy, PolicyStatus } from '@/store/policyStore';
import { formatCurrency } from '@/utils/quoteCalculations';
import { showToast } from '@/utils/toast';

interface PolicyRenewalSectionProps {
  policy: Policy;
  onRenewPolicy: () => Promise<any>;
}

const PolicyRenewalSection: React.FC<PolicyRenewalSectionProps> = ({
  policy,
  onRenewPolicy,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [isRenewing, setIsRenewing] = useState(false);
  const [newPremium, setNewPremium] = useState<number | null>(null);
  const [renewalDocuments, setRenewalDocuments] = useState<any[]>([]);

  // Calculate estimated new premium on component mount
  useEffect(() => {
    // Simulate premium calculation with a slight increase
    const premiumIncrease = Math.random() * 0.1 + 0.02; // 2% to 12% increase
    const calculatedNewPremium = policy.premium * (1 + premiumIncrease);
    setNewPremium(Math.round(calculatedNewPremium * 100) / 100);

    // Simulate renewal documents
    if (policy.status === 'pending_renewal' || policy.status === 'renewed') {
      setRenewalDocuments([
        {
          id: `renewal-doc-${Date.now()}-1`,
          name: 'Renewal Terms & Conditions',
          type: 'renewal',
          date: new Date().toISOString().split('T')[0]
        },
        {
          id: `renewal-doc-${Date.now()}-2`,
          name: 'Premium Adjustment Notice',
          type: 'renewal',
          date: new Date().toISOString().split('T')[0]
        }
      ]);
    }
  }, [policy.premium, policy.status]);

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate days until renewal
  const getDaysUntilRenewal = () => {
    const renewalDate = new Date(policy.renewalDate);
    const today = new Date();
    const diffTime = renewalDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get renewal status
  const getRenewalStatus = () => {
    const daysUntilRenewal = getDaysUntilRenewal();

    if (policy.status === 'pending_renewal') {
      return {
        color: colors.warning[500],
        bgColor: colors.warning[50],
        icon: <RefreshCw size={16} color={colors.warning[500]} />,
        text: 'Renewal in Progress',
        message: 'Your policy renewal is being processed. You will be notified once complete.',
      };
    } else if (policy.status === 'renewed') {
      return {
        color: colors.success[500],
        bgColor: colors.success[50],
        icon: <Check size={16} color={colors.success[500]} />,
        text: 'Recently Renewed',
        message: `Your policy was renewed on ${formatDate(policy.startDate)}.`,
      };
    } else if (daysUntilRenewal <= 0) {
      return {
        color: colors.error[500],
        bgColor: colors.error[50],
        icon: <AlertCircle size={16} color={colors.error[500]} />,
        text: 'Renewal Overdue',
        message: 'Your policy has expired. Please renew immediately to maintain coverage.',
      };
    } else if (daysUntilRenewal <= 30) {
      return {
        color: colors.warning[500],
        bgColor: colors.warning[50],
        icon: <Clock size={16} color={colors.warning[500]} />,
        text: 'Renewal Due Soon',
        message: `Your policy is due for renewal in ${daysUntilRenewal} days.`,
      };
    } else {
      return {
        color: colors.info[500],
        bgColor: colors.info[50],
        icon: <Calendar size={16} color={colors.info[500]} />,
        text: 'Active',
        message: `Your policy is active until ${formatDate(policy.endDate)}.`,
      };
    }
  };

  // Handle renewal
  const handleRenewal = async () => {
    if (isRenewing) return;

    setIsRenewing(true);
    try {
      await onRenewPolicy();
      showToast(
        'success',
        'Renewal Initiated',
        'Your policy renewal has been initiated successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error renewing policy:', error);
      showToast(
        'error',
        'Renewal Failed',
        'Failed to initiate policy renewal. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsRenewing(false);
    }
  };

  // Calculate premium change percentage
  const getPremiumChangePercentage = () => {
    if (!newPremium) return 0;
    return ((newPremium - policy.premium) / policy.premium) * 100;
  };

  const renewalStatus = getRenewalStatus();
  const daysUntilRenewal = getDaysUntilRenewal();
  const showRenewButton = policy.status === 'active' && daysUntilRenewal <= 30;
  const premiumChangePercentage = getPremiumChangePercentage();

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: renewalStatus.bgColor,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.md,
    },
    statusIcon: {
      marginRight: spacing.sm,
    },
    statusContent: {
      flex: 1,
    },
    statusText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: renewalStatus.color,
      marginBottom: spacing.xs,
    },
    statusMessage: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    infoLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    premiumChangeContainer: {
      alignItems: 'flex-end',
      marginBottom: spacing.md,
    },
    premiumChangeText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
    },
    documentsContainer: {
      marginTop: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      paddingTop: spacing.md,
    },
    documentsTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    documentItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    documentIcon: {
      marginRight: spacing.sm,
    },
    documentName: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    renewButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      alignItems: 'center',
      marginTop: spacing.md,
    },
    renewButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Policy Renewal</Text>

      <Animated.View
        style={styles.card}
        entering={FadeInDown.delay(100).springify()}
      >
        <View style={styles.statusContainer}>
          {renewalStatus.icon}
          <View style={styles.statusContent}>
            <Text style={styles.statusText}>{renewalStatus.text}</Text>
            <Text style={styles.statusMessage}>{renewalStatus.message}</Text>
          </View>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Policy Number</Text>
          <Text style={styles.infoValue}>{policy.policyNumber}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Start Date</Text>
          <Text style={styles.infoValue}>{formatDate(policy.startDate)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>End Date</Text>
          <Text style={styles.infoValue}>{formatDate(policy.endDate)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Renewal Date</Text>
          <Text style={styles.infoValue}>{formatDate(policy.renewalDate)}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Current Premium</Text>
          <Text style={styles.infoValue}>{formatCurrency(policy.premium, policy.currency)}</Text>
        </View>

        {newPremium && (policy.status === 'pending_renewal' || daysUntilRenewal <= 30) && (
          <>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Estimated New Premium</Text>
              <Text style={styles.infoValue}>{formatCurrency(newPremium, policy.currency)}</Text>
            </View>
            <View style={styles.premiumChangeContainer}>
              <Text style={[
                styles.premiumChangeText,
                { color: premiumChangePercentage > 0 ? colors.error[500] : colors.success[500] }
              ]}>
                {premiumChangePercentage > 0 ? '+' : ''}
                {formatCurrency(newPremium - policy.premium, policy.currency)}
                ({premiumChangePercentage.toFixed(1)}%)
              </Text>
            </View>
          </>
        )}

        {renewalDocuments.length > 0 && (
          <View style={styles.documentsContainer}>
            <Text style={styles.documentsTitle}>Renewal Documents</Text>
            {renewalDocuments.map((doc) => (
              <View key={doc.id} style={styles.documentItem}>
                <FileText size={16} color={colors.primary[500]} style={styles.documentIcon} />
                <Text style={styles.documentName}>{doc.name}</Text>
              </View>
            ))}
          </View>
        )}

        {showRenewButton && (
          <TouchableOpacity
            style={styles.renewButton}
            onPress={handleRenewal}
            disabled={isRenewing}
          >
            <Text style={styles.renewButtonText}>
              {isRenewing ? 'Processing...' : 'Renew Policy'}
            </Text>
          </TouchableOpacity>
        )}
      </Animated.View>
    </View>
  );
};

export default PolicyRenewalSection;
