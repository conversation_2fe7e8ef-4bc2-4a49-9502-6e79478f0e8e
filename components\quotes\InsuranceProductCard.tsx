import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Car, Home, Plane, Heart, Shield, Briefcase } from 'lucide-react-native';
import { InsuranceProductType } from '@/types/quote.types';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface InsuranceProductCardProps {
  id: string;
  type: InsuranceProductType;
  name: string;
  description: string;
  color: string;
  onSelect: (type: string) => void;
  delay?: number;
}

const InsuranceProductCard: React.FC<InsuranceProductCardProps> = ({
  id,
  type,
  name,
  description,
  color,
  onSelect,
  delay = 0,
}) => {
  // Add error handling for theme context
  const themeContext = useTheme();
  const isDarkMode = themeContext?.isDarkMode ?? false;

  // Create theme with fallback
  const theme = createTheme(isDarkMode);
  const { colors, spacing, typography, borders, shadows } = theme;

  // Get the appropriate icon based on the insurance type
  const getIcon = () => {
    switch (type) {
      case 'motor':
        return <Car size={24} color={color} />;
      case 'houseowners':
      case 'householdContents':
        return <Home size={24} color={color} />;
      case 'allRisks':
        return <Shield size={24} color={color} />;
      case 'travel':
        return <Plane size={24} color={color} />;
      case 'health':
        return <Heart size={24} color={color} />;
      case 'life':
      case 'funeral':
        return <Shield size={24} color={color} />;
      case 'scheme':
      case 'business':
        return <Briefcase size={24} color={color} />;
      default:
        return <Shield size={24} color={color} />;
    }
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...shadows.sm,
      borderLeftWidth: 4,
      borderLeftColor: color,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: borders.radius.md,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: `${color}15`, // 15% opacity of the color
      marginRight: spacing.md,
    },
    contentContainer: {
      flex: 1,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    description: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.sm,
    },
    button: {
      backgroundColor: `${color}15`, // 15% opacity of the color
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borders.radius.md,
      alignSelf: 'flex-start',
      marginTop: spacing.xs,
    },
    buttonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: color,
    },
  });

  // Handle selection with error handling
  const handleSelect = () => {
    try {
      onSelect(type);
    } catch (error) {
      console.error('Error selecting insurance product:', error);
    }
  };

  return (
    <Animated.View
      entering={FadeInDown.delay(100 + delay * 100).springify()}
    >
      <TouchableOpacity
        style={styles.container}
        onPress={handleSelect}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            {getIcon()}
          </View>
          <View style={styles.contentContainer}>
            <Text style={styles.title}>{name}</Text>
            <Text style={styles.description}>{description}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.button}
          onPress={handleSelect}
        >
          <Text style={styles.buttonText}>Get a Quote</Text>
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default InsuranceProductCard;
