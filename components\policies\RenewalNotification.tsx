import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Clock, X, AlertTriangle, AlertCircle, Info } from 'lucide-react-native';
import { RenewalNotification } from '@/services/renewalService';
import { format } from 'date-fns';
import { router } from 'expo-router';
import Animated, { FadeIn } from 'react-native-reanimated';

interface RenewalNotificationProps {
  notification: RenewalNotification;
  onDismiss: () => void;
}

const RenewalNotificationComponent: React.FC<RenewalNotificationProps> = ({
  notification,
  onDismiss,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  // Get background color based on notification type
  const getBackgroundColor = () => {
    switch (notification.notificationType) {
      case 'overdue':
        return `${colors.error[500]}20`;
      case 'due':
        return `${colors.warning[500]}20`;
      case 'upcoming':
        return `${colors.info[500]}20`;
      default:
        return colors.card;
    }
  };

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.notificationType) {
      case 'overdue':
        return <AlertCircle size={24} color={colors.error[500]} />;
      case 'due':
        return <AlertTriangle size={24} color={colors.warning[500]} />;
      case 'upcoming':
        return <Info size={24} color={colors.info[500]} />;
      default:
        return <Clock size={24} color={colors.primary[500]} />;
    }
  };

  // Get message based on notification type
  const getMessage = () => {
    switch (notification.notificationType) {
      case 'overdue':
        return `Your ${notification.policyType} policy is overdue for renewal. Please renew as soon as possible to maintain coverage.`;
      case 'due':
        return `Your ${notification.policyType} policy is due for renewal in ${notification.daysUntilRenewal} day${notification.daysUntilRenewal !== 1 ? 's' : ''}. Please renew to maintain coverage.`;
      case 'upcoming':
        return `Your ${notification.policyType} policy is due for renewal in ${notification.daysUntilRenewal} days. Start the renewal process to ensure continuous coverage.`;
      default:
        return `Your ${notification.policyType} policy is due for renewal on ${format(new Date(notification.renewalDate), 'MMMM d, yyyy')}.`;
    }
  };

  // Handle renewal button press
  const handleRenewPress = () => {
    router.push(`/policies/${notification.policyId}/renew`);
  };

  // Create styles
  const styles = StyleSheet.create({
    container: {
      backgroundColor: getBackgroundColor(),
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.sm,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginLeft: spacing.sm,
    },
    dismissButton: {
      padding: spacing.xs,
    },
    message: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      marginBottom: spacing.md,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    policyNumber: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
    },
    renewButton: {
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
    },
    renewButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
    },
  });

  if (notification.dismissed) {
    return null;
  }

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeIn.duration(300)}
    >
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          {getIcon()}
          <Text style={styles.title}>
            {notification.notificationType === 'overdue'
              ? 'Policy Renewal Overdue'
              : notification.notificationType === 'due'
              ? 'Policy Renewal Due Soon'
              : 'Policy Renewal Reminder'}
          </Text>
        </View>
        <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
          <X size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      <Text style={styles.message}>{getMessage()}</Text>
      <View style={styles.footer}>
        <Text style={styles.policyNumber}>Policy: {notification.policyNumber}</Text>
        <TouchableOpacity style={styles.renewButton} onPress={handleRenewPress}>
          <Text style={styles.renewButtonText}>Renew Now</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

export default RenewalNotificationComponent;
