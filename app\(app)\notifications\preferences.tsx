import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { router } from 'expo-router';
import NotificationPreferencesComponent from '@/components/notifications/NotificationPreferences';
import HeaderBar from '@/components/ui/HeaderBar';

export default function NotificationPreferencesScreen() {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);

  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
    },
  });

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <HeaderBar 
        title="Notification Settings" 
        showBackButton 
        onBackPress={() => router.back()} 
      />
      <View style={styles.content}>
        <NotificationPreferencesComponent onClose={() => router.back()} />
      </View>
    </SafeAreaView>
  );
}
