import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { router, useLocalSearchParams } from 'expo-router';
import HeaderBar from '@/components/ui/HeaderBar';
import usePolicyStore, { Policy } from '@/store/policyStore';
import { Check, AlertCircle, Clock } from 'lucide-react-native';
import { format, addYears, parseISO } from 'date-fns';
import { showToast } from '@/utils/toast';
import useNotificationStore from '@/store/notificationStore';

export default function PolicyRenewalScreen() {
  const { isDarkMode } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);
  const params = useLocalSearchParams();
  const policyId = params.id as string;
  
  // Get policy store
  const { policies, isLoading, renewPolicy } = usePolicyStore();
  const [policy, setPolicy] = useState<Policy | null>(null);
  const [isRenewing, setIsRenewing] = useState(false);
  
  // Get notification store
  const { addNotification } = useNotificationStore();
  
  // Load policy on mount
  useEffect(() => {
    if (policyId && policies.length > 0) {
      const foundPolicy = policies.find(p => p.id === policyId);
      setPolicy(foundPolicy || null);
    }
  }, [policyId, policies]);
  
  // Calculate new expiry date (1 year from current expiry)
  const getNewExpiryDate = () => {
    if (!policy?.expiryDate) return 'Unknown';
    
    const currentExpiry = parseISO(policy.expiryDate);
    const newExpiry = addYears(currentExpiry, 1);
    
    return format(newExpiry, 'MMMM d, yyyy');
  };
  
  // Handle renewal
  const handleRenew = async () => {
    if (!policy) return;
    
    setIsRenewing(true);
    
    try {
      // Renew policy
      await renewPolicy(policy.id);
      
      // Show success toast
      showToast(
        'success',
        'Policy Renewed',
        'Your policy has been successfully renewed',
        { visibilityTime: 3000 }
      );
      
      // Add notification
      await addNotification({
        title: 'Policy Renewed Successfully',
        message: `Your ${policy.type} policy (${policy.policyNumber}) has been renewed until ${getNewExpiryDate()}.`,
        type: 'success',
        read: false,
        category: 'policy',
        actionRoute: `/policies/${policy.id}`,
        actionLabel: 'View Policy',
        actionData: {
          policyId: policy.id,
        },
      });
      
      // Navigate back to policy details
      router.replace(`/policies/${policy.id}`);
    } catch (error) {
      console.error('Error renewing policy:', error);
      
      // Show error toast
      showToast(
        'error',
        'Renewal Failed',
        'Failed to renew policy. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsRenewing(false);
    }
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
      padding: spacing.lg,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.xl,
    },
    errorIcon: {
      marginBottom: spacing.md,
    },
    errorTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    errorMessage: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    card: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.lg,
      marginBottom: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    policyInfo: {
      marginBottom: spacing.md,
    },
    policyInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    policyInfoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    policyInfoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    renewalInfo: {
      backgroundColor: `${colors.info[500]}15`,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.lg,
    },
    renewalInfoTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    renewalInfoText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    renewButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      paddingVertical: spacing.md,
      alignItems: 'center',
      marginTop: spacing.lg,
    },
    renewButtonText: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.white,
    },
  });
  
  // Show loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? 'light' : 'dark'} />
        <HeaderBar 
          title="Policy Renewal" 
          showBackButton 
          onBackPress={() => router.back()} 
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
        </View>
      </SafeAreaView>
    );
  }
  
  // Show error state if policy not found
  if (!policy) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style={isDarkMode ? 'light' : 'dark'} />
        <HeaderBar 
          title="Policy Renewal" 
          showBackButton 
          onBackPress={() => router.back()} 
        />
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={colors.error[500]} style={styles.errorIcon} />
          <Text style={styles.errorTitle}>Policy Not Found</Text>
          <Text style={styles.errorMessage}>
            The policy you are looking for could not be found. Please try again.
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <HeaderBar 
        title="Policy Renewal" 
        showBackButton 
        onBackPress={() => router.back()} 
      />
      <ScrollView style={styles.content}>
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>Policy Information</Text>
          <View style={styles.policyInfo}>
            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>Policy Number</Text>
              <Text style={styles.policyInfoValue}>{policy.policyNumber}</Text>
            </View>
            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>Policy Type</Text>
              <Text style={styles.policyInfoValue}>{policy.type}</Text>
            </View>
            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>Current Expiry Date</Text>
              <Text style={styles.policyInfoValue}>
                {policy.expiryDate ? format(parseISO(policy.expiryDate), 'MMMM d, yyyy') : 'Unknown'}
              </Text>
            </View>
            <View style={styles.policyInfoRow}>
              <Text style={styles.policyInfoLabel}>New Expiry Date</Text>
              <Text style={styles.policyInfoValue}>{getNewExpiryDate()}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.renewalInfo}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: spacing.sm }}>
            <Clock size={20} color={colors.info[500]} style={{ marginRight: spacing.sm }} />
            <Text style={styles.renewalInfoTitle}>Renewal Information</Text>
          </View>
          <Text style={styles.renewalInfoText}>
            Renewing your policy will extend your coverage for another year. Your premium may be adjusted based on updated risk factors and market conditions. After renewal, you will receive a new policy document with the updated terms and conditions.
          </Text>
        </View>
        
        <TouchableOpacity
          style={styles.renewButton}
          onPress={handleRenew}
          disabled={isRenewing}
        >
          {isRenewing ? (
            <ActivityIndicator color={colors.white} />
          ) : (
            <Text style={styles.renewButtonText}>Renew Policy</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}
