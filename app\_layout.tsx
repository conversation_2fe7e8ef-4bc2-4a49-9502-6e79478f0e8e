import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFonts } from 'expo-font';
import { Inter_400Regular, Inter_500Medium, Inter_700Bold } from '@expo-google-fonts/inter';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import * as SplashScreen from 'expo-splash-screen';
import { ThemeProvider, useTheme } from '@/context/ThemeContext';
import { Provider } from 'react-redux';
import { store } from '@/store/store';
import { useAppDispatch } from '@/store/hooks';
import { initializeAuth } from '@/store/authSlice';
import Toast from 'react-native-toast-message';
import { useToastConfig } from '@/components/ui/ToastConfig';
import { DocumentUploadProvider } from '@/context/DocumentUploadContext';
import EnhancedNotificationManager from '@/components/notifications/EnhancedNotificationManager';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useFrameworkReady();

  const [fontsLoaded, fontError] = useFonts({
    'Inter-Regular': Inter_400Regular,
    'Inter-Medium': Inter_500Medium,
    'Inter-Bold': Inter_700Bold,
  });

  // Hide splash screen once fonts are loaded
  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // Fonts loading is now handled in AppContent

  return (
    <Provider store={store}>
      <ThemeProvider>
        <DocumentUploadProvider>
          <AppContent fontsLoaded={fontsLoaded} fontError={fontError} />
        </DocumentUploadProvider>
      </ThemeProvider>
    </Provider>
  );
}

function AppContent({ fontsLoaded, fontError }: { fontsLoaded: boolean; fontError: Error | null }) {
  const { isDarkMode } = useTheme();
  const theme = createTheme(isDarkMode);
  const dispatch = useAppDispatch();
  // Always call hooks at the top level, even if we don't use the result immediately
  const toastConfig = useToastConfig();

  // Initialize auth store
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  // Return null to keep splash screen visible while fonts load
  if (!fontsLoaded && !fontError) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary[500]} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading Inerca...</Text>
      </View>
    );
  }

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" options={{ animation: 'fade' }} />
        <Stack.Screen name="(onboarding)" options={{ animation: 'fade' }} />
        <Stack.Screen name="(auth)" options={{ animation: 'fade' }} />
        <Stack.Screen name="(app)" options={{ animation: 'fade' }} />
        <Stack.Screen name="+not-found" options={{ title: 'Oops!' }} />
      </Stack>
      <StatusBar style={isDarkMode ? "light" : "dark"} />
      <Toast config={toastConfig} />
      <EnhancedNotificationManager />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
});