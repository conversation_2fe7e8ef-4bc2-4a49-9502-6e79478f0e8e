import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Switch, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createTheme } from '@/constants/theme';
import { useState, useEffect } from 'react';
import {
  ChevronRight,
  FileText,
  Shield,
  Wallet,
  Settings,
  Share2,
  Headphones,
  Info,
  LogOut,
  Bell,
  Globe,
  Fingerprint,
  FileCheck,
  Wifi,
  Terminal,
  Code,
  MessageSquare
} from 'lucide-react-native';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/context/ThemeContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { logout, toggleBiometric } from '@/store/authSlice';
import ThemeToggle from '@/components/ui/ThemeToggle';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import ApiConnectionTest from '@/components/settings/ApiConnectionTest';
import { checkApiHealth } from '@/utils/apiHealthCheck';

// Define the menu item type
type MenuItem = {
  id: string;
  title: string;
  icon: any;
  color: string;
  route?: string;
};

// Define the section type
type MenuSection = {
  title: string;
  items: MenuItem[];
};

// Define styles with proper TypeScript types
const getStyles = (typography: any, spacing: any, borders: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerTitle: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.xl,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: spacing.lg,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes.sm,
    textTransform: 'uppercase',
    letterSpacing: 1,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionContent: {
    borderRadius: borders.radius.lg,
    marginHorizontal: spacing.md,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150, 150, 150, 0.1)',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: borders.radius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  menuItemTitle: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.md,
  },
  appInfo: {
    alignItems: 'center',
    marginVertical: spacing.xl,
    paddingBottom: spacing.md,
  },
  versionText: {
    fontFamily: typography.fonts.medium,
    fontSize: typography.sizes.md,
    marginBottom: spacing.xs,
  },
  versionDetails: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.sm,
  },
});

export default function SettingsScreen() {
  const { isDarkMode, theme } = useTheme();
  const { colors, typography, spacing, borders } = createTheme(isDarkMode);

  // Get auth state and actions from Redux store
  const dispatch = useAppDispatch();
  const {
    isBiometricEnabled,
    isBiometricAvailable,
    user
  } = useAppSelector(state => state.auth);

  // Create styles with the current theme
  const styles = getStyles(typography, spacing, borders);

  // State for settings
  const [selectedLanguage, setSelectedLanguage] = useState('English');
  const [isApiConnected, setIsApiConnected] = useState<boolean | null>(null);

  // Check API connection on mount
  useEffect(() => {
    const checkConnection = async () => {
      const isConnected = await checkApiHealth();
      setIsApiConnected(isConnected);
    };

    checkConnection();
  }, []);

  // Handle API connection change
  const handleConnectionChange = (isConnected: boolean) => {
    setIsApiConnected(isConnected);
  };

  // Handle logout
  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: async () => {
            console.log('User confirmed logout');
            await dispatch(logout());
          },
          style: 'destructive',
        },
      ]
    );
  };

  // Handle biometric toggle
  const handleBiometricToggle = async (value: boolean) => {
    if (!isBiometricAvailable && value) {
      Alert.alert(
        'Biometric Authentication',
        'Biometric authentication is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }

    await dispatch(toggleBiometric(value));
  };

  // Handle language selection
  const handleLanguageSelect = () => {
    Alert.alert(
      'Select Language',
      'Choose your preferred language',
      [
        {
          text: 'English',
          onPress: () => setSelectedLanguage('English'),
        },
        {
          text: 'Setswana (Coming Soon)',
          onPress: () => {
            Alert.alert('Coming Soon', 'Setswana language support will be available in a future update.');
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  // Define menu items with the current theme colors
  const menuItems: MenuSection[] = [
    {
      title: 'Insurance',
      items: [
        { id: 'quotes', title: 'Get a Quote', icon: Shield, color: colors.primary[300], route: '/quotes' },
        { id: 'policies', title: 'My Policies', icon: FileText, color: colors.primary[500] },
        { id: 'payments', title: 'Payments', icon: Wallet, color: colors.success[500] },
        { id: 'documents', title: 'Document Management', icon: FileText, color: colors.primary[500], route: '/documents' },
        { id: 'verification', title: 'Document Verification', icon: FileCheck, color: colors.success[500], route: '/verification' },
        { id: 'policy-docs', title: 'Policy Documents', icon: FileText, color: colors.lightPurple, route: '/documents/policies' },
      ]
    },
    {
      title: 'Support',
      items: [
        { id: 'support', title: 'Support Center', icon: Headphones, color: colors.secondary[500], route: '/support' },
        { id: 'contact', title: 'Contact Us', icon: Headphones, color: colors.secondary[500], route: '/support/contact' },
        { id: 'faq', title: 'FAQ', icon: Info, color: colors.secondary[500], route: '/support/faq' },
        { id: 'chat', title: 'Live Chat', icon: MessageSquare, color: colors.primary[500], route: '/support/chat' },
      ]
    },
    {
      title: 'Other',
      items: [
        { id: 'refer', title: 'Refer a Friend', icon: Share2, color: colors.primary[500] },
      ]
    },
    {
      title: 'Developer',
      items: [
        { id: 'api-test', title: 'API Integration Test', icon: Code, color: colors.primary[500], route: '/debug/api-test' },
      ]
    },
  ];

  // Render menu item
  const renderMenuItem = (item: MenuItem) => {
    // Handle special actions for specific menu items
    const handlePress = () => {
      console.log(`Pressed ${item.title}`);

      // If the item has a route, navigate to it
      if (item.route) {
        // @ts-ignore - Route path is valid but TypeScript doesn't recognize it
        router.push(item.route);
        return;
      }

      switch (item.id) {
        case 'logout':
          handleLogout();
          break;
        default:
          // For other menu items, just log the press
          console.log(`Pressed ${item.title}`);
      }
    };

    return (
      <TouchableOpacity
        key={item.id}
        style={styles.menuItem}
        onPress={handlePress}
      >
        <View style={styles.menuItemLeft}>
          <View style={[styles.iconContainer, { backgroundColor: `${item.color}20` }]}>
            <item.icon size={20} color={item.color} />
          </View>
          <Text style={[styles.menuItemTitle, { color: colors.text }]}>{item.title}</Text>
        </View>
        <ChevronRight size={20} color={colors.textSecondary} />
      </TouchableOpacity>
    );
  };

  // Render toggle item
  const renderToggleItem = ({ icon: Icon, title, color, value, onToggle }: {
    icon: any;
    title: string;
    color: string;
    value: boolean;
    onToggle: (value: boolean) => void;
  }) => {
    return (
      <View style={styles.menuItem}>
        <View style={styles.menuItemLeft}>
          <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
            <Icon size={20} color={color} />
          </View>
          <Text style={[styles.menuItemTitle, { color: colors.text }]}>{title}</Text>
        </View>
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: colors.border, true: colors.primary[500] }}
          thumbColor={colors.white}
        />
      </View>
    );
  };

  // Render selection item
  const renderSelectionItem = ({ icon: Icon, title, color, value, onPress }: {
    icon: any;
    title: string;
    color: string;
    value: string;
    onPress: () => void;
  }) => {
    return (
      <TouchableOpacity style={styles.menuItem} onPress={onPress}>
        <View style={styles.menuItemLeft}>
          <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
            <Icon size={20} color={color} />
          </View>
          <Text style={[styles.menuItemTitle, { color: colors.text }]}>{title}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={{ color: colors.textSecondary, marginRight: 8, fontSize: 14 }}>
            {value}
          </Text>
          <ChevronRight size={20} color={colors.textSecondary} />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <StatusBar style={isDarkMode ? "light" : "dark"} />
      <View style={[
        styles.header,
        {
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
          borderBottomWidth: 1
        }
      ]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Settings</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Menu Sections */}
        {menuItems.map((section, index) => (
          <View key={index} style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>{section.title}</Text>
            <View style={[styles.sectionContent, { backgroundColor: colors.card }]}>
              {section.items.map(renderMenuItem)}
            </View>
          </View>
        ))}

        {/* App Settings Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>App Settings</Text>
          <View style={[styles.sectionContent, { backgroundColor: colors.card }]}>
            {/* Notifications */}
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                // @ts-ignore - Route path is valid but TypeScript doesn't recognize it
                router.push('/notifications/preferences');
              }}
            >
              <View style={styles.menuItemLeft}>
                <View style={[styles.iconContainer, { backgroundColor: `${colors.primary[500]}20` }]}>
                  <Bell size={20} color={colors.primary[500]} />
                </View>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Notification Settings</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>

            {/* Language */}
            {renderSelectionItem({
              icon: Globe,
              title: 'Language',
              color: colors.secondary[500],
              value: selectedLanguage,
              onPress: handleLanguageSelect,
            })}

            {/* Biometric Authentication */}
            {isBiometricAvailable && renderToggleItem({
              icon: Fingerprint,
              title: 'Biometric Authentication',
              color: colors.primary[500],
              value: isBiometricEnabled,
              onToggle: handleBiometricToggle,
            })}
          </View>
        </View>

        {/* API Connection Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Backend Connection</Text>
          <ApiConnectionTest onConnectionChange={handleConnectionChange} />
        </View>

        {/* Theme Toggle Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Appearance</Text>
          <View style={[styles.sectionContent, { backgroundColor: colors.card }]}>
            <View style={styles.menuItem}>
              <View style={styles.menuItemLeft}>
                <View style={[styles.iconContainer, { backgroundColor: `${colors.primary[500]}20` }]}>
                  <Settings size={20} color={colors.primary[500]} />
                </View>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Theme</Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <ThemeToggle />
              </View>
            </View>
          </View>
        </View>

        {/* Terms & Privacy Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Legal</Text>
          <View style={[styles.sectionContent, { backgroundColor: colors.card }]}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                // @ts-ignore - Route path is valid but TypeScript doesn't recognize it
                router.push('/legal/terms');
              }}
            >
              <View style={styles.menuItemLeft}>
                <View style={[styles.iconContainer, { backgroundColor: `${colors.textSecondary}20` }]}>
                  <FileText size={20} color={colors.textSecondary} />
                </View>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Terms & Conditions</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                // @ts-ignore - Route path is valid but TypeScript doesn't recognize it
                router.push('/legal/privacy');
              }}
            >
              <View style={styles.menuItemLeft}>
                <View style={[styles.iconContainer, { backgroundColor: `${colors.textSecondary}20` }]}>
                  <FileText size={20} color={colors.textSecondary} />
                </View>
                <Text style={[styles.menuItemTitle, { color: colors.text }]}>Privacy Policy</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>

            {/* Legal Management (Admin Only) */}
            {user?.role === 'admin' && (
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => {
                  // @ts-ignore - Route path is valid but TypeScript doesn't recognize it
                  router.push('/legal/manage');
                }}
              >
                <View style={styles.menuItemLeft}>
                  <View style={[styles.iconContainer, { backgroundColor: `${colors.primary[500]}20` }]}>
                    <Settings size={20} color={colors.primary[500]} />
                  </View>
                  <Text style={[styles.menuItemTitle, { color: colors.text }]}>Legal Document Management</Text>
                </View>
                <ChevronRight size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Logout Button */}
        <View style={styles.section}>
          <View style={[styles.sectionContent, { backgroundColor: colors.card }]}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleLogout}
            >
              <View style={styles.menuItemLeft}>
                <View style={[styles.iconContainer, { backgroundColor: `${colors.error[500]}20` }]}>
                  <LogOut size={20} color={colors.error[500]} />
                </View>
                <Text style={[styles.menuItemTitle, { color: colors.error[500] }]}>Logout</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.appInfo}>
          <Text style={[styles.versionText, { color: colors.text }]}>INERCA</Text>
          <Text style={[styles.versionDetails, { color: colors.textSecondary }]}>
             v{Constants.expoConfig?.version || '1.0.0'}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
