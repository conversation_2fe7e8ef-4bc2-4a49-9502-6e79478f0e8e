import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Check, Plus } from 'lucide-react-native';
import { InsuranceExtra } from '@/types/quote.types';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { formatCurrency } from '@/utils/quoteCalculations';

interface OptionalExtrasSelectorProps {
  extras: InsuranceExtra[];
  onToggleExtra: (extraId: string, selected: boolean) => void;
  currency?: string;
}

const OptionalExtrasSelector: React.FC<OptionalExtrasSelectorProps> = ({
  extras,
  onToggleExtra,
  currency = 'P',
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // We're using the imported formatCurrency function from utils/quoteCalculations

  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    extraItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.sm,
      borderLeftWidth: 3,
    },
    extraContent: {
      flex: 1,
    },
    extraName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: 2,
    },
    extraDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    extraPrice: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
    },
    toggleButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: 32,
      height: 32,
      borderRadius: 16,
      marginLeft: spacing.md,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.md,
    },
  });

  const renderExtraItem = ({ item, index }: { item: InsuranceExtra; index: number }) => {
    const isSelected = item.selected;
    const borderColor = isSelected ? colors.primary[500] : colors.border;
    const backgroundColor = isSelected ? colors.primary[500] : colors.card;

    return (
      <Animated.View
        entering={FadeInDown.delay(100 + index * 50).springify()}
      >
        <TouchableOpacity
          style={[styles.extraItem, { borderLeftColor: borderColor }]}
          onPress={() => onToggleExtra(item.id, !isSelected)}
          activeOpacity={0.7}
        >
          <View style={styles.extraContent}>
            <Text style={styles.extraName}>{item.name}</Text>
            <Text style={styles.extraDescription}>{item.description}</Text>
            <Text style={styles.extraPrice}>{formatCurrency(item.price, currency)}</Text>
          </View>

          <View
            style={[
              styles.toggleButton,
              { backgroundColor: isSelected ? colors.primary[500] : `${colors.primary[500]}15` },
            ]}
          >
            {isSelected ? (
              <Check size={16} color={colors.white} />
            ) : (
              <Plus size={16} color={colors.primary[500]} />
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Plus size={48} color={colors.textSecondary} />
      <Text style={styles.emptyText}>No optional extras available for this quote</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Optional Extras</Text>

      {extras.length > 0 ? (
        <FlatList
          data={extras}
          renderItem={renderExtraItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
        />
      ) : (
        renderEmptyState()
      )}
    </View>
  );
};

export default OptionalExtrasSelector;
