import 'dotenv/config';

export default {
  name: 'INERCA',
  slug: 'inerca-insurance-app',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/images/logo.png',
  userInterfaceStyle: 'automatic',
  splash: {
    image: './assets/images/logo.png',
    resizeMode: 'contain',
    backgroundColor: '#ffffff'
  },
  assetBundlePatterns: [
    '**/*'
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.inerca.app'
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/images/logo.png',
      backgroundColor: '#ffffff'
    },
    package: 'com.qazzy.inercainsuranceapp'
  },
  web: {
    favicon: './assets/images/logo.png'
  },
  owner: 'qazzy',
  extra: {
    apiBaseUrl: process.env.API_BASE_URL,
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    googleAndroidClientId: process.env.GOOGLE_ANDROID_CLIENT_ID,
    googleIosClientId: process.env.GOOGLE_IOS_CLIENT_ID,
    eas: {
      projectId: '3ff25f9d-4952-424c-b5a0-ff38b2e42493'
    }
  },
  plugins: [
    'expo-router',
    [
      'expo-document-picker',
      {
        iCloudContainerEnvironment: 'Production'
      }
    ],
    [
      'expo-camera',
      {
        cameraPermission: 'Allow Inerca to access your camera to take photos of documents.'
      }
    ],
    [
      'expo-image-picker',
      {
        photosPermission: 'Allow Inerca to access your photos to upload documents.'
      }
    ],
    [
      'expo-notifications',
      {
        icon: './assets/images/logo.png',
        color: '#ffffff'
      }
    ]
  ]
};
