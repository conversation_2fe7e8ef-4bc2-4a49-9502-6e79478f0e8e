import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  Bell,
  CheckCircle,
  Info,
  AlertTriangle,
  AlertCircle,
  FileText,
  Trash2
} from 'lucide-react-native';
import { Notification } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface NotificationItemProps {
  notification: Notification;
  onPress: (notification: Notification) => void;
  onDelete: (id: string) => void;
  index: number;
}

export default function NotificationItem({
  notification,
  onPress,
  onDelete,
  index
}: NotificationItemProps) {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info size={24} color={colors.info[500]} />;
      case 'success':
        return <CheckCircle size={24} color={colors.success[500]} />;
      case 'warning':
        return <AlertTriangle size={24} color={colors.warning[500]} />;
      case 'error':
        return <AlertCircle size={24} color={colors.error[500]} />;
      case 'policy':
        return <FileText size={24} color={colors.primary[500]} />;
      case 'claim':
        return <FileText size={24} color={colors.secondary[500]} />;
      case 'application':
        return <FileText size={24} color={colors.tertiary[500]} />;
      case 'document':
        return <FileText size={24} color={colors.neutral[500]} />;
      default:
        return <Bell size={24} color={colors.primary[500]} />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Create styles with the current theme
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: notification.read ? colors.card : `${colors.primary[500]}10`,
    },
    content: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    icon: {
      marginRight: spacing.md,
    },
    textContainer: {
      flex: 1,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    message: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.xs,
    },
    time: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textTertiary,
    },
    deleteButton: {
      padding: spacing.sm,
    },
  });

  return (
    <Animated.View
      entering={FadeInDown.delay(index * 100).springify()}
      style={styles.container}
    >
      <TouchableOpacity
        style={styles.content}
        onPress={() => onPress(notification)}
        activeOpacity={0.7}
      >
        <View style={styles.icon}>
          {getNotificationIcon(notification.type)}
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>
            {notification.title}
          </Text>
          <Text style={styles.message}>
            {notification.message}
          </Text>
          <Text style={styles.time}>
            {formatDate(notification.createdAt)}
          </Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => onDelete(notification.id)}
      >
        <Trash2 size={20} color={colors.error[500]} />
      </TouchableOpacity>
    </Animated.View>
  );
}
