import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { createTheme } from '@/constants/theme';
import { Home, Calendar, DollarSign, Hash, Info, Shield, FileText } from 'lucide-react-native';
import QuoteFormContainer from '@/components/quotes/QuoteFormContainer';
import DynamicFormField, { SelectOption } from '@/components/quotes/DynamicFormField';
import QuoteDocumentsSection from '@/components/quotes/QuoteDocumentsSection';
import useQuoteStore from '@/store/quoteStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { showToast } from '@/utils/toast';
import { QuoteDocument } from '@/types/quote.types';
import { calculateHouseownersPremium } from '@/utils/quoteCalculations';

export default function HouseownersQuoteForm() {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography } = createTheme(isDarkMode);

  // Get params
  const params = useLocalSearchParams();
  const quoteId = params.quoteId as string;

  // Get quote store functions
  const {
    getQuoteById,
    updateQuote,
    setCurrentQuote,
    currentQuote,
    isLoading: isQuoteLoading
  } = useQuoteStore();

  // Form state
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formValid, setFormValid] = useState(false);
  const [isStorageLoading, setIsStorageLoading] = useState(true);
  const [storedQuoteId, setStoredQuoteId] = useState<string | null>(null);
  const [documents, setDocuments] = useState<QuoteDocument[]>([]);
  const [activeTab, setActiveTab] = useState<'details' | 'documents'>('details');

  // First useEffect to check AsyncStorage for stored quote data
  useEffect(() => {
    const checkAsyncStorage = async () => {
      try {
        // Try to get the quoteId from AsyncStorage
        const storedId = await AsyncStorage.getItem('currentQuoteId');

        console.log('HouseownersQuoteForm - Retrieved from AsyncStorage:', storedId);

        if (storedId) setStoredQuoteId(storedId);

        setIsStorageLoading(false);
      } catch (error) {
        console.error('HouseownersQuoteForm - AsyncStorage error:', error);
        setIsStorageLoading(false);
      }
    };

    checkAsyncStorage();
  }, []);

  // Load quote data
  useEffect(() => {
    // Skip if still loading AsyncStorage data
    if (isStorageLoading) return;

    console.log('HouseownersQuoteForm - Params received:', { quoteId });
    console.log('HouseownersQuoteForm - Stored data:', { storedQuoteId });

    // Use quoteId from params or AsyncStorage
    const quoteIdToUse = quoteId || storedQuoteId;

    if (!quoteIdToUse) {
      console.error('HouseownersQuoteForm - No quoteId found');
      showToast(
        'error',
        'Error',
        'Missing quote ID information',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
      return;
    }

    console.log('HouseownersQuoteForm - Looking up quote with ID:', quoteIdToUse);
    const quote = getQuoteById(quoteIdToUse);

    if (quote) {
      console.log('HouseownersQuoteForm - Quote found:', quote.id, 'Type:', quote.type);
      setCurrentQuote(quote);

      // Initialize form data based on existing quote data
      if (quote.additionalInfo) {
        setFormData(quote.additionalInfo);
      } else {
        // Set default form data
        setFormData({
          propertyAddress: quote.clientInfo.address || '',
          constructionType: '',
          occupancyStatus: 'yes',
          buildingValue: 0,
          yearBuilt: new Date().getFullYear() - 10,
          securityFeatures: [],
          coverType: 'standard',
        });
      }

      // Initialize documents if they exist
      if (quote.documents && quote.documents.length > 0) {
        setDocuments(quote.documents);
      } else {
        // Initialize with required documents based on requirements
        setDocuments([
          {
            id: Date.now().toString() + '1',
            name: 'Copy of Title Deed / Land Board Certificate',
            type: 'title_deed',
            required: true,
            uploaded: false,
          },
          {
            id: Date.now().toString() + '2',
            name: 'Building Valuation Report (not older than 3 years)',
            type: 'valuation_report',
            required: true,
            uploaded: false,
          },
        ]);
      }
    } else {
      showToast(
        'error',
        'Error',
        'Quote not found',
        { visibilityTime: 4000 }
      );
      router.replace('/(app)/quotes');
    }
  }, [quoteId, storedQuoteId, isStorageLoading, getQuoteById, setCurrentQuote]);

  // Validate form when data or documents change
  useEffect(() => {
    validateForm();
  }, [formData, documents]);

  // Update form data
  const updateFormField = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form - memoize to prevent unnecessary re-renders
  const validateForm = React.useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.propertyAddress) {
      newErrors.propertyAddress = 'Property address is required';
    }
    if (!formData.constructionType) {
      newErrors.constructionType = 'Construction type is required';
    }
    if (!formData.buildingValue || formData.buildingValue <= 0) {
      newErrors.buildingValue = 'Building value must be greater than 0';
    }
    if (!formData.yearBuilt) {
      newErrors.yearBuilt = 'Year built is required';
    }

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      newErrors.documents = 'All required documents must be uploaded';
    }

    setErrors(newErrors);
    setFormValid(Object.keys(newErrors).length === 0);
  }, [formData, documents]);


  // Handle back button
  const handleBack = () => {
    // Save the current form data before navigating back
    if (currentQuote && formData) {
      const saveBeforeBack = async () => {
        try {
          console.log('Saving draft before navigating back:', {
            quoteId: currentQuote.id,
            formData
          });

          // Update the quote with the current form data and documents
          await updateQuote({
            additionalInfo: formData,
            documents: documents,
            // Make sure the status is still draft
            status: 'draft',
            // Update the timestamp to ensure it's at the top of the list
            updatedAt: new Date().toISOString().split('T')[0]
          });

          // Get the fetchQuotes function from the store to refresh the quotes list
          // Use getState() to access store methods without hooks
          const storeState = useQuoteStore.getState();
          const fetchQuotes = storeState.fetchQuotes;

          // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
          await fetchQuotes();

          // Show toast notification
          showToast(
            'success',
            'Draft Saved',
            'Your quote draft has been saved',
            { visibilityTime: 2000 }
          );

          // Clear AsyncStorage to prevent issues with future navigation
          try {
            await AsyncStorage.removeItem('currentQuoteId');
            await AsyncStorage.removeItem('currentQuoteType');
            console.log('AsyncStorage cleared');
          } catch (storageError) {
            console.error('Error clearing AsyncStorage:', storageError);
          }

          // Use setTimeout to ensure navigation happens after component is mounted
          setTimeout(() => {
            try {
              // Navigate back to the quotes list
              // Use router.push instead of replace to ensure a fresh load
              router.push('/(app)/quotes' as any);
            } catch (navError) {
              console.error('Navigation error:', navError);
              // Show toast notification
              showToast(
                'error',
                'Navigation Error',
                'Failed to navigate back. Please try again.',
                { visibilityTime: 4000 }
              );
            }
          }, 100);
        } catch (error) {
          console.error('Error saving draft before navigating back:', error);

          // Still navigate back even if save fails
          setTimeout(() => {
            try {
              router.push('/(app)/quotes' as any);
            } catch (navError) {
              console.error('Navigation error:', navError);
            }
          }, 100);
        }
      };

      saveBeforeBack();
    } else {
      // If no current quote, just navigate back
      setTimeout(() => {
        try {
          router.push('/(app)/quotes' as any);
        } catch (navError) {
          console.error('Navigation error:', navError);
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate back. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!currentQuote) return;

    try {
      // Update the quote with the current form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        // Make sure the status is still draft
        status: 'draft',
        // Update the timestamp to ensure it's at the top of the list
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const storeState = useQuoteStore.getState();
      const fetchQuotes = storeState.fetchQuotes;

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Show a toast notification for better user feedback
      showToast(
        'success',
        'Draft Saved',
        'Your quote draft has been saved',
        { visibilityTime: 2000 }
      );
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Handle next
  const handleNext = async () => {
    if (!currentQuote) return;

    // Check if all required documents are uploaded
    const requiredDocumentsUploaded = documents.every(doc => !doc.required || doc.uploaded);
    if (!requiredDocumentsUploaded) {
      showToast(
        'error',
        'Required Documents',
        'Please upload all required documents before proceeding',
        { visibilityTime: 4000 }
      );
      setActiveTab('documents');
      return;
    }

    try {
      // Calculate premium based on building value and year built
      const buildingValue = formData.buildingValue || 0;
      const yearBuilt = formData.yearBuilt || new Date().getFullYear() - 10;

      // Calculate premium using our utility function (assuming it exists)
      const premium = calculateHouseownersPremium(buildingValue, yearBuilt);

      // Save form data and documents
      await updateQuote({
        additionalInfo: formData,
        documents: documents,
        premium: premium,
        currency: 'P', // Set Botswana currency
        coverAmount: buildingValue,
        // Update the timestamp
        updatedAt: new Date().toISOString().split('T')[0]
      });

      // Get the fetchQuotes function from the store to refresh the quotes list
      const storeState = useQuoteStore.getState();
      const fetchQuotes = storeState.fetchQuotes;

      // Refresh the quotes list to ensure the updated quote appears in "Your Quotes"
      await fetchQuotes();

      // Clear AsyncStorage to prevent issues with future navigation
      try {
        await AsyncStorage.removeItem('currentQuoteId');
        await AsyncStorage.removeItem('currentQuoteType');
        console.log('AsyncStorage cleared before navigating to summary');
      } catch (storageError) {
        console.error('Error clearing AsyncStorage:', storageError);
      }

      // Use setTimeout to ensure navigation happens after component is mounted
      setTimeout(() => {
        try {
          // Navigate to the summary page
          router.push({
            pathname: '/(app)/quotes/[type]/summary',
            params: {
              type: 'houseowners',
              quoteId: currentQuote.id
            }
          });
        } catch (navError) {
          console.error('Navigation error:', navError);
          // Show toast notification
          showToast(
            'error',
            'Navigation Error',
            'Failed to navigate to summary page. Please try again.',
            { visibilityTime: 4000 }
          );
        }
      }, 100);
    } catch (error) {
      console.error('Error saving quote details:', error);
      showToast(
        'error',
        'Save Failed',
        'Failed to save quote details. Please try again.',
        { visibilityTime: 4000 }
      );
    }
  };

  // Define form steps
  const steps = [
    { id: '1', title: 'Client Info' },
    { id: '2', title: 'Quote Details' },
    { id: '3', title: 'Review' },
  ];

  // Render houseowners insurance form fields
  const renderFormFields = () => {
    const occupancyStatusOptions: SelectOption[] = [
      { value: 'owner', label: 'Owner Occupied' },
      { value: 'tenant', label: 'Tenant Occupied' },
      { value: 'vacant', label: 'Vacant' },
    ];

    const coverTypeOptions: SelectOption[] = [
      { value: 'standard', label: 'Standard Cover' },
      { value: 'comprehensive', label: 'Comprehensive Cover' },
    ];

    const constructionTypeOptions: SelectOption[] = [
      { value: 'brick', label: 'Brick' },
      { value: 'concrete', label: 'Concrete' },
      { value: 'wood', label: 'Wood' },
      { value: 'steel', label: 'Steel' },
      { value: 'thatched', label: 'Thatched Roof' },
      { value: 'mixed', label: 'Mixed Materials' },
    ];

    const securityFeaturesOptions: SelectOption[] = [
      { value: 'alarm', label: 'Alarm System' },
      { value: 'cctv', label: 'CCTV' },
      { value: 'securityGuard', label: 'Security Guard' },
      { value: 'electricFence', label: 'Electric Fence' },
      { value: 'burglarBars', label: 'Burglar Bars' },
    ];

    return (
      <View>
        <DynamicFormField
          label="Property Address"
          type="textarea"
          value={formData.propertyAddress}
          onChange={(value) => updateFormField('propertyAddress', value)}
          placeholder="Enter property address"
          error={errors.propertyAddress}
          required
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Year of Construction"
          type="number"
          value={formData.yearBuilt?.toString()}
          onChange={(value) => updateFormField('yearBuilt', parseInt(value) || 0)}
          placeholder="e.g., 2000"
          error={errors.yearBuilt}
          required
          keyboardType="numeric"
          icon={<Calendar size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Construction Type"
          type="select"
          value={formData.constructionType}
          onChange={(value) => updateFormField('constructionType', value)}
          options={constructionTypeOptions}
          error={errors.constructionType}
          required
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Is the building occupied?"
          type="select"
          value={formData.occupancyStatus}
          onChange={(value) => updateFormField('occupancyStatus', value)}
          options={[
            { value: 'yes', label: 'Yes' },
            { value: 'no', label: 'No' },
          ]}
          icon={<Home size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Building Value"
          type="number"
          value={formData.buildingValue?.toString()}
          onChange={(value) => updateFormField('buildingValue', parseFloat(value) || 0)}
          placeholder="e.g., 500000"
          error={errors.buildingValue}
          required
          keyboardType="numeric"
          icon={<DollarSign size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Security Features"
          type="select"
          value={formData.securityFeatures}
          onChange={(value) => updateFormField('securityFeatures', value)}
          options={securityFeaturesOptions}
          icon={<Shield size={20} color={colors.textSecondary} />}
        />

        <DynamicFormField
          label="Cover Type"
          type="select"
          value={formData.coverType}
          onChange={(value) => updateFormField('coverType', value)}
          options={coverTypeOptions}
          icon={<Info size={20} color={colors.textSecondary} />}
        />
      </View>
    );
  };

  // Handle document updates
  const handleDocumentsUpdated = (updatedDocuments: QuoteDocument[]) => {
    setDocuments(updatedDocuments);
  };

  // Define styles
  const styles = StyleSheet.create({
    loadingContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    loadingText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      marginTop: spacing.md,
      textAlign: 'center',
    },
    tabsContainer: {
      flexDirection: 'row',
      marginBottom: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    tab: {
      flex: 1,
      paddingVertical: spacing.sm,
      alignItems: 'center',
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: colors.primary[500],
    },
    tabText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
    },
    activeTabText: {
      color: colors.primary[500],
    },
    contentContainer: {
      flex: 1,
    },
  });

  // Show loading indicator while checking AsyncStorage
  if (isStorageLoading || isQuoteLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading quote data...</Text>
      </View>
    );
  }

  return (
    <QuoteFormContainer
      title="Houseowners Insurance Details"
      subtitle="Please provide details for your houseowners insurance quote"
      steps={steps}
      currentStep={1}
      completedSteps={[0]}
      onBack={handleBack}
      onNext={handleNext}
      onSave={handleSave}
      isLoading={isQuoteLoading}
      nextDisabled={!formValid}
    >
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'details' && styles.activeTab
          ]}
          onPress={() => setActiveTab('details')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'details' && styles.activeTabText
          ]}>
            Property Details
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'documents' && styles.activeTab
          ]}
          onPress={() => setActiveTab('documents')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'documents' && styles.activeTabText
          ]}>
            Required Documents
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        {/* Render both components but only show the active one */}
        <View style={{ display: activeTab === 'details' ? 'flex' : 'none' }}>
          {renderFormFields()}
        </View>

        <View style={{ display: activeTab === 'documents' ? 'flex' : 'none' }}>
          <QuoteDocumentsSection
            quoteType="houseowners"
            documents={documents}
            onDocumentsUpdated={handleDocumentsUpdated}
          />
        </View>
      </View>
    </QuoteFormContainer>
  );
}
