import { useEffect } from 'react';
import { View, StyleSheet, Text, Image, Alert } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { createTheme } from '@/constants/theme';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withDelay } from 'react-native-reanimated';
import { useTheme } from '@/context/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { initializeAuth, authenticateWithBiometric } from '@/store/authSlice';
import * as LocalAuthentication from 'expo-local-authentication';

export default function SplashScreen() {
  const { isDarkMode } = useTheme();
  const { colors, spacing } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(spacing, colors);

  // Animation values
  const logoOpacity = useSharedValue(0);
  const logoScale = useSharedValue(0.8);

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: logoOpacity.value,
      transform: [{ scale: logoScale.value }],
    };
  });

  // Initialize auth store
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated);
  const isLoading = useAppSelector(state => state.auth.isLoading);
  const isBiometricEnabled = useAppSelector(state => state.auth.isBiometricEnabled);

  // Function to handle biometric authentication
  const handleBiometricAuth = async () => {
    try {
      // Check if biometric is available and enabled
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();

      if (!compatible || !enrolled) {
        console.log('Biometric not available or not enrolled');
        router.replace('/(app)/(tabs)/');
        return;
      }

      // Dispatch biometric authentication
      const resultAction = await dispatch(authenticateWithBiometric());

      if (authenticateWithBiometric.fulfilled.match(resultAction)) {
        console.log('Biometric authentication successful');
        router.replace('/(app)/(tabs)/');
      } else {
        console.log('Biometric authentication failed, redirecting to login');
        router.replace('/(auth)/login');
      }
    } catch (error) {
      console.error('Biometric error:', error);
      router.replace('/(auth)/login');
    }
  };

  useEffect(() => {
    // Start animations
    logoOpacity.value = withDelay(300, withTiming(1, { duration: 1000 }));
    logoScale.value = withDelay(300, withTiming(1, { duration: 1000 }));

    // Navigate to appropriate screen after delay
    const timer = setTimeout(async () => {
      try {
        // Check if user has completed onboarding before
        const hasCompletedOnboarding = await AsyncStorage.getItem('hasCompletedOnboarding');

        console.log('Splash screen navigation check:');
        console.log('- Has completed onboarding:', !!hasCompletedOnboarding);
        console.log('- Is authenticated:', isAuthenticated);
        console.log('- Is biometric enabled:', isBiometricEnabled);
        console.log('- Auth loading state:', isLoading);

        if (isAuthenticated && !isLoading) {
          if (isBiometricEnabled) {
            // If biometric is enabled, prompt for authentication
            handleBiometricAuth();
          } else {
            // Otherwise, go directly to the app
            router.replace('/(app)/(tabs)/');
          }
        } else if (hasCompletedOnboarding === 'true') {
          router.replace('/(auth)/login');
        } else {
          router.replace('/(onboarding)');
        }
      } catch (error) {
        console.error('Navigation error:', error);
        router.replace('/(onboarding)');
      }
    }, 2500);

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, isBiometricEnabled]);

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={[colors.primary[500], colors.secondary[500]]}
        style={styles.background}
      >
        <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
          <View style={styles.logoImageContainer}>
            {/* <LinearGradient
              colors={[colors.primary[500], colors.secondary[500]]}
              style={styles.logoGradient}
            /> */}
            <Image
              source={require('../assets/images/logo.png')}
              style={styles.logoImageContainer}
              resizeMode="cover"/>
          </View>
          <Text style={styles.logoText}>INERCA</Text>
          <Text style={styles.tagline}>REFINED INSURANCE SOLUTIONS</Text>
        </Animated.View>
      </LinearGradient>
    </View>
  );
}

// Define styles with a function to access theme values
const getStyles = (spacing: any, colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
  },
  logoImageContainer: {
    width: 150,
    height: 150,
    borderRadius: 20,
    marginBottom: spacing.md,
    overflow: 'hidden',
  },
  logoGradient: {
    width: '100%',
    height: '100%',
  },
  logoText: {
    fontSize: 40,
    fontFamily: 'Inter-Bold',
    color: colors.white,
    letterSpacing: 4,
  },
  tagline: {
    marginTop: spacing.sm,
    fontSize: 18,
    fontFamily: 'Inter-Regular',
    color: colors.white,
    opacity: 0.8,
  },
});
function useAuthStore(arg0: (state: any) => any) {
  throw new Error('Function not implemented.');
}




