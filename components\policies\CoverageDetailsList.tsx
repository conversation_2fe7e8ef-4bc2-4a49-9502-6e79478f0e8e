import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, ScrollView } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Shield, ChevronDown, ChevronUp, AlertCircle, Info } from 'lucide-react-native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { CoverageItem } from '@/store/policyStore';
import { formatCurrency } from '@/utils/quoteCalculations';

interface CoverageDetailsListProps {
  coverageItems: CoverageItem[];
  currency?: string;
  totalCoverAmount: number;
  totalPremium: number;
}

const CoverageDetailsList: React.FC<CoverageDetailsListProps> = ({
  coverageItems,
  currency = 'P',
  totalCoverAmount,
  totalPremium,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  // Toggle item expansion
  const toggleItemExpansion = (itemId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // Separate core coverage from add-ons
  const coreCoverage = coverageItems.filter(item => !item.isAddOn);
  const addOns = coverageItems.filter(item => item.isAddOn);

  const styles = StyleSheet.create({
    container: {
      marginVertical: spacing.md,
    },
    summaryCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    summaryHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    summaryIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary[50],
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: spacing.md,
    },
    summaryTitle: {
      ...typography.h3,
      color: colors.text,
      flex: 1,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    summaryLabel: {
      ...typography.body,
      color: colors.textSecondary,
    },
    summaryValue: {
      ...typography.body,
      color: colors.text,
      fontWeight: 'bold',
    },
    sectionTitle: {
      ...typography.h4,
      color: colors.text,
      marginTop: spacing.md,
      marginBottom: spacing.sm,
    },
    coverageCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      marginBottom: spacing.md,
      overflow: 'hidden',
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
        },
        android: {
          elevation: 3,
        },
      }),
    },
    coverageHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    coverageTitle: {
      ...typography.h4,
      color: colors.text,
      flex: 1,
    },
    addOnBadge: {
      backgroundColor: colors.primary[50],
      borderRadius: borders.radius.sm,
      paddingHorizontal: spacing.xs,
      paddingVertical: 2,
      marginRight: spacing.sm,
    },
    addOnText: {
      ...typography.caption,
      color: colors.primary[700],
    },
    coverageAmounts: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    coverageAmount: {
      ...typography.body,
      color: colors.primary[500],
      fontWeight: 'bold',
      marginRight: spacing.sm,
    },
    coverageContent: {
      padding: spacing.md,
    },
    coverageDescription: {
      ...typography.body,
      color: colors.textSecondary,
      marginBottom: spacing.md,
    },
    coverageDetailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.xs,
    },
    coverageDetailLabel: {
      ...typography.body,
      color: colors.textSecondary,
    },
    coverageDetailValue: {
      ...typography.body,
      color: colors.text,
    },
    excessContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.info[50],
      padding: spacing.sm,
      borderRadius: borders.radius.md,
      marginTop: spacing.sm,
    },
    excessIcon: {
      marginRight: spacing.sm,
    },
    excessText: {
      ...typography.body,
      color: colors.info[700],
      flex: 1,
    },
    emptyState: {
      alignItems: 'center',
      padding: spacing.lg,
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
    },
    emptyStateText: {
      ...typography.body,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: spacing.sm,
    },
  });

  return (
    <Animated.View style={styles.container} entering={FadeIn.duration(300)}>
      <View style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <View style={styles.summaryIcon}>
            <Shield size={24} color={colors.primary[500]} />
          </View>
          <Text style={styles.summaryTitle}>Coverage Summary</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Coverage Amount</Text>
          <Text style={styles.summaryValue}>{formatCurrency(totalCoverAmount, currency)}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Premium</Text>
          <Text style={styles.summaryValue}>{formatCurrency(totalPremium, currency)}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Core Coverage Items</Text>
          <Text style={styles.summaryValue}>{coreCoverage.length}</Text>
        </View>

        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Add-ons</Text>
          <Text style={styles.summaryValue}>{addOns.length}</Text>
        </View>
      </View>

      {coreCoverage.length > 0 && (
        <>
          <Text style={styles.sectionTitle}>Core Coverage</Text>
          {coreCoverage.map(item => (
            <View key={item.id} style={styles.coverageCard}>
              <TouchableOpacity
                style={styles.coverageHeader}
                onPress={() => toggleItemExpansion(item.id)}
                activeOpacity={0.7}
              >
                <Text style={styles.coverageTitle}>{item.name}</Text>
                <Text style={styles.coverageAmount}>
                  {formatCurrency(item.coverAmount, currency)}
                </Text>
                {expandedItems[item.id] ? (
                  <ChevronUp size={20} color={colors.textSecondary} />
                ) : (
                  <ChevronDown size={20} color={colors.textSecondary} />
                )}
              </TouchableOpacity>

              {expandedItems[item.id] && (
                <View style={styles.coverageContent}>
                  {item.description && (
                    <Text style={styles.coverageDescription}>{item.description}</Text>
                  )}

                  <View style={styles.coverageDetailRow}>
                    <Text style={styles.coverageDetailLabel}>Coverage Amount</Text>
                    <Text style={styles.coverageDetailValue}>
                      {formatCurrency(item.coverAmount, currency)}
                    </Text>
                  </View>

                  <View style={styles.coverageDetailRow}>
                    <Text style={styles.coverageDetailLabel}>Premium Contribution</Text>
                    <Text style={styles.coverageDetailValue}>
                      {formatCurrency(item.premium, currency)}
                    </Text>
                  </View>

                  {item.excess !== undefined && item.excess > 0 && (
                    <View style={styles.excessContainer}>
                      <Info size={16} color={colors.info[700]} style={styles.excessIcon} />
                      <Text style={styles.excessText}>
                        Excess: {formatCurrency(item.excess, currency)}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          ))}
        </>
      )}

      {addOns.length > 0 && (
        <>
          <Text style={styles.sectionTitle}>Add-ons</Text>
          {addOns.map(item => (
            <View key={item.id} style={styles.coverageCard}>
              <TouchableOpacity
                style={styles.coverageHeader}
                onPress={() => toggleItemExpansion(item.id)}
                activeOpacity={0.7}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                  <View style={styles.addOnBadge}>
                    <Text style={styles.addOnText}>Add-on</Text>
                  </View>
                  <Text style={styles.coverageTitle}>{item.name}</Text>
                </View>
                <Text style={styles.coverageAmount}>
                  {formatCurrency(item.premium, currency)}
                </Text>
                {expandedItems[item.id] ? (
                  <ChevronUp size={20} color={colors.textSecondary} />
                ) : (
                  <ChevronDown size={20} color={colors.textSecondary} />
                )}
              </TouchableOpacity>

              {expandedItems[item.id] && (
                <View style={styles.coverageContent}>
                  {item.description && (
                    <Text style={styles.coverageDescription}>{item.description}</Text>
                  )}

                  <View style={styles.coverageDetailRow}>
                    <Text style={styles.coverageDetailLabel}>Coverage Amount</Text>
                    <Text style={styles.coverageDetailValue}>
                      {formatCurrency(item.coverAmount, currency)}
                    </Text>
                  </View>

                  <View style={styles.coverageDetailRow}>
                    <Text style={styles.coverageDetailLabel}>Premium</Text>
                    <Text style={styles.coverageDetailValue}>
                      {formatCurrency(item.premium, currency)}
                    </Text>
                  </View>

                  {item.excess !== undefined && item.excess > 0 && (
                    <View style={styles.excessContainer}>
                      <Info size={16} color={colors.info[700]} style={styles.excessIcon} />
                      <Text style={styles.excessText}>
                        Excess: {formatCurrency(item.excess, currency)}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          ))}
        </>
      )}

      {coverageItems.length === 0 && (
        <View style={styles.emptyState}>
          <AlertCircle size={48} color={colors.textSecondary} />
          <Text style={styles.emptyStateText}>
            No coverage details available for this policy.
          </Text>
        </View>
      )}
    </Animated.View>
  );
};

export default CoverageDetailsList;
