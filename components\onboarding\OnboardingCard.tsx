import { View, StyleSheet, Text, Image, Dimensions } from 'react-native';
import { createTheme } from '@/constants/theme';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useTheme } from '@/context/ThemeContext';

const { width } = Dimensions.get('window');

type OnboardingCardProps = {
  data: {
    id: string;
    title: string;
    description: string;
    imageUrl: string;
  };
};

export default function OnboardingCard({ data }: OnboardingCardProps) {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // Create styles with the current theme
  const styles = getStyles(colors, spacing, typography, borders, width);

  return (
    <Animated.View
      style={styles.container}
      entering={FadeIn.delay(300).duration(1000)}
    >
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: data.imageUrl }}
          style={styles.image}
          resizeMode="cover"
        />
      </View>

      <View style={styles.contentContainer}>
        <Text style={[styles.title, { color: colors.text }]}>{data.title}</Text>
        <Text style={[styles.description, { color: colors.textSecondary }]}>{data.description}</Text>
      </View>
    </Animated.View>
  );
}

const getStyles = (colors, spacing, typography, borders, screenWidth) => StyleSheet.create({
  container: {
    width: screenWidth,
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  imageContainer: {
    width: '100%',
    height: 300,
    borderRadius: borders.radius.lg,
    overflow: 'hidden',
    marginBottom: spacing.xl,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.md,
  },
  title: {
    fontFamily: typography.fonts.bold,
    fontSize: typography.sizes['2xl'],
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  description: {
    fontFamily: typography.fonts.regular,
    fontSize: typography.sizes.md,
    textAlign: 'center',
    lineHeight: typography.lineHeights.normal * typography.sizes.md,
  },
});