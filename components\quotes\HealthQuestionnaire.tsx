import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Heart, Check, X, AlertCircle, Plus, Trash2 } from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import DynamicForm<PERSON>ield from './DynamicFormField';
import { showToast } from '@/utils/toast';

export interface HealthCondition {
  id: string;
  condition: string;
  severity: 'minor' | 'moderate' | 'severe' | 'critical';
  details?: string;
}

interface HealthQuestionnaireProps {
  healthConditions: HealthCondition[];
  onHealthConditionsUpdated: (conditions: HealthCondition[]) => void;
  onRiskLevelUpdated: (riskLevel: string) => void;
}

const HealthQuestionnaire: React.FC<HealthQuestionnaireProps> = ({
  healthConditions,
  onHealthConditionsUpdated,
  onRiskLevelUpdated,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  
  const [isAddingCondition, setIsAddingCondition] = useState(false);
  const [editingConditionId, setEditingConditionId] = useState<string | null>(null);
  
  // Form state for new/editing condition
  const [condition, setCondition] = useState('');
  const [severity, setSeverity] = useState<'minor' | 'moderate' | 'severe' | 'critical'>('minor');
  const [details, setDetails] = useState('');
  
  // Form errors
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Health questions
  const [hasHeartDisease, setHasHeartDisease] = useState(false);
  const [hasDiabetes, setHasDiabetes] = useState(false);
  const [hasHighBloodPressure, setHasHighBloodPressure] = useState(false);
  const [hasCancer, setHasCancer] = useState(false);
  const [isCurrentSmoker, setIsCurrentSmoker] = useState(false);
  
  // Update risk level when health conditions change
  useEffect(() => {
    // Determine the highest risk level
    let highestRisk = 'none';
    
    if (healthConditions.length > 0) {
      const riskLevels = ['minor', 'moderate', 'severe', 'critical'];
      const riskIndices = healthConditions.map(c => riskLevels.indexOf(c.severity));
      const maxRiskIndex = Math.max(...riskIndices);
      highestRisk = riskLevels[maxRiskIndex];
    }
    
    // Update the risk level
    onRiskLevelUpdated(highestRisk);
  }, [healthConditions, onRiskLevelUpdated]);
  
  // Reset form
  const resetForm = () => {
    setCondition('');
    setSeverity('minor');
    setDetails('');
    setErrors({});
  };
  
  // Start adding a new condition
  const handleAddCondition = () => {
    resetForm();
    setIsAddingCondition(true);
    setEditingConditionId(null);
  };
  
  // Start editing an existing condition
  const handleEditCondition = (condition: HealthCondition) => {
    setCondition(condition.condition);
    setSeverity(condition.severity);
    setDetails(condition.details || '');
    setEditingConditionId(condition.id);
    setIsAddingCondition(true);
  };
  
  // Delete a condition
  const handleDeleteCondition = (id: string) => {
    const updatedConditions = healthConditions.filter(c => c.id !== id);
    onHealthConditionsUpdated(updatedConditions);
    
    showToast(
      'success',
      'Condition Removed',
      'Health condition has been removed successfully',
      { visibilityTime: 3000 }
    );
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!condition.trim()) {
      newErrors.condition = 'Condition name is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Save condition
  const handleSaveCondition = () => {
    if (!validateForm()) {
      return;
    }
    
    const healthCondition: HealthCondition = {
      id: editingConditionId || Date.now().toString(),
      condition,
      severity,
      details: details || undefined,
    };
    
    let updatedConditions: HealthCondition[];
    
    if (editingConditionId) {
      // Update existing condition
      updatedConditions = healthConditions.map(c => 
        c.id === editingConditionId ? healthCondition : c
      );
      
      showToast(
        'success',
        'Condition Updated',
        'Health condition has been updated successfully',
        { visibilityTime: 3000 }
      );
    } else {
      // Add new condition
      updatedConditions = [...healthConditions, healthCondition];
      
      showToast(
        'success',
        'Condition Added',
        'Health condition has been added successfully',
        { visibilityTime: 3000 }
      );
    }
    
    onHealthConditionsUpdated(updatedConditions);
    setIsAddingCondition(false);
    resetForm();
  };
  
  // Cancel adding/editing
  const handleCancel = () => {
    setIsAddingCondition(false);
    resetForm();
  };
  
  // Add predefined condition
  const addPredefinedCondition = (conditionName: string, conditionSeverity: 'minor' | 'moderate' | 'severe' | 'critical') => {
    // Check if condition already exists
    const exists = healthConditions.some(c => 
      c.condition.toLowerCase() === conditionName.toLowerCase()
    );
    
    if (exists) {
      showToast(
        'info',
        'Condition Exists',
        'This health condition is already in your list',
        { visibilityTime: 3000 }
      );
      return;
    }
    
    const newCondition: HealthCondition = {
      id: Date.now().toString(),
      condition: conditionName,
      severity: conditionSeverity,
    };
    
    onHealthConditionsUpdated([...healthConditions, newCondition]);
    
    showToast(
      'success',
      'Condition Added',
      'Health condition has been added successfully',
      { visibilityTime: 3000 }
    );
  };
  
  // Handle health question responses
  useEffect(() => {
    // Add conditions based on questionnaire responses
    const newConditions: HealthCondition[] = [];
    
    if (hasHeartDisease) {
      newConditions.push({
        id: 'heart-disease',
        condition: 'Heart Disease',
        severity: 'severe',
      });
    }
    
    if (hasDiabetes) {
      newConditions.push({
        id: 'diabetes',
        condition: 'Diabetes',
        severity: 'moderate',
      });
    }
    
    if (hasHighBloodPressure) {
      newConditions.push({
        id: 'high-blood-pressure',
        condition: 'High Blood Pressure',
        severity: 'minor',
      });
    }
    
    if (hasCancer) {
      newConditions.push({
        id: 'cancer',
        condition: 'Cancer',
        severity: 'critical',
      });
    }
    
    // Only update if there are new conditions to add
    if (newConditions.length > 0) {
      // Filter out any existing conditions with the same IDs
      const existingIds = healthConditions.map(c => c.id);
      const filteredNewConditions = newConditions.filter(c => !existingIds.includes(c.id));
      
      if (filteredNewConditions.length > 0) {
        onHealthConditionsUpdated([...healthConditions, ...filteredNewConditions]);
      }
    }
  }, [hasHeartDisease, hasDiabetes, hasHighBloodPressure, hasCancer]);
  
  // Render condition form
  const renderConditionForm = () => {
    const severityOptions = [
      { value: 'minor', label: 'Minor' },
      { value: 'moderate', label: 'Moderate' },
      { value: 'severe', label: 'Severe' },
      { value: 'critical', label: 'Critical' },
    ];
    
    return (
      <Animated.View 
        style={styles.formContainer}
        entering={FadeInDown.delay(100).springify()}
      >
        <Text style={[styles.formTitle, { color: colors.text }]}>
          {editingConditionId ? 'Edit Health Condition' : 'Add Health Condition'}
        </Text>
        
        <DynamicFormField
          label="Condition"
          type="text"
          value={condition}
          onChange={setCondition}
          placeholder="Enter condition name"
          error={errors.condition}
          required
          icon={<Heart size={20} color={colors.textSecondary} />}
        />
        
        <DynamicFormField
          label="Severity"
          type="select"
          value={severity}
          onChange={(value) => setSeverity(value as any)}
          options={severityOptions}
          icon={<AlertCircle size={20} color={colors.textSecondary} />}
        />
        
        <DynamicFormField
          label="Additional Details"
          type="textarea"
          value={details}
          onChange={setDetails}
          placeholder="Enter any additional details (optional)"
          multiline={true}
          numberOfLines={3}
        />
        
        <View style={styles.formActions}>
          <TouchableOpacity
            style={[styles.cancelButton, { backgroundColor: colors.neutral[200] }]}
            onPress={handleCancel}
          >
            <Text style={[styles.buttonText, { color: colors.text }]}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary[500] }]}
            onPress={handleSaveCondition}
          >
            <Text style={[styles.buttonText, { color: colors.white }]}>Save</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };
  
  // Render health question
  const renderHealthQuestion = (
    question: string,
    value: boolean,
    onChange: (value: boolean) => void
  ) => {
    return (
      <View style={styles.questionContainer}>
        <Text style={[styles.questionText, { color: colors.text }]}>{question}</Text>
        <View style={styles.questionButtons}>
          <TouchableOpacity
            style={[
              styles.answerButton,
              value === true && { backgroundColor: colors.error[100] }
            ]}
            onPress={() => onChange(true)}
          >
            <Text style={[
              styles.answerText,
              value === true && { color: colors.error[700] }
            ]}>Yes</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.answerButton,
              value === false && { backgroundColor: colors.success[100] }
            ]}
            onPress={() => onChange(false)}
          >
            <Text style={[
              styles.answerText,
              value === false && { color: colors.success[700] }
            ]}>No</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  // Render condition item
  const renderConditionItem = (condition: HealthCondition, index: number) => {
    const getSeverityColor = (severity: string) => {
      switch (severity) {
        case 'minor': return colors.success[500];
        case 'moderate': return colors.warning[500];
        case 'severe': return colors.error[500];
        case 'critical': return colors.error[700];
        default: return colors.textSecondary;
      }
    };
    
    return (
      <Animated.View 
        key={condition.id}
        style={[
          styles.conditionItem, 
          { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }
        ]}
        entering={FadeInDown.delay(index * 100).springify()}
      >
        <View style={styles.conditionInfo}>
          <Text style={[styles.conditionName, { color: colors.text }]}>
            {condition.condition}
          </Text>
          <Text style={[
            styles.conditionSeverity, 
            { color: getSeverityColor(condition.severity) }
          ]}>
            {condition.severity.charAt(0).toUpperCase() + condition.severity.slice(1)}
          </Text>
          {condition.details && (
            <Text style={[styles.conditionDetails, { color: colors.textSecondary }]}>
              {condition.details}
            </Text>
          )}
        </View>
        
        <View style={styles.conditionActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary[50] }]}
            onPress={() => handleEditCondition(condition)}
          >
            <Plus size={16} color={colors.primary[500]} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.error[50] }]}
            onPress={() => handleDeleteCondition(condition.id)}
          >
            <Trash2 size={16} color={colors.error[500]} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };
  
  return (
    <View style={styles.container}>
      {!isAddingCondition && (
        <>
          <Text style={[styles.title, { color: colors.text }]}>Health Questionnaire</Text>
          
          <View style={styles.questionsSection}>
            {renderHealthQuestion(
              'Have you ever been diagnosed with heart disease?',
              hasHeartDisease,
              setHasHeartDisease
            )}
            
            {renderHealthQuestion(
              'Do you have diabetes?',
              hasDiabetes,
              setHasDiabetes
            )}
            
            {renderHealthQuestion(
              'Do you have high blood pressure?',
              hasHighBloodPressure,
              setHasHighBloodPressure
            )}
            
            {renderHealthQuestion(
              'Have you ever been diagnosed with cancer?',
              hasCancer,
              setHasCancer
            )}
            
            {renderHealthQuestion(
              'Are you currently a smoker?',
              isCurrentSmoker,
              setIsCurrentSmoker
            )}
          </View>
          
          <Text style={[styles.subtitle, { color: colors.text }]}>Health Conditions</Text>
          
          {healthConditions.length === 0 ? (
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No health conditions added yet. Add any pre-existing conditions that may affect your policy.
            </Text>
          ) : (
            healthConditions.map(renderConditionItem)
          )}
          
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary[500] }]}
            onPress={handleAddCondition}
          >
            <Plus size={20} color={colors.white} />
            <Text style={[styles.addButtonText, { color: colors.white }]}>
              Add Health Condition
            </Text>
          </TouchableOpacity>
        </>
      )}
      
      {isAddingCondition && renderConditionForm()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 14,
    marginBottom: 16,
  },
  questionsSection: {
    marginBottom: 16,
  },
  questionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionText: {
    flex: 1,
    fontSize: 14,
    marginRight: 8,
  },
  questionButtons: {
    flexDirection: 'row',
  },
  answerButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginLeft: 8,
  },
  answerText: {
    fontSize: 14,
    fontWeight: '500',
  },
  conditionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  conditionInfo: {
    flex: 1,
  },
  conditionName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  conditionSeverity: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  conditionDetails: {
    fontSize: 14,
  },
  conditionActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  formContainer: {
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default HealthQuestionnaire;
