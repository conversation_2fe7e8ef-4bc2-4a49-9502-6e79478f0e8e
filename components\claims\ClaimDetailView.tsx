import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Claim, ClaimDocument, CLAIM_TYPE_DISPLAY_NAMES } from '@/types/claim.types';
import { formatCurrency } from '@/utils/quoteCalculations';
import ClaimStatusBadge from './ClaimStatusBadge';
import ClaimStatusTimeline from './ClaimStatusTimeline';
import ClaimDocumentsSection from './ClaimDocumentsSection';
import { ChevronRight, Send, FileText, Share2 } from 'lucide-react-native';
import { showToast } from '@/utils/toast';

interface ClaimDetailViewProps {
  claim: Claim;
  onUpdateClaim: (id: string, updates: Partial<Claim>) => Promise<void>;
  onSubmitClaim: (id: string) => Promise<void>;
  onUploadDocument: (claimId: string, document: any) => Promise<void>;
  onViewDocument: (document: ClaimDocument) => void;
  onShareClaim: (claim: Claim) => void;
}

const ClaimDetailView: React.FC<ClaimDetailViewProps> = ({
  claim,
  onUpdateClaim,
  onSubmitClaim,
  onUploadDocument,
  onViewDocument,
  onShareClaim
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders, shadows } = createTheme(isDarkMode);

  // State
  const [newNote, setNewNote] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isAddingNote, setIsAddingNote] = useState<boolean>(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    contentContainer: {
      padding: spacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    reference: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
    },
    section: {
      marginBottom: spacing.lg,
    },
    sectionTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.md,
    },
    infoCard: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...shadows.sm,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.sm,
    },
    infoLabel: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      flex: 1,
    },
    infoValue: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      flex: 2,
      textAlign: 'right',
    },
    description: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      padding: spacing.md,
      marginBottom: spacing.md,
      ...shadows.sm,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginVertical: spacing.sm,
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    secondaryButton: {
      backgroundColor: colors.neutral[200],
    },
    secondaryButtonText: {
      color: colors.text,
    },
    disabledButton: {
      backgroundColor: colors.neutral[400],
    },
    notesSection: {
      marginTop: spacing.md,
    },
    noteItem: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginBottom: spacing.sm,
    },
    noteText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
    },
    noteInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.sm,
    },
    noteInput: {
      flex: 1,
      backgroundColor: colors.card,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginRight: spacing.sm,
    },
    sendButton: {
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
    },
    noNotesText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginVertical: spacing.md,
    },
  });

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle timeline action
  const handleTimelineAction = async (action: string) => {
    if (action === 'submit_claim') {
      handleSubmitClaim();
    }
  };

  // Handle submit claim
  const handleSubmitClaim = async () => {
    // Check if all required documents are uploaded
    const requiredDocuments = claim.documents.filter(doc => doc.required);
    const allRequiredDocumentsUploaded = requiredDocuments.every(doc => doc.status === 'verified');
    
    if (!allRequiredDocumentsUploaded) {
      showToast(
        'error',
        'Missing Documents',
        'Please upload all required documents before submitting',
        { visibilityTime: 3000 }
      );
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onSubmitClaim(claim.id);
      showToast(
        'success',
        'Claim Submitted',
        'Your claim has been submitted successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error submitting claim:', error);
      showToast(
        'error',
        'Submission Error',
        'Failed to submit claim. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle add note
  const handleAddNote = async () => {
    if (!newNote.trim()) {
      return;
    }
    
    setIsAddingNote(true);
    
    try {
      await onUpdateClaim(claim.id, {
        notes: [...(claim.notes || []), newNote.trim()]
      });
      
      setNewNote('');
      showToast(
        'success',
        'Note Added',
        'Your note has been added successfully',
        { visibilityTime: 3000 }
      );
    } catch (error) {
      console.error('Error adding note:', error);
      showToast(
        'error',
        'Error',
        'Failed to add note. Please try again.',
        { visibilityTime: 3000 }
      );
    } finally {
      setIsAddingNote(false);
    }
  };

  // Handle upload document
  const handleUploadDocument = async (document: any) => {
    try {
      await onUploadDocument(claim.id, document);
    } catch (error) {
      console.error('Error uploading document:', error);
      showToast(
        'error',
        'Upload Error',
        'Failed to upload document. Please try again.',
        { visibilityTime: 3000 }
      );
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.contentContainer}>
        <View style={styles.header}>
          <Text style={styles.reference}>{claim.reference}</Text>
          <ClaimStatusBadge status={claim.status} size="medium" />
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Claim Details</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Claim Type</Text>
              <Text style={styles.infoValue}>{CLAIM_TYPE_DISPLAY_NAMES[claim.type]}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Policy Number</Text>
              <Text style={styles.infoValue}>{claim.policyNumber}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date Submitted</Text>
              <Text style={styles.infoValue}>{formatDate(claim.date)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Incident Date</Text>
              <Text style={styles.infoValue}>{formatDate(claim.incidentDate)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Claim Amount</Text>
              <Text style={styles.infoValue}>{formatCurrency(claim.claimAmount, claim.currency)}</Text>
            </View>
            {claim.approvedAmount !== undefined && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Approved Amount</Text>
                <Text style={styles.infoValue}>{formatCurrency(claim.approvedAmount, claim.currency)}</Text>
              </View>
            )}
            {claim.assessorName && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Assessor</Text>
                <Text style={styles.infoValue}>{claim.assessorName}</Text>
              </View>
            )}
            {claim.assessmentDate && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Assessment Date</Text>
                <Text style={styles.infoValue}>{formatDate(claim.assessmentDate)}</Text>
              </View>
            )}
            {claim.paymentDate && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Payment Date</Text>
                <Text style={styles.infoValue}>{formatDate(claim.paymentDate)}</Text>
              </View>
            )}
            {claim.paymentReference && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Payment Reference</Text>
                <Text style={styles.infoValue}>{claim.paymentReference}</Text>
              </View>
            )}
          </View>
          
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{claim.description}</Text>
          
          {/* Actions based on claim status */}
          {claim.status === 'draft' && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                isSubmitting ? styles.disabledButton : {}
              ]}
              onPress={handleSubmitClaim}
              disabled={isSubmitting}
            >
              <FileText size={20} color={colors.white} />
              <Text style={styles.actionButtonText}>
                {isSubmitting ? 'Submitting...' : 'Submit Claim'}
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => onShareClaim(claim)}
          >
            <Share2 size={20} color={colors.text} />
            <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
              Share Claim Details
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Claim Timeline</Text>
          <ClaimStatusTimeline
            events={claim.timeline}
            onActionPress={handleTimelineAction}
          />
        </View>
        
        <ClaimDocumentsSection
          claim={claim}
          onUploadDocument={handleUploadDocument}
          onViewDocument={onViewDocument}
        />
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          
          {(!claim.notes || claim.notes.length === 0) ? (
            <Text style={styles.noNotesText}>No notes yet</Text>
          ) : (
            claim.notes.map((note, index) => (
              <View key={index} style={styles.noteItem}>
                <Text style={styles.noteText}>{note}</Text>
              </View>
            ))
          )}
          
          <View style={styles.noteInputContainer}>
            <TextInput
              style={styles.noteInput}
              placeholder="Add a note..."
              placeholderTextColor={colors.textSecondary}
              value={newNote}
              onChangeText={setNewNote}
              multiline
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!newNote.trim() || isAddingNote) ? styles.disabledButton : {}
              ]}
              onPress={handleAddNote}
              disabled={!newNote.trim() || isAddingNote}
            >
              <Send size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default ClaimDetailView;
