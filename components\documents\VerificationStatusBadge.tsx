import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Check, Clock, AlertCircle } from 'lucide-react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { DocumentStatus } from './types';

interface VerificationStatusBadgeProps {
  status: DocumentStatus;
  size?: 'small' | 'medium' | 'large';
}

const VerificationStatusBadge: React.FC<VerificationStatusBadgeProps> = ({
  status,
  size = 'medium',
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing } = createTheme(isDarkMode);

  console.log(`[VerificationStatusBadge] Rendering badge with status: ${status}, size: ${size}`);

  // Determine icon size based on the badge size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 12;
      case 'large':
        return 16;
      case 'medium':
      default:
        return 14;
    }
  };

  // Determine status color, icon, and text
  let statusColor, icon, text;
  const iconSize = getIconSize();

  switch (status) {
    case 'verified':
      statusColor = colors.success[500];
      icon = <Check size={iconSize} color={statusColor} />;
      text = 'Verified';
      break;
    case 'pending':
      statusColor = colors.warning[500];
      icon = <Clock size={iconSize} color={statusColor} />;
      text = 'Pending';
      break;
    case 'rejected':
      statusColor = colors.error[500];
      icon = <AlertCircle size={iconSize} color={statusColor} />;
      text = 'Rejected';
      break;
    default:
      statusColor = colors.textSecondary;
      icon = null;
      text = 'Unknown';
  }

  // Create styles with dynamic properties
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: size === 'small' ? spacing.xs : spacing.sm,
      paddingVertical: size === 'small' ? spacing.xxs : spacing.xs,
      borderRadius: 100, // Full rounded corners
      backgroundColor: `${statusColor}20`, // 20% opacity of the status color
    },
    text: {
      color: statusColor,
      fontSize: size === 'small' ? 10 : size === 'large' ? 14 : 12,
      fontWeight: '500',
      marginLeft: size === 'small' ? 2 : 4,
    },
  });

  return (
    <View style={styles.container}>
      {icon}
      <Text style={styles.text}>{text}</Text>
    </View>
  );
};

export default VerificationStatusBadge;
