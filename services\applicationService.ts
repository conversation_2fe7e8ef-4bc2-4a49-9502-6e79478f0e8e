import apiService from './api';
import { Application, ApplicationStatus, Document, DocumentStatus, PaymentMethod, PaymentStatus } from '@/store/applicationStore';
import { showToast } from '@/utils/toast';

// Application service for handling application-related API calls
// NOTE: This service is prepared for future backend integration
// Currently, the application store uses local storage and mock data instead
export const applicationService = {
  // Get all applications for the current user
  getAllApplications: async (): Promise<Application[]> => {
    try {
      const response = await apiService.policies.getUserPolicies();

      // Map API response to Application type
      const applications: Application[] = response.map((item: any) => ({
        id: item.id,
        quoteId: item.quote_id,
        type: item.policy_type,
        status: mapApiStatusToAppStatus(item.status),
        reference: item.reference_number,
        date: item.created_at.split('T')[0],
        premium: item.premium_amount,
        currency: item.currency,
        coverAmount: item.cover_amount,
        clientInfo: {
          firstName: item.client_info.first_name,
          lastName: item.client_info.last_name,
          email: item.client_info.email,
          phone: item.client_info.phone,
          address: item.client_info.address,
          idNumber: item.client_info.id_number,
        },
        documents: item.documents.map((doc: any) => ({
          id: doc.id,
          name: doc.name,
          type: doc.document_type,
          status: mapApiDocStatusToAppDocStatus(doc.status),
          date: doc.uploaded_at.split('T')[0],
          verificationDate: doc.verified_at ? doc.verified_at.split('T')[0] : undefined,
          rejectionReason: doc.rejection_reason,
          required: doc.required,
          documentId: doc.id,
        })),
        timeline: mapApiTimelineToAppTimeline(item.timeline),
        payment: {
          method: mapApiPaymentMethodToAppPaymentMethod(item.payment.method),
          status: mapApiPaymentStatusToAppPaymentStatus(item.payment.status),
          amount: item.payment.amount,
          currency: item.payment.currency,
          dueDate: item.payment.due_date,
          paidDate: item.payment.paid_date,
          reference: item.payment.reference,
          proofOfPaymentId: item.payment.proof_of_payment_id,
          bankDetails: item.payment.bank_details ? {
            accountName: item.payment.bank_details.account_name,
            accountNumber: item.payment.bank_details.account_number,
            bankName: item.payment.bank_details.bank_name,
            branchCode: item.payment.bank_details.branch_code,
            reference: item.payment.bank_details.reference,
          } : undefined,
          debitOrderDetails: item.payment.debit_order_details ? {
            accountHolder: item.payment.debit_order_details.account_holder,
            accountNumber: item.payment.debit_order_details.account_number,
            bankName: item.payment.debit_order_details.bank_name,
            accountType: item.payment.debit_order_details.account_type,
            debitDay: item.payment.debit_order_details.debit_day,
            reference: item.payment.debit_order_details.reference,
            authorized: item.payment.debit_order_details.authorized,
          } : undefined,
        },
        underwriting: item.underwriting ? {
          status: item.underwriting.status,
          assignedTo: item.underwriting.assigned_to,
          startDate: item.underwriting.start_date,
          completionDate: item.underwriting.completion_date,
          notes: item.underwriting.notes,
          decision: item.underwriting.decision,
          decisionReason: item.underwriting.decision_reason,
          modifiedTerms: item.underwriting.modified_terms,
        } : undefined,
        policyNumber: item.policy_number,
        policyIssueDate: item.policy_issue_date,
        policyDocumentId: item.policy_document_id,
        notes: item.notes,
        requiredActions: item.required_actions ? item.required_actions.map((action: any) => ({
          id: action.id,
          description: action.description,
          priority: action.priority,
          dueDate: action.due_date,
          completed: action.completed,
        })) : [],
      }));

      return applications;
    } catch (error) {
      console.error('Error fetching applications:', error);
      showToast(
        'error',
        'Error',
        'Failed to fetch applications. Please try again.',
        { visibilityTime: 4000 }
      );
      return [];
    }
  },

  // Get application by ID
  getApplicationById: async (id: string): Promise<Application | null> => {
    try {
      const response = await apiService.policies.getPolicyById(id);

      // Map API response to Application type
      const application: Application = {
        id: response.id,
        quoteId: response.quote_id,
        type: response.policy_type,
        status: mapApiStatusToAppStatus(response.status),
        reference: response.reference_number,
        date: response.created_at.split('T')[0],
        premium: response.premium_amount,
        currency: response.currency,
        coverAmount: response.cover_amount,
        clientInfo: {
          firstName: response.client_info.first_name,
          lastName: response.client_info.last_name,
          email: response.client_info.email,
          phone: response.client_info.phone,
          address: response.client_info.address,
          idNumber: response.client_info.id_number,
        },
        documents: response.documents.map((doc: any) => ({
          id: doc.id,
          name: doc.name,
          type: doc.document_type,
          status: mapApiDocStatusToAppDocStatus(doc.status),
          date: doc.uploaded_at.split('T')[0],
          verificationDate: doc.verified_at ? doc.verified_at.split('T')[0] : undefined,
          rejectionReason: doc.rejection_reason,
          required: doc.required,
          documentId: doc.id,
        })),
        timeline: mapApiTimelineToAppTimeline(response.timeline),
        payment: {
          method: mapApiPaymentMethodToAppPaymentMethod(response.payment.method),
          status: mapApiPaymentStatusToAppPaymentStatus(response.payment.status),
          amount: response.payment.amount,
          currency: response.payment.currency,
          dueDate: response.payment.due_date,
          paidDate: response.payment.paid_date,
          reference: response.payment.reference,
          proofOfPaymentId: response.payment.proof_of_payment_id,
          bankDetails: response.payment.bank_details ? {
            accountName: response.payment.bank_details.account_name,
            accountNumber: response.payment.bank_details.account_number,
            bankName: response.payment.bank_details.bank_name,
            branchCode: response.payment.bank_details.branch_code,
            reference: response.payment.bank_details.reference,
          } : undefined,
          debitOrderDetails: response.payment.debit_order_details ? {
            accountHolder: response.payment.debit_order_details.account_holder,
            accountNumber: response.payment.debit_order_details.account_number,
            bankName: response.payment.debit_order_details.bank_name,
            accountType: response.payment.debit_order_details.account_type,
            debitDay: response.payment.debit_order_details.debit_day,
            reference: response.payment.debit_order_details.reference,
            authorized: response.payment.debit_order_details.authorized,
          } : undefined,
        },
        underwriting: response.underwriting ? {
          status: response.underwriting.status,
          assignedTo: response.underwriting.assigned_to,
          startDate: response.underwriting.start_date,
          completionDate: response.underwriting.completion_date,
          notes: response.underwriting.notes,
          decision: response.underwriting.decision,
          decisionReason: response.underwriting.decision_reason,
          modifiedTerms: response.underwriting.modified_terms,
        } : undefined,
        policyNumber: response.policy_number,
        policyIssueDate: response.policy_issue_date,
        policyDocumentId: response.policy_document_id,
        notes: response.notes,
        requiredActions: response.required_actions ? response.required_actions.map((action: any) => ({
          id: action.id,
          description: action.description,
          priority: action.priority,
          dueDate: action.due_date,
          completed: action.completed,
        })) : [],
      };

      return application;
    } catch (error) {
      console.error(`Error fetching application with ID ${id}:`, error);
      showToast(
        'error',
        'Error',
        'Failed to fetch application details. Please try again.',
        { visibilityTime: 4000 }
      );
      return null;
    }
  },
};

// Helper functions to map API response to application types
function mapApiStatusToAppStatus(apiStatus: string): ApplicationStatus {
  const statusMap: Record<string, ApplicationStatus> = {
    'quote_accepted': 'quote_accepted',
    'submitted': 'submitted',
    'payment_pending': 'payment_pending',
    'payment_verified': 'payment_verified',
    'underwriting': 'underwriting',
    'additional_info': 'additional_info',
    'approved': 'approved',
    'approved_with_terms': 'approved_with_terms',
    'policy_issued': 'policy_issued',
    'rejected': 'rejected',
  };

  return statusMap[apiStatus] || 'submitted';
}

function mapApiDocStatusToAppDocStatus(apiStatus: string): DocumentStatus {
  const statusMap: Record<string, DocumentStatus> = {
    'pending': 'pending',
    'verified': 'verified',
    'rejected': 'rejected',
  };

  return statusMap[apiStatus] || 'pending';
}

function mapApiPaymentMethodToAppPaymentMethod(apiMethod: string): PaymentMethod {
  const methodMap: Record<string, PaymentMethod> = {
    'eft': 'eft',
    'direct_debit': 'direct_debit',
    'none': 'none',
  };

  return methodMap[apiMethod] || 'none';
}

function mapApiPaymentStatusToAppPaymentStatus(apiStatus: string): PaymentStatus {
  const statusMap: Record<string, PaymentStatus> = {
    'not_started': 'not_started',
    'pending': 'pending',
    'verified': 'verified',
    'failed': 'failed',
  };

  return statusMap[apiStatus] || 'not_started';
}

function mapApiTimelineToAppTimeline(apiTimeline: any[]) {
  return apiTimeline.map((event: any) => ({
    id: event.id,
    date: event.date,
    title: event.title,
    description: event.description,
    status: event.status,
    icon: event.icon,
    actions: event.actions ? event.actions.map((action: any) => ({
      label: action.label,
      action: action.action,
    })) : undefined,
  }));
}

export default applicationService;
