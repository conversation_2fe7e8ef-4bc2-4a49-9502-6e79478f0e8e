// Payment types and interfaces

import { PaymentMethod, PaymentStatus } from '@/store/applicationStore';

// Payment status for history and tracking
export type PaymentHistoryStatus = 
  | 'pending'      // Payment is pending verification
  | 'processing'   // Payment is being processed
  | 'completed'    // Payment is completed
  | 'failed'       // Payment failed
  | 'refunded'     // Payment was refunded
  | 'cancelled';   // Payment was cancelled

// Payment receipt status
export type ReceiptStatus = 
  | 'available'    // Receipt is available
  | 'pending'      // Receipt is being generated
  | 'unavailable'; // Receipt is not available

// Payment type
export type PaymentType = 
  | 'premium'      // Regular premium payment
  | 'deposit'      // Initial deposit
  | 'renewal'      // Policy renewal payment
  | 'other';       // Other payment types

// Payment category for filtering
export type PaymentCategory = 
  | 'policy'       // Policy-related payment
  | 'claim'        // Claim-related payment
  | 'application'  // Application-related payment
  | 'other';       // Other payment category

// Payment receipt
export interface PaymentReceipt {
  id: string;
  paymentId: string;
  issueDate: string;
  documentId?: string;
  downloadUrl?: string;
  status: ReceiptStatus;
}

// Payment history item
export interface PaymentHistoryItem {
  id: string;
  reference: string;
  amount: number;
  currency: string;
  date: string;
  dueDate?: string;
  status: PaymentHistoryStatus;
  method: PaymentMethod;
  description: string;
  type: PaymentType;
  category: PaymentCategory;
  policyId?: string;
  policyNumber?: string;
  claimId?: string;
  claimReference?: string;
  applicationId?: string;
  applicationReference?: string;
  receiptId?: string;
  proofOfPaymentId?: string;
  verificationDate?: string;
}

// Payment filters
export interface PaymentFilters {
  status?: PaymentHistoryStatus[];
  method?: PaymentMethod[];
  type?: PaymentType[];
  category?: PaymentCategory[];
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  searchQuery?: string;
}

// Payment verification input
export interface PaymentVerificationInput {
  paymentId: string;
  documentId: string;
  notes?: string;
}

// Payment method input
export interface PaymentMethodInput {
  type: PaymentMethod;
  bankAccount?: {
    accountHolder: string;
    accountNumber: string;
    bankName: string;
    accountType: string;
    branchCode?: string;
  };
  debitOrderDetails?: {
    debitDay: number;
    reference: string;
    authorized: boolean;
    startDate?: string;
    endDate?: string;
  };
  isDefault?: boolean;
}
