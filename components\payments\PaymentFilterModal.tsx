import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Platform,
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  X,
  Check,
  Calendar,
  RefreshCw,
  Filter,
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { 
  PaymentHistoryStatus, 
  PaymentType, 
  PaymentCategory,
  PaymentFilters 
} from '@/types/payment.types';
import { PaymentMethod } from '@/store/applicationStore';

interface PaymentFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: PaymentFilters) => void;
  initialFilters?: PaymentFilters;
}

export default function PaymentFilterModal({
  visible,
  onClose,
  onApplyFilters,
  initialFilters = {}
}: PaymentFilterModalProps) {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  // State for filters
  const [filters, setFilters] = useState<PaymentFilters>(initialFilters);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Reset filters when modal opens with new initialFilters
  useEffect(() => {
    if (visible) {
      setFilters(initialFilters);
    }
  }, [visible, initialFilters]);

  // Payment status options
  const statusOptions: { value: PaymentHistoryStatus; label: string }[] = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'completed', label: 'Completed' },
    { value: 'failed', label: 'Failed' },
    { value: 'refunded', label: 'Refunded' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  // Payment method options
  const methodOptions: { value: PaymentMethod; label: string }[] = [
    { value: 'eft', label: 'EFT' },
    { value: 'direct_debit', label: 'Direct Debit' },
  ];

  // Payment type options
  const typeOptions: { value: PaymentType; label: string }[] = [
    { value: 'premium', label: 'Premium' },
    { value: 'deposit', label: 'Deposit' },
    { value: 'renewal', label: 'Renewal' },
    { value: 'other', label: 'Other' },
  ];

  // Payment category options
  const categoryOptions: { value: PaymentCategory; label: string }[] = [
    { value: 'policy', label: 'Policy' },
    { value: 'claim', label: 'Claim' },
    { value: 'application', label: 'Application' },
    { value: 'other', label: 'Other' },
  ];

  // Toggle status filter
  const toggleStatusFilter = (status: PaymentHistoryStatus) => {
    setFilters(prev => {
      const currentStatuses = prev.status || [];
      if (currentStatuses.includes(status)) {
        return {
          ...prev,
          status: currentStatuses.filter(s => s !== status)
        };
      } else {
        return {
          ...prev,
          status: [...currentStatuses, status]
        };
      }
    });
  };

  // Toggle method filter
  const toggleMethodFilter = (method: PaymentMethod) => {
    setFilters(prev => {
      const currentMethods = prev.method || [];
      if (currentMethods.includes(method)) {
        return {
          ...prev,
          method: currentMethods.filter(m => m !== method)
        };
      } else {
        return {
          ...prev,
          method: [...currentMethods, method]
        };
      }
    });
  };

  // Toggle type filter
  const toggleTypeFilter = (type: PaymentType) => {
    setFilters(prev => {
      const currentTypes = prev.type || [];
      if (currentTypes.includes(type)) {
        return {
          ...prev,
          type: currentTypes.filter(t => t !== type)
        };
      } else {
        return {
          ...prev,
          type: [...currentTypes, type]
        };
      }
    });
  };

  // Toggle category filter
  const toggleCategoryFilter = (category: PaymentCategory) => {
    setFilters(prev => {
      const currentCategories = prev.category || [];
      if (currentCategories.includes(category)) {
        return {
          ...prev,
          category: currentCategories.filter(c => c !== category)
        };
      } else {
        return {
          ...prev,
          category: [...currentCategories, category]
        };
      }
    });
  };

  // Handle date changes
  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setFilters(prev => ({
        ...prev,
        dateRange: {
          ...prev.dateRange,
          start: selectedDate
        }
      }));
    }
  };

  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setFilters(prev => ({
        ...prev,
        dateRange: {
          ...prev.dateRange,
          end: selectedDate
        }
      }));
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({});
  };

  // Apply filters and close modal
  const applyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  // Format date for display
  const formatDate = (date?: Date) => {
    if (!date) return 'Select Date';
    return format(date, 'dd MMM yyyy');
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[styles.modalContent, { backgroundColor: colors.card }]}
          entering={FadeInDown.duration(300)}
        >
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Filter Payments</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            {/* Status Filter */}
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Status</Text>
              <View style={styles.optionsContainer}>
                {statusOptions.map(option => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.filterOption,
                      filters.status?.includes(option.value) && {
                        backgroundColor: colors.primary[500] + '20',
                        borderColor: colors.primary[500],
                      },
                    ]}
                    onPress={() => toggleStatusFilter(option.value)}
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        { color: colors.text },
                        filters.status?.includes(option.value) && {
                          color: colors.primary[500],
                          fontWeight: 'bold',
                        },
                      ]}
                    >
                      {option.label}
                    </Text>
                    {filters.status?.includes(option.value) && (
                      <Check size={16} color={colors.primary[500]} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Payment Method Filter */}
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Payment Method</Text>
              <View style={styles.optionsContainer}>
                {methodOptions.map(option => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.filterOption,
                      filters.method?.includes(option.value) && {
                        backgroundColor: colors.primary[500] + '20',
                        borderColor: colors.primary[500],
                      },
                    ]}
                    onPress={() => toggleMethodFilter(option.value)}
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        { color: colors.text },
                        filters.method?.includes(option.value) && {
                          color: colors.primary[500],
                          fontWeight: 'bold',
                        },
                      ]}
                    >
                      {option.label}
                    </Text>
                    {filters.method?.includes(option.value) && (
                      <Check size={16} color={colors.primary[500]} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Payment Type Filter */}
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Payment Type</Text>
              <View style={styles.optionsContainer}>
                {typeOptions.map(option => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.filterOption,
                      filters.type?.includes(option.value) && {
                        backgroundColor: colors.primary[500] + '20',
                        borderColor: colors.primary[500],
                      },
                    ]}
                    onPress={() => toggleTypeFilter(option.value)}
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        { color: colors.text },
                        filters.type?.includes(option.value) && {
                          color: colors.primary[500],
                          fontWeight: 'bold',
                        },
                      ]}
                    >
                      {option.label}
                    </Text>
                    {filters.type?.includes(option.value) && (
                      <Check size={16} color={colors.primary[500]} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Payment Category Filter */}
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Payment Category</Text>
              <View style={styles.optionsContainer}>
                {categoryOptions.map(option => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.filterOption,
                      filters.category?.includes(option.value) && {
                        backgroundColor: colors.primary[500] + '20',
                        borderColor: colors.primary[500],
                      },
                    ]}
                    onPress={() => toggleCategoryFilter(option.value)}
                  >
                    <Text
                      style={[
                        styles.filterOptionText,
                        { color: colors.text },
                        filters.category?.includes(option.value) && {
                          color: colors.primary[500],
                          fontWeight: 'bold',
                        },
                      ]}
                    >
                      {option.label}
                    </Text>
                    {filters.category?.includes(option.value) && (
                      <Check size={16} color={colors.primary[500]} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Date Range Filter */}
            <View style={styles.filterSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Date Range</Text>
              <View style={styles.dateRangeContainer}>
                <TouchableOpacity
                  style={[styles.datePickerButton, { borderColor: colors.border }]}
                  onPress={() => setShowStartDatePicker(true)}
                >
                  <Calendar size={16} color={colors.textSecondary} />
                  <Text style={[styles.datePickerText, { color: colors.text }]}>
                    {filters.dateRange?.start ? formatDate(filters.dateRange.start) : 'Start Date'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.datePickerButton, { borderColor: colors.border }]}
                  onPress={() => setShowEndDatePicker(true)}
                >
                  <Calendar size={16} color={colors.textSecondary} />
                  <Text style={[styles.datePickerText, { color: colors.text }]}>
                    {filters.dateRange?.end ? formatDate(filters.dateRange.end) : 'End Date'}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Date Pickers */}
              {(showStartDatePicker || showEndDatePicker) && Platform.OS === 'android' && (
                <DateTimePicker
                  value={
                    showStartDatePicker
                      ? filters.dateRange?.start || new Date()
                      : filters.dateRange?.end || new Date()
                  }
                  mode="date"
                  display="default"
                  onChange={showStartDatePicker ? handleStartDateChange : handleEndDateChange}
                />
              )}

              {Platform.OS === 'ios' && (
                <>
                  {showStartDatePicker && (
                    <View style={styles.iosDatePickerContainer}>
                      <DateTimePicker
                        value={filters.dateRange?.start || new Date()}
                        mode="date"
                        display="spinner"
                        onChange={handleStartDateChange}
                      />
                      <TouchableOpacity
                        style={styles.iosDatePickerDoneButton}
                        onPress={() => setShowStartDatePicker(false)}
                      >
                        <Text style={styles.iosDatePickerDoneText}>Done</Text>
                      </TouchableOpacity>
                    </View>
                  )}

                  {showEndDatePicker && (
                    <View style={styles.iosDatePickerContainer}>
                      <DateTimePicker
                        value={filters.dateRange?.end || new Date()}
                        mode="date"
                        display="spinner"
                        onChange={handleEndDateChange}
                      />
                      <TouchableOpacity
                        style={styles.iosDatePickerDoneButton}
                        onPress={() => setShowEndDatePicker(false)}
                      >
                        <Text style={styles.iosDatePickerDoneText}>Done</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </>
              )}
            </View>
          </ScrollView>

          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.resetButton, { borderColor: colors.border }]}
              onPress={resetFilters}
            >
              <RefreshCw size={16} color={colors.textSecondary} />
              <Text style={[styles.resetButtonText, { color: colors.textSecondary }]}>
                Reset All
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.applyButton, { backgroundColor: colors.primary[500] }]}
              onPress={applyFilters}
            >
              <Filter size={16} color={colors.white} />
              <Text style={[styles.applyButtonText, { color: colors.white }]}>
                Apply Filters
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
  },
  filterSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
    minWidth: 100,
  },
  filterOptionText: {
    fontSize: 14,
    marginRight: 4,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderRadius: 8,
    width: '48%',
  },
  datePickerText: {
    fontSize: 14,
    marginLeft: 8,
  },
  iosDatePickerContainer: {
    marginTop: 8,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    overflow: 'hidden',
  },
  iosDatePickerDoneButton: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#e0e0e0',
  },
  iosDatePickerDoneText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  applyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
  },
  applyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});
