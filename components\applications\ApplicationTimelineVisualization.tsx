import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  Check, Clock, AlertCircle, FileText, CreditCard,
  FileCheck, ClipboardList, Shield, ChevronRight,
  Calendar, Info, ChevronDown, ExternalLink, 
  RefreshCw, CheckCircle2, AlertTriangle, MoreHorizontal,
  ArrowRight, Hourglass, FileSearch, FileWarning, User,
  Filter, Search, X, Calendar as CalendarIcon
} from 'lucide-react-native';
import Animated, { FadeIn, FadeInDown, FadeInRight, FadeInUp } from 'react-native-reanimated';
import { router } from 'expo-router';
import { TimelineStep } from '@/types/application.types';

interface ApplicationTimelineVisualizationProps {
  steps: TimelineStep[];
  onFilterChange?: (filter: string) => void;
  onStepPress?: (step: TimelineStep) => void;
}

const ApplicationTimelineVisualization: React.FC<ApplicationTimelineVisualizationProps> = ({
  steps,
  onFilterChange,
  onStepPress,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [expandedStep, setExpandedStep] = useState<string | null>(null);

  // Filter steps based on the selected filter and search query
  const filteredSteps = steps.filter(step => {
    // Filter by status
    if (activeFilter !== 'all') {
      if (activeFilter === 'active' && 
          (step.status !== 'current' && step.status !== 'in_progress')) {
        return false;
      }
      if (activeFilter === 'completed' && step.status !== 'completed') {
        return false;
      }
      if (activeFilter === 'upcoming' && step.status !== 'upcoming') {
        return false;
      }
      if (activeFilter === 'issues' && 
          (step.status !== 'rejected' && step.status !== 'warning')) {
        return false;
      }
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        step.title.toLowerCase().includes(query) ||
        step.description.toLowerCase().includes(query) ||
        (step.details && step.details.toLowerCase().includes(query))
      );
    }

    return true;
  });

  // Handle filter change
  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter);
    if (onFilterChange) {
      onFilterChange(filter);
    }
  };

  // Handle step press
  const handleStepPress = (step: TimelineStep) => {
    setExpandedStep(expandedStep === step.id ? null : step.id);
    if (onStepPress) {
      onStepPress(step);
    }
  };

  // Get status color
  const getStatusColor = (status: TimelineStep['status']) => {
    switch (status) {
      case 'completed':
        return colors.success[500];
      case 'current':
        return colors.primary[500];
      case 'in_progress':
        return colors.info[500];
      case 'upcoming':
        return colors.neutral[400];
      case 'rejected':
        return colors.error[500];
      case 'warning':
        return colors.warning[500];
      case 'info':
        return colors.info[500];
      default:
        return colors.neutral[400];
    }
  };

  // Get status icon
  const getStatusIcon = (status: TimelineStep['status'], icon?: string, size: number = 20) => {
    // First check if there's a custom icon
    if (icon) {
      switch (icon) {
        case 'check-circle':
          return <CheckCircle2 size={size} color={colors.white} />;
        case 'file-text':
          return <FileText size={size} color={colors.white} />;
        case 'credit-card':
          return <CreditCard size={size} color={colors.white} />;
        case 'file-check':
          return <FileCheck size={size} color={colors.white} />;
        case 'clipboard':
          return <ClipboardList size={size} color={colors.white} />;
        case 'shield':
          return <Shield size={size} color={colors.white} />;
        case 'alert-circle':
          return <AlertCircle size={size} color={colors.white} />;
        case 'calendar':
          return <CalendarIcon size={size} color={colors.white} />;
        case 'info':
          return <Info size={size} color={colors.white} />;
        case 'refresh':
          return <RefreshCw size={size} color={colors.white} />;
        case 'hourglass':
          return <Hourglass size={size} color={colors.white} />;
        case 'file-search':
          return <FileSearch size={size} color={colors.white} />;
        case 'file-warning':
          return <FileWarning size={size} color={colors.white} />;
        case 'alert-triangle':
          return <AlertTriangle size={size} color={colors.white} />;
      }
    }

    // Fall back to status-based icons
    switch (status) {
      case 'completed':
        return <Check size={size} color={colors.white} />;
      case 'current':
        return <Clock size={size} color={colors.white} />;
      case 'in_progress':
        return <RefreshCw size={size} color={colors.white} />;
      case 'upcoming':
        return <Clock size={size} color={colors.white} />;
      case 'rejected':
        return <AlertCircle size={size} color={colors.white} />;
      case 'warning':
        return <AlertTriangle size={size} color={colors.white} />;
      case 'info':
        return <Info size={size} color={colors.white} />;
      default:
        return null;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.card,
    },
    title: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.lg,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    filterContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.sm,
    },
    filterButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.xs,
      borderRadius: borders.radius.full,
      marginRight: spacing.xs,
      marginBottom: spacing.xs,
      backgroundColor: colors.neutral[200],
    },
    filterButtonActive: {
      backgroundColor: colors.primary[500],
    },
    filterButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    filterButtonTextActive: {
      color: colors.white,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: isDarkMode ? colors.neutral[800] : colors.neutral[100],
      borderRadius: borders.radius.md,
      paddingHorizontal: spacing.sm,
      marginTop: spacing.sm,
    },
    searchInput: {
      flex: 1,
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.md,
      color: colors.text,
      paddingVertical: spacing.sm,
      marginLeft: spacing.xs,
    },
    clearButton: {
      padding: spacing.xs,
    },
    timelineContainer: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
    },
    timelineItem: {
      marginBottom: spacing.md,
      backgroundColor: colors.card,
      borderRadius: borders.radius.lg,
      overflow: 'hidden',
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    timelineHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.md,
    },
    statusIndicator: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
    },
    timelineContent: {
      flex: 1,
    },
    timelineTitle: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.xs,
    },
    timelineDescription: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
    },
    timelineDate: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textTertiary,
      marginTop: spacing.xs,
    },
    expandedContent: {
      padding: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    detailsText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      marginBottom: spacing.md,
    },
    metadataContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: spacing.md,
    },
    metadataItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: spacing.md,
      marginBottom: spacing.xs,
    },
    metadataText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
      marginLeft: spacing.xs,
    },
    actionsContainer: {
      marginTop: spacing.sm,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.primary[500],
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borders.radius.md,
      marginBottom: spacing.xs,
    },
    actionButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.white,
      marginRight: spacing.xs,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.xl,
    },
    emptyText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Application Timeline</Text>
        
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeFilter === 'all' && styles.filterButtonActive,
            ]}
            onPress={() => handleFilterChange('all')}
          >
            <Text
              style={[
                styles.filterButtonText,
                activeFilter === 'all' && styles.filterButtonTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeFilter === 'active' && styles.filterButtonActive,
            ]}
            onPress={() => handleFilterChange('active')}
          >
            <Text
              style={[
                styles.filterButtonText,
                activeFilter === 'active' && styles.filterButtonTextActive,
              ]}
            >
              Active
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeFilter === 'completed' && styles.filterButtonActive,
            ]}
            onPress={() => handleFilterChange('completed')}
          >
            <Text
              style={[
                styles.filterButtonText,
                activeFilter === 'completed' && styles.filterButtonTextActive,
              ]}
            >
              Completed
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeFilter === 'upcoming' && styles.filterButtonActive,
            ]}
            onPress={() => handleFilterChange('upcoming')}
          >
            <Text
              style={[
                styles.filterButtonText,
                activeFilter === 'upcoming' && styles.filterButtonTextActive,
              ]}
            >
              Upcoming
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              activeFilter === 'issues' && styles.filterButtonActive,
            ]}
            onPress={() => handleFilterChange('issues')}
          >
            <Text
              style={[
                styles.filterButtonText,
                activeFilter === 'issues' && styles.filterButtonTextActive,
              ]}
            >
              Issues
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView>
        <View style={styles.timelineContainer}>
          {filteredSteps.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No timeline events match your filter</Text>
            </View>
          ) : (
            filteredSteps.map((step, index) => (
              <Animated.View
                key={step.id}
                style={styles.timelineItem}
                entering={FadeInDown.delay(index * 100).springify()}
              >
                <TouchableOpacity
                  style={styles.timelineHeader}
                  onPress={() => handleStepPress(step)}
                >
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: getStatusColor(step.status) },
                    ]}
                  >
                    {getStatusIcon(step.status, step.icon)}
                  </View>
                  
                  <View style={styles.timelineContent}>
                    <Text style={styles.timelineTitle}>{step.title}</Text>
                    <Text style={styles.timelineDescription}>{step.description}</Text>
                    {step.date && (
                      <Text style={styles.timelineDate}>{formatDate(step.date)}</Text>
                    )}
                  </View>
                  
                  <ChevronRight
                    size={20}
                    color={colors.textSecondary}
                    style={{
                      transform: [{ rotate: expandedStep === step.id ? '90deg' : '0deg' }],
                    }}
                  />
                </TouchableOpacity>
                
                {expandedStep === step.id && (
                  <Animated.View
                    style={styles.expandedContent}
                    entering={FadeInUp.duration(300)}
                  >
                    {step.details && (
                      <Text style={styles.detailsText}>{step.details}</Text>
                    )}
                    
                    <View style={styles.metadataContainer}>
                      {step.assignedTo && (
                        <View style={styles.metadataItem}>
                          <User size={16} color={colors.textSecondary} />
                          <Text style={styles.metadataText}>{step.assignedTo}</Text>
                        </View>
                      )}
                      
                      {step.estimatedCompletionDate && (
                        <View style={styles.metadataItem}>
                          <Calendar size={16} color={colors.textSecondary} />
                          <Text style={styles.metadataText}>
                            Est. completion: {formatDate(step.estimatedCompletionDate)}
                          </Text>
                        </View>
                      )}
                    </View>
                    
                    {step.actions && step.actions.length > 0 && (
                      <View style={styles.actionsContainer}>
                        {step.actions.map((action, actionIndex) => (
                          <TouchableOpacity
                            key={`${step.id}-action-${actionIndex}`}
                            style={styles.actionButton}
                            onPress={() => {
                              if (action.route) {
                                if (action.params) {
                                  router.push({
                                    pathname: action.route,
                                    params: action.params
                                  });
                                } else {
                                  router.push(action.route);
                                }
                              }
                            }}
                          >
                            <Text style={styles.actionButtonText}>{action.label}</Text>
                            <ArrowRight size={16} color={colors.white} />
                          </TouchableOpacity>
                        ))}
                      </View>
                    )}
                  </Animated.View>
                )}
              </Animated.View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default ApplicationTimelineVisualization;
