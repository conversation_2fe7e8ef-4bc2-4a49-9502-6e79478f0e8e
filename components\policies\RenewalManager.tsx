import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import usePolicyStore from '@/store/policyStore';
import renewalService, { RenewalNotification } from '@/services/renewalService';
import RenewalNotificationComponent from './RenewalNotification';

const RenewalManager: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { colors } = createTheme(isDarkMode);
  const { policies } = usePolicyStore();
  const [renewalNotifications, setRenewalNotifications] = useState<RenewalNotification[]>([]);

  // Check for policy renewals on mount and when policies change
  useEffect(() => {
    const checkRenewals = async () => {
      // Check all policies for renewal notifications
      await renewalService.checkPoliciesForRenewal(policies);
      
      // Get all renewal notifications
      const notifications: RenewalNotification[] = [];
      
      for (const policy of policies) {
        const policyNotifications = await renewalService.getRenewalNotifications(policy);
        notifications.push(...policyNotifications);
      }
      
      // Sort notifications by days until renewal (ascending)
      notifications.sort((a, b) => a.daysUntilRenewal - b.daysUntilRenewal);
      
      // Update state
      setRenewalNotifications(notifications);
    };
    
    checkRenewals();
  }, [policies]);

  // Handle dismissing a notification
  const handleDismissNotification = async (notification: RenewalNotification) => {
    // Dismiss notification
    await renewalService.dismissRenewalNotification(
      policies.find(p => p.id === notification.policyId)!,
      notification.daysUntilRenewal
    );
    
    // Update state
    setRenewalNotifications(prevNotifications =>
      prevNotifications.map(n =>
        n.policyId === notification.policyId && n.daysUntilRenewal === notification.daysUntilRenewal
          ? { ...n, dismissed: true }
          : n
      )
    );
  };

  // Create styles
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
    },
  });

  // Filter out dismissed notifications
  const activeNotifications = renewalNotifications.filter(n => !n.dismissed);
  
  if (activeNotifications.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {activeNotifications.map((notification, index) => (
        <RenewalNotificationComponent
          key={`${notification.policyId}-${notification.daysUntilRenewal}`}
          notification={notification}
          onDismiss={() => handleDismissNotification(notification)}
        />
      ))}
    </View>
  );
};

export default RenewalManager;
