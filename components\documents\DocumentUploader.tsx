import React, { useState, useCallback, useRef, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  AppState,
  AppStateStatus,
  InteractionManager
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import { Upload, Camera, Image as ImageIcon, File } from 'lucide-react-native';
import { Document, DocumentCategory } from './types';
import { UploadManager, UploadResult } from '@/utils/UploadManager';
import DocumentTypeSelector from './DocumentTypeSelector';
import { Alert } from 'react-native';
import Toast from 'react-native-toast-message';
import { useDocumentUpload } from '@/context/DocumentUploadContext';

interface DocumentUploaderProps {
  onDocumentUploaded: (document: Document) => void;
  preselectedDocumentType?: string;
}

// Use memo to prevent unnecessary re-renders
const DocumentUploader: React.FC<DocumentUploaderProps> = memo(({
  onDocumentUploaded,
  preselectedDocumentType
}) => {
  const { isDarkMode } = useTheme();
  // Memoize theme creation to prevent unnecessary recalculations
  const theme = React.useMemo(() => createTheme(isDarkMode), [isDarkMode]);
  const { colors, spacing, typography, borders } = theme;
  const { uploadDocument } = useDocumentUpload();

  // Use refs instead of state where possible to prevent rerenders
  const isUploadingRef = useRef(false);
  const [isUploading, setIsUploading] = useState(false);

  // Use refs for document type and category to reduce state updates
  const selectedDocumentTypeRef = useRef<string | null>(preselectedDocumentType || null);
  const selectedDocumentCategoryRef = useRef<DocumentCategory | null>(null);

  // Use state only for UI updates
  const [selectedDocumentType, setSelectedDocumentType] = useState<string | null>(preselectedDocumentType || null);
  const [selectedDocumentCategory, setSelectedDocumentCategory] = useState<DocumentCategory | null>(null);

  // Track app state to reset if app goes to background during upload
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Set up app state change listener
  React.useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      // When app comes back to foreground, reset uploading state
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        isUploadingRef.current
      ) {
        isUploadingRef.current = false;
        setIsUploading(false);
      }

      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // Handle preselected document type if provided
  React.useEffect(() => {
    if (preselectedDocumentType) {
      // Find the document type in the list
      const documentTypes = [
        { name: 'National ID/Omang/Passport', category: 'ID' as DocumentCategory },
        { name: 'Proof of Address', category: 'Proof of Address' as DocumentCategory },
        { name: 'Driver\'s License', category: 'ID' as DocumentCategory },
        { name: 'Vehicle Registration Book', category: 'ID' as DocumentCategory },
        { name: 'Title Deed/Land Board Certificate', category: 'ID' as DocumentCategory },
        { name: 'Building Valuation Report', category: 'Insurance' as DocumentCategory },
        { name: 'Completed Inventory Form', category: 'Insurance' as DocumentCategory },
        { name: 'Beneficiary Nomination Form', category: 'Insurance' as DocumentCategory },
        { name: 'Other Document', category: 'Other' as DocumentCategory },
      ];

      const docType = documentTypes.find(dt => dt.name === preselectedDocumentType);

      if (docType) {
        // Set the document type and category
        setSelectedDocumentType(docType.name);
        setSelectedDocumentCategory(docType.category);
      }
    }
  }, [preselectedDocumentType]);

  // Handle document type selection - memoize with useCallback
  const handleDocumentTypeSelect = useCallback((documentType: string, documentCategory: DocumentCategory) => {
    // Update both refs and state in a single batch
    selectedDocumentTypeRef.current = documentType;
    selectedDocumentCategoryRef.current = documentCategory;

    // Use a single batch update for state changes to reduce re-renders
    const batchStateUpdates = () => {
      setSelectedDocumentType(documentType);
      setSelectedDocumentCategory(documentCategory);
    };

    // Execute batch updates
    batchStateUpdates();
  }, []);

  // Forward declare functions to avoid reference errors
  const pickImage = useCallback((useCamera: boolean) => {
    // Prevent multiple uploads
    if (isUploading || isUploadingRef.current || UploadManager.isUploading) return;

    // Set local uploading state
    isUploadingRef.current = true;
    setIsUploading(true);

    // Use the UploadManager to handle the image picking process
    UploadManager.pickImage(
      useCamera,
      selectedDocumentTypeRef.current || selectedDocumentType!,
      selectedDocumentCategoryRef.current || selectedDocumentCategory!,
      (result: UploadResult) => {
        // Handle the result
        if (result.success && result.document) {
          // Reset uploading flag immediately to prevent further uploads
          isUploadingRef.current = false;

          // Use InteractionManager to ensure we don't block the UI thread
          InteractionManager.runAfterInteractions(() => {
            try {
              // Upload the document using our context
              // We know result.document is defined here because we're in the success branch
              const uploadedDoc = uploadDocument(result.document!);

              // Also notify the parent component
              onDocumentUploaded(uploadedDoc);

              // Reset the document type - use refs to avoid unnecessary state updates
              selectedDocumentTypeRef.current = null;
              selectedDocumentCategoryRef.current = null;

              // Batch state updates to reduce re-renders
              requestAnimationFrame(() => {
                setIsUploading(false);
                setSelectedDocumentType(null);
                setSelectedDocumentCategory(null);
              });
            } catch (error) {
              console.error('[DocumentUploader] Error processing document:', error);

              // Reset state
              requestAnimationFrame(() => {
                setIsUploading(false);
              });

              // Show error toast
              Toast.show({
                type: 'error',
                text1: 'Upload Error',
                text2: 'Failed to process document. Please try again.',
                visibilityTime: 4000,
                topOffset: 60,
              });
            }
          });
        } else {
          // Handle error
          console.error('[DocumentUploader] Error picking image:', result.error);

          // Only show toast if there's an actual error (not user cancellation)
          if (result.error && result.error !== 'User canceled') {
            Toast.show({
              type: 'error',
              text1: 'Upload Error',
              text2: result.error || 'Failed to upload document. Please try again.',
              visibilityTime: 3000,
            });
          }

          // Reset state
          isUploadingRef.current = false;
          requestAnimationFrame(() => {
            setIsUploading(false);
          });
        }
      }
    );

  }, [uploadDocument, onDocumentUploaded]);

  // Pick document file
  const pickDocument = useCallback(() => {
    // Prevent multiple uploads
    if (isUploading || isUploadingRef.current || UploadManager.isUploading) return;

    // Set local uploading state
    isUploadingRef.current = true;
    setIsUploading(true);

    // Use the UploadManager to handle the document picking process
    UploadManager.pickDocument(
      selectedDocumentTypeRef.current || selectedDocumentType!,
      selectedDocumentCategoryRef.current || selectedDocumentCategory!,
      (result: UploadResult) => {
        // Handle the result
        if (result.success && result.document) {
          // Reset uploading flag immediately to prevent further uploads
          isUploadingRef.current = false;

          // Use InteractionManager to ensure we don't block the UI thread
          InteractionManager.runAfterInteractions(() => {
            try {
              // Upload the document using our context
              // We know result.document is defined here because we're in the success branch
              const uploadedDoc = uploadDocument(result.document!);

              // Also notify the parent component
              onDocumentUploaded(uploadedDoc);

              // Reset the document type - use refs to avoid unnecessary state updates
              selectedDocumentTypeRef.current = null;
              selectedDocumentCategoryRef.current = null;

              // Batch state updates to reduce re-renders
              requestAnimationFrame(() => {
                setIsUploading(false);
                setSelectedDocumentType(null);
                setSelectedDocumentCategory(null);
              });
            } catch (error) {
              console.error('[DocumentUploader] Error processing document:', error);

              // Reset state
              requestAnimationFrame(() => {
                setIsUploading(false);
              });

              // Show error toast
              Toast.show({
                type: 'error',
                text1: 'Upload Error',
                text2: 'Failed to process document. Please try again.',
                visibilityTime: 4000,
                topOffset: 60,
              });
            }
          });
        } else {
          // Handle error
          console.error('[DocumentUploader] Error picking document:', result.error);

          // Only show toast if there's an actual error (not user cancellation)
          if (result.error && result.error !== 'User canceled') {
            Toast.show({
              type: 'error',
              text1: 'Upload Error',
              text2: result.error || 'Failed to upload document. Please try again.',
              visibilityTime: 3000,
            });
          }

          // Reset state
          isUploadingRef.current = false;
          requestAnimationFrame(() => {
            setIsUploading(false);
          });
        }
      }
    );
  }, [uploadDocument, onDocumentUploaded, selectedDocumentType, selectedDocumentCategory, isUploading]);

  // Select upload method (camera, gallery, or document) - memoize with useCallback
  const selectUploadMethod = useCallback(() => {
    // Prevent multiple uploads or selection when already uploading
    if (!selectedDocumentType || !selectedDocumentCategory || isUploading || isUploadingRef.current) {
      // Use toast directly
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please select a document type first or wait for current upload to complete',
        visibilityTime: 4000,
        topOffset: 60,
      });
      return;
    }

    Alert.alert(
      'Upload Method',
      'How would you like to upload your document?',
      [
        {
          text: 'Take Photo',
          onPress: () => {
            // Use setTimeout to prevent UI issues
            setTimeout(() => {
              pickImage(true);
            }, 300);
          },
        },
        {
          text: 'Choose from Gallery',
          onPress: () => {
            // Use setTimeout to prevent UI issues
            setTimeout(() => {
              pickImage(false);
            }, 300);
          },
        },
        {
          text: 'Select Document File',
          onPress: () => {
            // Use setTimeout to prevent UI issues
            setTimeout(() => {
              pickDocument();
            }, 300);
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  }, [pickImage, pickDocument, selectedDocumentType, selectedDocumentCategory, isUploading, isUploadingRef]);



  // Memoize styles to prevent recreation on every render
  const styles = React.useMemo(() => StyleSheet.create({
    container: {
      marginBottom: spacing.lg,
    },
    uploadButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary[500],
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
    },
    uploadButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.white,
      marginLeft: spacing.sm,
    },
    uploadOptionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: spacing.md,
    },
    uploadOption: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginHorizontal: spacing.xs,
      borderWidth: 1,
      borderColor: colors.border,
    },
    uploadOptionIcon: {
      marginBottom: spacing.sm,
    },
    uploadOptionText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
      textAlign: 'center',
    },
  }), [spacing, colors, borders, typography]);

  // Function to render upload options based on platform
  const renderUploadOptions = () => {
    if (Platform.OS === 'ios') {
      return (
        <View style={styles.uploadOptionsContainer}>
          <TouchableOpacity
            style={styles.uploadOption}
            onPress={() => {
              // Use setTimeout to prevent UI issues
              setTimeout(() => {
                pickImage(true);
              }, 300);
            }}
            disabled={!selectedDocumentType || isUploading || isUploadingRef.current}
          >
            <Camera
              size={24}
              color={colors.primary[500]}
              style={styles.uploadOptionIcon}
            />
            <Text style={styles.uploadOptionText}>Camera</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.uploadOption}
            onPress={() => {
              // Use setTimeout to prevent UI issues
              setTimeout(() => {
                pickImage(false);
              }, 300);
            }}
            disabled={!selectedDocumentType || isUploading || isUploadingRef.current}
          >
            <ImageIcon
              size={24}
              color={colors.primary[500]}
              style={styles.uploadOptionIcon}
            />
            <Text style={styles.uploadOptionText}>Gallery</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.uploadOption}
            onPress={() => {
              // Use setTimeout to prevent UI issues
              setTimeout(() => {
                pickDocument();
              }, 300);
            }}
            disabled={!selectedDocumentType || isUploading || isUploadingRef.current}
          >
            <File
              size={24}
              color={colors.primary[500]}
              style={styles.uploadOptionIcon}
            />
            <Text style={styles.uploadOptionText}>Document</Text>
          </TouchableOpacity>
        </View>
      );
    } else {
      return (
        <TouchableOpacity
          style={[
            styles.uploadButton,
            (!selectedDocumentType || isUploading || isUploadingRef.current) && { opacity: 0.7 }
          ]}
          onPress={() => {
            // Use setTimeout to prevent UI issues
            setTimeout(() => {
              selectUploadMethod();
            }, 300);
          }}
          disabled={!selectedDocumentType || isUploading || isUploadingRef.current}
        >
          {isUploading ? (
            <ActivityIndicator color={colors.white} />
          ) : (
            <>
              <Upload size={20} color={colors.white} />
              <Text style={styles.uploadButtonText}>Upload Document</Text>
            </>
          )}
        </TouchableOpacity>
      );
    }
  };

  return (
    <View style={styles.container}>
      <DocumentTypeSelector onSelect={handleDocumentTypeSelect} />
      {renderUploadOptions()}
    </View>
  );
});

export default DocumentUploader;
