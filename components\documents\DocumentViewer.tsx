import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Modal,
  ActivityIndicator,
  Share,
  InteractionManager,
  Platform,
  ScrollView
} from 'react-native';
import { createTheme } from '@/constants/theme';
import { useTheme } from '@/context/ThemeContext';
import {
  X,
  Download,
  Share2,
  ZoomIn,
  ZoomOut,
  FileText as FilePdf,
  FileImage,
  File,
  FileSpreadsheet,
  Presentation,
  ExternalLink,
  Tag,
  Calendar,
  AlertCircle,
  Link,
  Info,
  FileText
} from 'lucide-react-native';
import { Document } from './types';
import * as Sharing from 'expo-sharing';
import Toast from 'react-native-toast-message';
import * as FileSystem from 'expo-file-system';
import { getUniqueFilename } from '@/utils/documentUtils';
import { FileSharing } from '@/utils/FileSharing';
import useDocumentStore from '@/store/documentStore';

interface DocumentViewerProps {
  document: Document;
  visible: boolean;
  onClose: () => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  document,
  visible,
  onClose,
}) => {
  const { isDarkMode } = useTheme();
  const { colors, spacing, typography, borders } = createTheme(isDarkMode);

  const [isLoading, setIsLoading] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);

  console.log(`[DocumentViewer] Rendering document: ${document.name}, type: ${document.fileType}`);
  console.log('[DocumentViewer] Document URI:', document.uri);
  console.log('[DocumentViewer] Document fileUrl:', document.fileUrl);
  console.log('[DocumentViewer] Document ID:', document.id);
  console.log('[DocumentViewer] Document isImage:', document.isImage);

  // Handle document download
  const handleDownload = async () => {
    try {
      setIsLoading(true);
      console.log('[DocumentViewer] Downloading document:', document.name);

      // Get the document URI
      const uri = document.fileUrl || document.uri;
      if (!uri) {
        throw new Error('No file URI available');
      }

      // Create a directory for downloads if it doesn't exist
      const downloadDir = `${FileSystem.documentDirectory}downloads/`;
      const dirInfo = await FileSystem.getInfoAsync(downloadDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(downloadDir, { intermediates: true });
      }

      // Generate a unique filename using our utility function
      const finalFilename = getUniqueFilename(document);
      const destinationUri = `${downloadDir}${finalFilename}`;

      // Copy the file to the downloads directory
      await FileSystem.copyAsync({
        from: uri,
        to: destinationUri
      });

      console.log('[DocumentViewer] File downloaded to:', destinationUri);

      // Show success toast with more details
      InteractionManager.runAfterInteractions(() => {
        Toast.show({
          type: 'success',
          text1: 'Download Complete',
          text2: `${document.name} saved to Downloads folder`,
          visibilityTime: 3000,
          topOffset: 60,
          props: {
            onPress: () => {
              Toast.hide();
            }
          }
        });
      });

      // On Android, we can notify the user that the file is available in the downloads folder
      if (Platform.OS === 'android') {
        try {
          // Get a content URI for the file to make it accessible to other apps
          const contentUri = await FileSystem.getContentUriAsync(destinationUri);
          console.log('[DocumentViewer] Content URI for downloaded file:', contentUri);
        } catch (contentUriError) {
          console.error('[DocumentViewer] Error getting content URI:', contentUriError);
          // This is not a critical error, so we don't need to show a toast
        }
      }
    } catch (error) {
      console.error('[DocumentViewer] Error downloading document:', error);

      // Show error toast with more details
      InteractionManager.runAfterInteractions(() => {
        Toast.show({
          type: 'error',
          text1: 'Download Failed',
          text2: 'Could not save the document. Please try again.',
          visibilityTime: 4000,
          topOffset: 60,
        });
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle document sharing
  const handleShare = async () => {
    try {
      setIsLoading(true);
      console.log('[DocumentViewer] Sharing document:', document.name);

      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        // Use toast directly
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Sharing is not available on this device',
          visibilityTime: 4000,
          topOffset: 60,
        });
        return;
      }

      // In a real app, you would share the actual file
      // For this example, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000));

      await Share.share({
        title: document.name,
        message: `Check out this document: ${document.name}`,
        url: document.fileUrl,
      });
    } catch (error) {
      console.error('[DocumentViewer] Error sharing document:', error);
      // Use toast directly
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to share document. Please try again.',
        visibilityTime: 4000,
        topOffset: 60,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle zoom in
  const handleZoomIn = () => {
    console.log('[DocumentViewer] Zooming in');
    setZoomLevel(prevZoom => Math.min(prevZoom + 0.25, 3));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    console.log('[DocumentViewer] Zooming out');
    setZoomLevel(prevZoom => Math.max(prevZoom - 0.25, 0.5));
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.lg,
      color: colors.text,
      flex: 1,
      marginHorizontal: spacing.md,
    },
    closeButton: {
      padding: spacing.xs,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
    },
    imageContainer: {
      flex: 1,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.md,
      minHeight: 400, // Ensure minimum height
    },
    image: {
      width: '100%',
      height: 350, // Reduced height to make room for the button
      transform: [{ scale: zoomLevel }],
      marginBottom: spacing.md,
    },
    pdfContainer: {
      flex: 1,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
    },
    pdfPlaceholder: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: spacing.lg,
    },
    pdfText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginTop: spacing.md,
      marginBottom: spacing.md,
      textAlign: 'center',
    },

    zoomControls: {
      position: 'absolute',
      bottom: spacing.lg,
      right: spacing.lg,
      flexDirection: 'row',
      backgroundColor: colors.card,
      borderRadius: borders.radius.full,
      padding: spacing.xs,
      borderWidth: 1,
      borderColor: colors.border,
    },
    zoomButton: {
      padding: spacing.xs,
      marginHorizontal: 2, // Fixed value instead of spacing.xxs
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      padding: spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    footerButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.sm,
    },
    footerButtonText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.primary[500],
      marginLeft: spacing.xs,
    },
    loadingContainer: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
    },

    viewExternalButton: {
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borders.radius.md,
      marginTop: spacing.md,
      alignSelf: 'center',
      width: '80%',
    },
    viewExternalButtonText: {
      color: colors.white,
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.md,
      textAlign: 'center',
    },
    metadataContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
      width: '100%',
    },
    metadataTitle: {
      fontFamily: typography.fonts.bold,
      fontSize: typography.sizes.md,
      color: colors.text,
      marginBottom: spacing.sm,
    },
    metadataRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    metadataLabel: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.textSecondary,
      width: 100,
    },
    metadataValue: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.sm,
      color: colors.text,
      flex: 1,
    },
    tagContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.xs,
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${colors.primary[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      marginRight: spacing.xs,
      marginBottom: spacing.xs,
    },
    tagText: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.primary[500],
      marginLeft: 2,
    },
    relatedDocsContainer: {
      backgroundColor: colors.card,
      borderRadius: borders.radius.md,
      padding: spacing.md,
      marginTop: spacing.md,
      width: '100%',
    },
    relatedDocItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    relatedDocIcon: {
      marginRight: spacing.sm,
    },
    relatedDocInfo: {
      flex: 1,
    },
    relatedDocName: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.sm,
      color: colors.text,
    },
    relatedDocType: {
      fontFamily: typography.fonts.regular,
      fontSize: typography.sizes.xs,
      color: colors.textSecondary,
    },
    importanceHigh: {
      backgroundColor: `${colors.error[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    importanceMedium: {
      backgroundColor: `${colors.warning[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    importanceLow: {
      backgroundColor: `${colors.success[500]}10`,
      borderRadius: borders.radius.full,
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs / 2,
      flexDirection: 'row',
      alignItems: 'center',
    },
    importanceText: {
      fontFamily: typography.fonts.medium,
      fontSize: typography.sizes.xs,
      marginLeft: 2,
    },
    importanceTextHigh: {
      color: colors.error[500],
    },
    importanceTextMedium: {
      color: colors.warning[500],
    },
    importanceTextLow: {
      color: colors.success[500],
    },
  });

  // State to track if image loading failed
  const [imageLoadFailed, setImageLoadFailed] = useState(false);

  // Determine the appropriate icon based on file type
  const getFileIcon = () => {
    if (!document.fileType) {
      return <File size={64} color={colors.primary[500]} />;
    }

    switch (document.fileType) {
      case 'pdf':
        return <FilePdf size={64} color={colors.primary[500]} />;
      case 'image':
        return <FileImage size={64} color={colors.primary[500]} />;
      case 'excel':
        return <FileSpreadsheet size={64} color={colors.primary[500]} />;
      case 'powerpoint':
        return <Presentation size={64} color={colors.primary[500]} />;
      case 'doc':
        return <FilePdf size={64} color={colors.primary[500]} />;
      default:
        return <File size={64} color={colors.primary[500]} />;
    }
  };

  // Handle image load error
  const handleImageError = useCallback((error: any) => {
    console.error('[DocumentViewer] Image loading error:', error.nativeEvent?.error || 'Unknown error');
    console.log('[DocumentViewer] Image URI that failed:', document.uri);
    console.log('[DocumentViewer] Image fileUrl that failed:', document.fileUrl);

    // Use InteractionManager to ensure UI is responsive
    InteractionManager.runAfterInteractions(() => {
      setImageLoadFailed(true);

      // Use toast directly
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load image. The file may be corrupted or inaccessible.',
        visibilityTime: 4000,
        topOffset: 60,
      });
    });
  }, [document.uri, document.fileUrl]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle} numberOfLines={1} ellipsizeMode="middle">
            {document.name}
          </Text>
        </View>

        <View style={styles.content}>
          {/* For images, try to display them regardless of fileType if uri is available */}
          {!imageLoadFailed && (document.fileType === 'image' || document.isImage === true) && (document.fileUrl || document.uri) ? (
            <View style={styles.imageContainer}>
              <Image
                source={{
                  uri: document.fileUrl || document.uri
                }}
                style={[styles.image, { transform: [{ scale: zoomLevel }] }]}
                resizeMode="contain"
                onError={handleImageError}
                onLoad={() => {
                  console.log('[DocumentViewer] Image loaded successfully');
                  setImageLoadFailed(false);
                }}
                // Force image to reload with a key based on the URI to prevent caching issues
                key={`image-${document.id}-${Date.now()}`}
              />
              {/* Fallback option if image doesn't display properly */}
              <TouchableOpacity
                style={styles.viewExternalButton}
                onPress={async () => {
                  try {
                    console.log('[DocumentViewer] Opening image in external viewer, document ID:', document.id);

                    // First check if we have a direct URI we can use
                    const uri = document.fileUrl || document.uri || '';

                    if (uri) {
                      console.log('[DocumentViewer] Using direct URI to open image:', uri);
                      await FileSharing.openFile(uri, 'image/jpeg');

                      Toast.show({
                        type: 'success',
                        text1: 'Success',
                        text2: 'Opening image in external viewer',
                        visibilityTime: 2000,
                        topOffset: 60,
                      });
                      return;
                    }

                    // If no direct URI, try to use the document store
                    if (document.id) {
                      // First check if the document exists in the store
                      const { getDocumentById, viewDocument } = useDocumentStore.getState();
                      const docInStore = getDocumentById(document.id);

                      if (!docInStore) {
                        throw new Error(`Document with ID ${document.id} not found`);
                      }

                      await viewDocument(document.id);
                    } else {
                      throw new Error('No document URI or ID available');
                    }
                  } catch (error) {
                    console.error('[DocumentViewer] Error opening image in external viewer:', error);

                    // Show error toast
                    Toast.show({
                      type: 'error',
                      text1: 'Error',
                      text2: 'Failed to open image. Please try again.',
                      visibilityTime: 4000,
                      topOffset: 60,
                    });
                  }
                }}
              >
                <Text style={styles.viewExternalButtonText}>
                  Open in External Viewer
                </Text>
              </TouchableOpacity>
            </View>
          ) : document.fileType === 'pdf' ? (
            <View style={styles.pdfContainer}>
              {document.fileUrl || document.uri ? (
                <View style={styles.pdfPlaceholder}>
                  <FilePdf size={64} color={colors.primary[500]} />
                  <Text style={styles.pdfText}>
                    PDF Document{'\n'}
                    {document.fileName || document.name}
                  </Text>
                  <TouchableOpacity
                    style={styles.viewExternalButton}
                    onPress={async () => {
                      try {
                        console.log('[DocumentViewer] Opening PDF in external viewer, document ID:', document.id);

                        // First check if we have a direct URI we can use
                        const uri = document.fileUrl || document.uri || '';

                        if (uri) {
                          console.log('[DocumentViewer] Using direct URI to open document:', uri);
                          await FileSharing.openFile(uri, 'application/pdf');

                          Toast.show({
                            type: 'success',
                            text1: 'Success',
                            text2: 'Opening PDF in external viewer',
                            visibilityTime: 2000,
                            topOffset: 60,
                          });
                          return;
                        }

                        // If no direct URI, try to use the document store
                        if (document.id) {
                          // First check if the document exists in the store
                          const { getDocumentById, viewDocument } = useDocumentStore.getState();
                          const docInStore = getDocumentById(document.id);

                          if (!docInStore) {
                            throw new Error(`Document with ID ${document.id} not found`);
                          }

                          await viewDocument(document.id);
                        } else {
                          throw new Error('No document URI or ID available');
                        }
                      } catch (error) {
                        console.error('[DocumentViewer] Error opening PDF in external viewer:', error);
                        Toast.show({
                          type: 'error',
                          text1: 'Error',
                          text2: 'Failed to open PDF. Please try again.',
                          visibilityTime: 4000,
                          topOffset: 60,
                        });
                      }
                    }}
                  >
                    <Text style={styles.viewExternalButtonText}>
                      Open in External App
                    </Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.pdfPlaceholder}>
                  <FilePdf size={64} color={colors.primary[500]} />
                  <Text style={styles.pdfText}>
                    PDF Viewer{'\n'}
                    (No PDF file available to display)
                  </Text>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.pdfContainer}>
              <View style={styles.pdfPlaceholder}>
                {getFileIcon()}
                <Text style={styles.pdfText}>
                  Document Preview{'\n'}
                  {document.fileName || document.name}
                </Text>
                {document.fileUrl || document.uri ? (
                  <TouchableOpacity
                    style={styles.viewExternalButton}
                    onPress={async () => {
                      try {
                        console.log('[DocumentViewer] Opening document in external viewer, document ID:', document.id);

                        // Determine MIME type based on file type
                        let mimeType = 'application/octet-stream'; // Default

                        if (document.fileType === 'doc') {
                          mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                        } else if (document.fileType === 'excel') {
                          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                        } else if (document.fileType === 'powerpoint') {
                          mimeType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
                        } else if (document.fileType === 'text') {
                          mimeType = 'text/plain';
                        } else if (document.fileType === 'rtf') {
                          mimeType = 'text/rtf';
                        }

                        // First check if we have a direct URI we can use
                        const uri = document.fileUrl || document.uri || '';

                        if (uri) {
                          console.log('[DocumentViewer] Using direct URI to open document:', uri);
                          await FileSharing.openFile(uri, mimeType);

                          Toast.show({
                            type: 'success',
                            text1: 'Success',
                            text2: 'Opening document in external viewer',
                            visibilityTime: 2000,
                            topOffset: 60,
                          });
                          return;
                        }

                        // If no direct URI, try to use the document store
                        if (document.id) {
                          // First check if the document exists in the store
                          const { getDocumentById, viewDocument } = useDocumentStore.getState();
                          const docInStore = getDocumentById(document.id);

                          if (!docInStore) {
                            throw new Error(`Document with ID ${document.id} not found`);
                          }

                          await viewDocument(document.id);
                        } else {
                          throw new Error('No document URI or ID available');
                        }
                      } catch (error) {
                        console.error('[DocumentViewer] Error opening document in external viewer:', error);

                        // Show error toast
                        Toast.show({
                          type: 'error',
                          text1: 'Error',
                          text2: 'Failed to open document. Please try again.',
                          visibilityTime: 4000,
                          topOffset: 60,
                        });
                      }
                    }}
                  >
                    <Text style={styles.viewExternalButtonText}>
                      Open in External Viewer
                    </Text>
                  </TouchableOpacity>
                ) : null}
              </View>
            </View>
          )}

          <View style={styles.zoomControls}>
            <TouchableOpacity style={styles.zoomButton} onPress={handleZoomIn}>
              <ZoomIn size={24} color={colors.primary[500]} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.zoomButton} onPress={handleZoomOut}>
              <ZoomOut size={24} color={colors.primary[500]} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Document Metadata */}
        <ScrollView style={{ width: '100%', maxHeight: 300 }}>
          <View style={styles.metadataContainer}>
            <Text style={styles.metadataTitle}>Document Information</Text>

            <View style={styles.metadataRow}>
              <Text style={styles.metadataLabel}>Type:</Text>
              <Text style={styles.metadataValue}>
                {'documentType' in document && typeof document.documentType === 'string' ?
                  document.documentType.split('_').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') :
                  document.type}
              </Text>
            </View>

            <View style={styles.metadataRow}>
              <Text style={styles.metadataLabel}>Date:</Text>
              <Text style={styles.metadataValue}>{document.date}</Text>
            </View>

            {'issueDate' in document && typeof document.issueDate === 'string' && (
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Issue Date:</Text>
                <Text style={styles.metadataValue}>{document.issueDate}</Text>
              </View>
            )}

            {'expiryDate' in document && typeof document.expiryDate === 'string' && (
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Expiry Date:</Text>
                <Text style={styles.metadataValue}>{document.expiryDate}</Text>
              </View>
            )}

            {'policyNumber' in document && typeof document.policyNumber === 'string' && (
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Policy:</Text>
                <Text style={styles.metadataValue}>{document.policyNumber}</Text>
              </View>
            )}

            {'importance' in document && typeof document.importance === 'string' && (
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Importance:</Text>
                <View
                  style={
                    document.importance === 'high'
                      ? styles.importanceHigh
                      : document.importance === 'medium'
                        ? styles.importanceMedium
                        : styles.importanceLow
                  }
                >
                  <AlertCircle
                    size={12}
                    color={
                      document.importance === 'high'
                        ? colors.error[500]
                        : document.importance === 'medium'
                          ? colors.warning[500]
                          : colors.success[500]
                    }
                  />
                  <Text
                    style={[
                      styles.importanceText,
                      document.importance === 'high'
                        ? styles.importanceTextHigh
                        : document.importance === 'medium'
                          ? styles.importanceTextMedium
                          : styles.importanceTextLow
                    ]}
                  >
                    {document.importance.charAt(0).toUpperCase() + document.importance.slice(1)}
                  </Text>
                </View>
              </View>
            )}

            {document.tags && document.tags.length > 0 && (
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Tags:</Text>
                <View style={styles.tagContainer}>
                  {document.tags.map((tag, index) => (
                    <View key={index} style={styles.tag}>
                      <Tag size={12} color={colors.primary[500]} />
                      <Text style={styles.tagText}>{tag}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>

          {/* Related Documents - Only show if this is a PolicyDocument with related docs */}
          {'relatedDocumentIds' in document &&
           Array.isArray(document.relatedDocumentIds) &&
           document.relatedDocumentIds.length > 0 && (
            <View style={styles.relatedDocsContainer}>
              <Text style={styles.metadataTitle}>Related Documents</Text>

              {/* This is a placeholder since we don't have actual related documents to display */}
              {/* In a real app, you would fetch the related documents and display them */}
              <View style={styles.relatedDocItem}>
                <FilePdf size={20} color={colors.primary[500]} style={styles.relatedDocIcon} />
                <View style={styles.relatedDocInfo}>
                  <Text style={styles.relatedDocName}>Policy Schedule</Text>
                  <Text style={styles.relatedDocType}>policy_schedule</Text>
                </View>
                <TouchableOpacity>
                  <ExternalLink size={20} color={colors.primary[500]} />
                </TouchableOpacity>
              </View>

              <View style={styles.relatedDocItem}>
                <FileText size={20} color={colors.primary[500]} style={styles.relatedDocIcon} />
                <View style={styles.relatedDocInfo}>
                  <Text style={styles.relatedDocName}>Terms and Conditions</Text>
                  <Text style={styles.relatedDocType}>terms_and_conditions</Text>
                </View>
                <TouchableOpacity>
                  <ExternalLink size={20} color={colors.primary[500]} />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.footerButton} onPress={handleDownload}>
            <Download size={20} color={colors.primary[500]} />
            <Text style={styles.footerButtonText}>Download</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.footerButton} onPress={handleShare}>
            <Share2 size={20} color={colors.primary[500]} />
            <Text style={styles.footerButtonText}>Share</Text>
          </TouchableOpacity>
        </View>

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.white} />
          </View>
        )}
      </View>
    </Modal>
  );
};

export default DocumentViewer;
